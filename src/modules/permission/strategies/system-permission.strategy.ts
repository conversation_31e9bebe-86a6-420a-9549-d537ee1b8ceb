import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';

import { PermissionStrategy } from './permission-strategy.interface';
import {
  CreatePermissionDto,
  UpdatePermissionDto,
  PermissionDto,
  AssignPermissionToRoleDto,
  PermissionQueryDto,
} from '../dto/permission.dto';

import { PublicPrismaService } from '@/core/database/prisma/public-prisma.service';

@Injectable()
export class SystemPermissionStrategy implements PermissionStrategy {
  constructor(private readonly prisma: PublicPrismaService) {}

  async getPermissions(query?: PermissionQueryDto): Promise<PermissionDto[]> {
    const where: any = {};

    if (query?.name) {
      where.name = {
        contains: query.name,
        mode: 'insensitive',
      };
    }

    if (query?.code) {
      where.code = {
        contains: query.code,
        mode: 'insensitive',
      };
    }

    if (query?.status !== undefined) {
      where.status = query.status;
    }

    const permissions = await this.prisma.systemPermission.findMany({
      where,
      orderBy: [{ id: 'asc' }],
    });

    return permissions.map(this.transformPermissionData);
  }

  async getPermissionById(id: number): Promise<PermissionDto | null> {
    const permission = await this.prisma.systemPermission.findUnique({
      where: { id },
    });

    return permission ? this.transformPermissionData(permission) : null;
  }

  async createPermission(createPermissionDto: CreatePermissionDto): Promise<PermissionDto> {
    // 检查权限码是否已存在
    const existingPermission = await this.prisma.systemPermission.findUnique({
      where: { code: createPermissionDto.code },
    });

    if (existingPermission) {
      throw new BadRequestException(`权限码 "${createPermissionDto.code}" 已存在`);
    }

    const permission = await this.prisma.systemPermission.create({
      data: {
        name: createPermissionDto.name,
        code: createPermissionDto.code,
        description: createPermissionDto.description,
        status: createPermissionDto.status ?? 1,
      },
    });

    return this.transformPermissionData(permission);
  }

  async updatePermission(
    id: number,
    updatePermissionDto: UpdatePermissionDto,
  ): Promise<PermissionDto> {
    // 检查权限是否存在
    const existingPermission = await this.prisma.systemPermission.findUnique({
      where: { id },
    });

    if (!existingPermission) {
      throw new NotFoundException(`权限ID ${id} 不存在`);
    }

    // 如果更新权限码，检查是否与其他权限冲突
    if (updatePermissionDto.code && updatePermissionDto.code !== existingPermission.code) {
      const codeExists = await this.checkPermissionCodeExists(updatePermissionDto.code, id);
      if (codeExists) {
        throw new BadRequestException(`权限码 "${updatePermissionDto.code}" 已存在`);
      }
    }

    const permission = await this.prisma.systemPermission.update({
      where: { id },
      data: {
        ...(updatePermissionDto.name && { name: updatePermissionDto.name }),
        ...(updatePermissionDto.code && { code: updatePermissionDto.code }),
        ...(updatePermissionDto.description !== undefined && {
          description: updatePermissionDto.description,
        }),
        ...(updatePermissionDto.status !== undefined && { status: updatePermissionDto.status }),
        updatedAt: new Date(),
      },
    });

    return this.transformPermissionData(permission);
  }

  async deletePermission(id: number): Promise<boolean> {
    // 检查权限是否存在
    const existingPermission = await this.prisma.systemPermission.findUnique({
      where: { id },
    });

    if (!existingPermission) {
      throw new NotFoundException(`权限ID ${id} 不存在`);
    }

    // 检查权限是否被角色使用
    const rolePermissionCount = await this.prisma.systemRolePermission.count({
      where: { permissionId: id },
    });

    if (rolePermissionCount > 0) {
      throw new BadRequestException('该权限正在被角色使用，无法删除');
    }

    await this.prisma.systemPermission.delete({
      where: { id },
    });

    return true;
  }

  async checkPermissionCodeExists(code: string, excludeId?: number): Promise<boolean> {
    const where: any = { code };

    if (excludeId) {
      where.id = { not: excludeId };
    }

    const permission = await this.prisma.systemPermission.findFirst({
      where,
    });

    return !!permission;
  }

  async assignPermissionsToRole(assignDto: AssignPermissionToRoleDto): Promise<boolean> {
    const { roleId, permissionIds } = assignDto;

    // 检查角色是否存在
    const role = await this.prisma.systemRole.findUnique({
      where: { id: roleId },
    });

    if (!role) {
      throw new NotFoundException(`角色ID ${roleId} 不存在`);
    }

    // 检查权限是否存在
    if (permissionIds.length > 0) {
      const permissions = await this.prisma.systemPermission.findMany({
        where: { id: { in: permissionIds } },
      });

      if (permissions.length !== permissionIds.length) {
        throw new BadRequestException('部分权限ID不存在');
      }
    }

    // 删除角色现有的权限
    await this.prisma.systemRolePermission.deleteMany({
      where: { roleId },
    });

    // 分配新的权限
    if (permissionIds.length > 0) {
      const rolePermissions = permissionIds.map(permissionId => ({
        roleId,
        permissionId,
      }));

      await this.prisma.systemRolePermission.createMany({
        data: rolePermissions,
      });
    }

    return true;
  }

  async getRolePermissions(roleId: string): Promise<PermissionDto[]> {
    // 检查角色是否存在
    const role = await this.prisma.systemRole.findUnique({
      where: { id: roleId },
    });

    if (!role) {
      throw new NotFoundException(`角色ID ${roleId} 不存在`);
    }

    const rolePermissions = await this.prisma.systemRolePermission.findMany({
      where: { roleId },
      include: {
        permission: true,
      },
    });

    return rolePermissions.map(rp => this.transformPermissionData(rp.permission));
  }

  async removePermissionFromRole(roleId: string, permissionId: number): Promise<boolean> {
    // 检查角色权限关联是否存在
    const rolePermission = await this.prisma.systemRolePermission.findFirst({
      where: { roleId, permissionId },
    });

    if (!rolePermission) {
      throw new NotFoundException('角色权限关联不存在');
    }

    await this.prisma.systemRolePermission.delete({
      where: { id: rolePermission.id },
    });

    return true;
  }

  private transformPermissionData(permission: any): PermissionDto {
    return {
      id: permission.id,
      name: permission.name,
      code: permission.code,
      description: permission.description,
      status: permission.status,
      createdAt: permission.createdAt,
      updatedAt: permission.updatedAt,
    };
  }
}
