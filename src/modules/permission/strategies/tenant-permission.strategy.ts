import { Injectable, BadRequestException, NotFoundException, Inject } from '@nestjs/common';

import { PermissionStrategy } from './permission-strategy.interface';
import {
  CreatePermissionDto,
  UpdatePermissionDto,
  PermissionDto,
  AssignPermissionToRoleDto,
  PermissionQueryDto,
} from '../dto/permission.dto';

import {
  TENANT_PRISMA_SERVICE,
  TenantPrismaService,
} from '@/core/database/prisma/tenant-prisma.service';

@Injectable()
export class TenantPermissionStrategy implements PermissionStrategy {
  constructor(@Inject(TENANT_PRISMA_SERVICE) private readonly prisma: TenantPrismaService) {}

  async getPermissions(query?: PermissionQueryDto, tenantId?: number): Promise<PermissionDto[]> {
    if (!tenantId) {
      throw new BadRequestException('租户ID不能为空');
    }

    const where: any = { tenantId };

    if (query?.name) {
      where.name = {
        contains: query.name,
        mode: 'insensitive',
      };
    }

    if (query?.code) {
      where.code = {
        contains: query.code,
        mode: 'insensitive',
      };
    }

    if (query?.status !== undefined) {
      where.status = query.status;
    }

    const permissions = await this.prisma.permission.findMany({
      where,
      orderBy: [{ id: 'asc' }],
    });

    return permissions.map(this.transformPermissionData);
  }

  async getPermissionById(id: number, tenantId?: number): Promise<PermissionDto | null> {
    if (!tenantId) {
      throw new BadRequestException('租户ID不能为空');
    }

    const permission = await this.prisma.permission.findFirst({
      where: { id, tenantId },
    });

    return permission ? this.transformPermissionData(permission) : null;
  }

  async createPermission(
    createPermissionDto: CreatePermissionDto,
    tenantId?: number,
  ): Promise<PermissionDto> {
    if (!tenantId) {
      throw new BadRequestException('租户ID不能为空');
    }

    // 检查权限码在该租户下是否已存在
    const existingPermission = await this.prisma.permission.findFirst({
      where: {
        code: createPermissionDto.code,
        tenantId,
      },
    });

    if (existingPermission) {
      throw new BadRequestException(`权限码 "${createPermissionDto.code}" 在该租户下已存在`);
    }

    const permission = await this.prisma.permission.create({
      data: {
        name: createPermissionDto.name,
        code: createPermissionDto.code,
        description: createPermissionDto.description,
        status: createPermissionDto.status ?? 1,
        tenantId,
      },
    });

    return this.transformPermissionData(permission);
  }

  async updatePermission(
    id: number,
    updatePermissionDto: UpdatePermissionDto,
    tenantId?: number,
  ): Promise<PermissionDto> {
    if (!tenantId) {
      throw new BadRequestException('租户ID不能为空');
    }

    // 检查权限是否存在
    const existingPermission = await this.prisma.permission.findFirst({
      where: { id, tenantId },
    });

    if (!existingPermission) {
      throw new NotFoundException(`权限ID ${id} 在该租户下不存在`);
    }

    // 如果更新权限码，检查是否与其他权限冲突
    if (updatePermissionDto.code && updatePermissionDto.code !== existingPermission.code) {
      const codeExists = await this.checkPermissionCodeExists(
        updatePermissionDto.code,
        id,
        tenantId,
      );
      if (codeExists) {
        throw new BadRequestException(`权限码 "${updatePermissionDto.code}" 在该租户下已存在`);
      }
    }

    const permission = await this.prisma.permission.update({
      where: { id },
      data: {
        ...(updatePermissionDto.name && { name: updatePermissionDto.name }),
        ...(updatePermissionDto.code && { code: updatePermissionDto.code }),
        ...(updatePermissionDto.description !== undefined && {
          description: updatePermissionDto.description,
        }),
        ...(updatePermissionDto.status !== undefined && { status: updatePermissionDto.status }),
        updatedAt: new Date(),
      },
    });

    return this.transformPermissionData(permission);
  }

  async deletePermission(id: number, tenantId?: number): Promise<boolean> {
    if (!tenantId) {
      throw new BadRequestException('租户ID不能为空');
    }

    // 检查权限是否存在
    const existingPermission = await this.prisma.permission.findFirst({
      where: { id, tenantId },
    });

    if (!existingPermission) {
      throw new NotFoundException(`权限ID ${id} 在该租户下不存在`);
    }

    // 检查权限是否被角色使用
    const rolePermissionCount = await this.prisma.rolePermission.count({
      where: { permissionId: id, tenantId },
    });

    if (rolePermissionCount > 0) {
      throw new BadRequestException('该权限正在被角色使用，无法删除');
    }

    await this.prisma.permission.delete({
      where: { id },
    });

    return true;
  }

  async checkPermissionCodeExists(
    code: string,
    excludeId?: number,
    tenantId?: number,
  ): Promise<boolean> {
    if (!tenantId) {
      throw new BadRequestException('租户ID不能为空');
    }

    const where: any = { code, tenantId };

    if (excludeId) {
      where.id = { not: excludeId };
    }

    const permission = await this.prisma.permission.findFirst({
      where,
    });

    return !!permission;
  }

  async assignPermissionsToRole(
    assignDto: AssignPermissionToRoleDto,
    tenantId?: number,
  ): Promise<boolean> {
    if (!tenantId) {
      throw new BadRequestException('租户ID不能为空');
    }

    const { roleId, permissionIds } = assignDto;

    // 检查角色是否存在且属于该租户
    const role = await this.prisma.role.findFirst({
      where: { id: roleId, tenantId },
    });

    if (!role) {
      throw new NotFoundException(`角色ID ${roleId} 在该租户下不存在`);
    }

    // 检查权限是否存在且属于该租户
    if (permissionIds.length > 0) {
      const permissions = await this.prisma.permission.findMany({
        where: {
          id: { in: permissionIds },
          tenantId,
        },
      });

      if (permissions.length !== permissionIds.length) {
        throw new BadRequestException('部分权限ID在该租户下不存在');
      }
    }

    // 删除角色现有的权限
    await this.prisma.rolePermission.deleteMany({
      where: { roleId, tenantId },
    });

    // 分配新的权限
    if (permissionIds.length > 0) {
      const rolePermissions = permissionIds.map(permissionId => ({
        roleId,
        permissionId,
        tenantId,
      }));

      await this.prisma.rolePermission.createMany({
        data: rolePermissions,
      });
    }

    return true;
  }

  async getRolePermissions(roleId: string, tenantId?: number): Promise<PermissionDto[]> {
    if (!tenantId) {
      throw new BadRequestException('租户ID不能为空');
    }

    // 检查角色是否存在且属于该租户
    const role = await this.prisma.role.findFirst({
      where: { id: roleId, tenantId },
    });

    if (!role) {
      throw new NotFoundException(`角色ID ${roleId} 在该租户下不存在`);
    }

    const rolePermissions = await this.prisma.rolePermission.findMany({
      where: { roleId, tenantId },
      include: {
        permission: true,
      },
    });

    return rolePermissions.map(rp => this.transformPermissionData(rp.permission));
  }

  async removePermissionFromRole(
    roleId: string,
    permissionId: number,
    tenantId?: number,
  ): Promise<boolean> {
    if (!tenantId) {
      throw new BadRequestException('租户ID不能为空');
    }

    // 检查角色权限关联是否存在
    const rolePermission = await this.prisma.rolePermission.findFirst({
      where: { roleId, permissionId, tenantId },
    });

    if (!rolePermission) {
      throw new NotFoundException('角色权限关联在该租户下不存在');
    }

    await this.prisma.rolePermission.delete({
      where: { id: rolePermission.id },
    });

    return true;
  }

  private transformPermissionData(permission: any): PermissionDto {
    return {
      id: permission.id,
      name: permission.name,
      code: permission.code,
      description: permission.description,
      status: permission.status,
      createdAt: permission.createdAt,
      updatedAt: permission.updatedAt,
    };
  }
}
