import { Injectable } from '@nestjs/common';

import {
  CreatePermissionDto,
  UpdatePermissionDto,
  PermissionDto,
  AssignPermissionToRoleDto,
  PermissionQueryDto,
} from './dto/permission.dto';
import { SystemPermissionStrategy } from './strategies/system-permission.strategy';
import { TenantPermissionStrategy } from './strategies/tenant-permission.strategy';

@Injectable()
export class PermissionService {
  constructor(
    private readonly systemPermissionStrategy: SystemPermissionStrategy,
    private readonly tenantPermissionStrategy: TenantPermissionStrategy,
  ) {}

  /**
   * 获取权限列表
   * @param userType 用户类型
   * @param query 查询条件
   * @param tenantId 租户ID
   * @returns 权限列表
   */
  async getPermissions(
    userType: string,
    query?: PermissionQueryDto,
    tenantId?: number,
  ): Promise<PermissionDto[]> {
    const strategy = this.getStrategy(userType);
    return strategy.getPermissions(query, tenantId);
  }

  /**
   * 根据ID获取权限
   * @param id 权限ID
   * @param userType 用户类型
   * @param tenantId 租户ID
   * @returns 权限信息
   */
  async getPermissionById(
    id: number,
    userType: string,
    tenantId?: number,
  ): Promise<PermissionDto | null> {
    const strategy = this.getStrategy(userType);
    return strategy.getPermissionById(id, tenantId);
  }

  /**
   * 创建权限
   * @param createPermissionDto 创建权限DTO
   * @param userType 用户类型
   * @param tenantId 租户ID
   * @returns 创建的权限信息
   */
  async createPermission(
    createPermissionDto: CreatePermissionDto,
    userType: string,
    tenantId?: number,
  ): Promise<PermissionDto> {
    const strategy = this.getStrategy(userType);
    return strategy.createPermission(createPermissionDto, tenantId);
  }

  /**
   * 更新权限
   * @param id 权限ID
   * @param updatePermissionDto 更新权限DTO
   * @param userType 用户类型
   * @param tenantId 租户ID
   * @returns 更新后的权限信息
   */
  async updatePermission(
    id: number,
    updatePermissionDto: UpdatePermissionDto,
    userType: string,
    tenantId?: number,
  ): Promise<PermissionDto> {
    const strategy = this.getStrategy(userType);
    return strategy.updatePermission(id, updatePermissionDto, tenantId);
  }

  /**
   * 删除权限
   * @param id 权限ID
   * @param userType 用户类型
   * @param tenantId 租户ID
   * @returns 删除成功标志
   */
  async deletePermission(id: number, userType: string, tenantId?: number): Promise<boolean> {
    const strategy = this.getStrategy(userType);
    return strategy.deletePermission(id, tenantId);
  }

  /**
   * 检查权限码是否存在
   * @param code 权限码
   * @param userType 用户类型
   * @param excludeId 排除的权限ID
   * @param tenantId 租户ID
   * @returns 是否存在
   */
  async checkPermissionCodeExists(
    code: string,
    userType: string,
    excludeId?: number,
    tenantId?: number,
  ): Promise<boolean> {
    const strategy = this.getStrategy(userType);
    return strategy.checkPermissionCodeExists(code, excludeId, tenantId);
  }

  /**
   * 分配权限给角色
   * @param assignDto 分配权限DTO
   * @param userType 用户类型
   * @param tenantId 租户ID
   * @returns 操作成功标志
   */
  async assignPermissionsToRole(
    assignDto: AssignPermissionToRoleDto,
    userType: string,
    tenantId?: number,
  ): Promise<boolean> {
    const strategy = this.getStrategy(userType);
    return strategy.assignPermissionsToRole(assignDto, tenantId);
  }

  /**
   * 获取角色的权限列表
   * @param roleId 角色ID
   * @param userType 用户类型
   * @param tenantId 租户ID
   * @returns 权限列表
   */
  async getRolePermissions(
    roleId: string,
    userType: string,
    tenantId?: number,
  ): Promise<PermissionDto[]> {
    const strategy = this.getStrategy(userType);
    return strategy.getRolePermissions(roleId, tenantId);
  }

  /**
   * 移除角色的权限
   * @param roleId 角色ID
   * @param permissionId 权限ID
   * @param userType 用户类型
   * @param tenantId 租户ID
   * @returns 操作成功标志
   */
  async removePermissionFromRole(
    roleId: string,
    permissionId: number,
    userType: string,
    tenantId?: number,
  ): Promise<boolean> {
    const strategy = this.getStrategy(userType);
    return strategy.removePermissionFromRole(roleId, permissionId, tenantId);
  }

  /**
   * 根据用户类型获取对应的策略
   * @param userType 用户类型
   * @returns 权限策略
   */
  private getStrategy(userType: string) {
    switch (userType) {
      case 'system':
        return this.systemPermissionStrategy;
      case 'tenant':
        return this.tenantPermissionStrategy;
      default:
        throw new Error(`不支持的用户类型: ${userType}`);
    }
  }
}
