import { BadRequestException, Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';

import { UserStrategyType } from './user-strategy.factory';
import { IUserStrategy } from './user-strategy.interface';
import { CreateUserDto } from '../dto/create-user.dto';
import { UpdateUserDto } from '../dto/update-user.dto';

import { ApiCode, ApiMessage } from '@/core/common/constants/api-code.constant';
import { DateFormatUtil } from '@/core/common/utils/date-format.util';
import { PasswordUtil } from '@/core/common/utils/password.util';
import { TENANT_PRISMA_SERVICE } from '@/core/database/prisma/tenant-prisma.service';
import { PaginationOptions } from '@/core/pagination-options/pagination-options';

/**
 * 租户用户策略
 * 实现租户用户相关的操作
 */
@Injectable()
export class TenantUserStrategy implements IUserStrategy {
  private readonly logger = new Logger(TenantUserStrategy.name);

  constructor(@Inject(TENANT_PRISMA_SERVICE) private readonly prisma: any) {}

  /**
   * 获取策略类型
   * @returns 策略类型
   */
  getType(): string {
    return UserStrategyType.TENANT;
  }

  /**
   * 创建租户用户
   * @param createUserDto 创建用户数据
   * @param tenantId 租户ID
   * @returns 创建的租户用户
   */
  async create(createUserDto: CreateUserDto, tenantId?: string): Promise<any> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    try {
      // 检查用户名是否已存在
      const existingUser = await this.prisma.user.findFirst({
        where: {
          username: createUserDto.username,
          tenantId,
        },
      });

      if (existingUser) {
        this.logger.warn(`创建用户失败: 用户名 ${createUserDto.username} 已存在`);
        throw new BadRequestException({
          code: ApiCode.USER_ALREADY_EXISTS,
          message: ApiMessage[ApiCode.USER_ALREADY_EXISTS],
        });
      }

      // 对密码进行哈希处理（使用bcrypt）
      const passwordHash = await PasswordUtil.hash(createUserDto.password);

      // 创建用户
      const user = await this.prisma.user.create({
        data: {
          username: createUserDto.username,
          password: passwordHash,
          realName: createUserDto.realName,
          emailAddress: createUserDto.email,
          status: createUserDto.status || 1,
          tenantId,
          // 添加新增字段
          phoneNumber: createUserDto.phoneNumber,
          idCardNumber: createUserDto.idCardNumber,
          openid: createUserDto.openid,
          unionid: createUserDto.unionid,
          adminRemark: createUserDto.adminRemark,
        },
      });

      // 如果提供了角色ID，则创建用户角色关联
      if (createUserDto.roleIds && createUserDto.roleIds.length > 0) {
        const userRoles = createUserDto.roleIds.map(roleId => ({
          userId: user.id,
          roleId,
          tenantId,
        }));

        await this.prisma.userRole.createMany({
          data: userRoles,
        });
      }

      // 返回创建的用户（不包含密码）
      const { password, ...rest } = user;

      // 获取用户角色信息
      const userWithRoles = await this.prisma.user.findFirst({
        where: {
          id: user.id,
          tenantId,
        },
        include: {
          userRoles: {
            include: {
              role: true,
            },
          },
        },
      });

      // 格式化数据
      const formattedUser = {
        ...rest,
        // 格式化时间字段
        createTime: DateFormatUtil.formatToDateTime(user.createdAt),
        updateTime: DateFormatUtil.formatToDateTime(user.updatedAt),
        lastLoginTime: DateFormatUtil.formatToDateTime(user.lastLoginAt),
        // 移除原始时间字段
        createdAt: undefined,
        updatedAt: undefined,
        lastLoginAt: undefined,
        // 添加角色信息
        roles: userWithRoles.userRoles.map(ur => ur.role),
      };

      // 按照API文档要求的格式返回数据
      return formattedUser;
    } catch (error) {
      this.logger.error(`创建用户失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 查询所有租户用户
   * @param where 查询条件
   * @param options 分页选项
   * @param tenantId 租户ID
   * @returns 租户用户列表
   */
  async findAll(where: any, options: PaginationOptions, tenantId?: string): Promise<any> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    try {
      const { page = 1, pageSize = 10 } = options;
      const skip = (page - 1) * pageSize;

      // 添加租户ID条件
      const finalWhere = {
        ...where,
        tenantId,
      };

      // 查询用户总数
      const total = await this.prisma.user.count({
        where: finalWhere || undefined,
      });

      // 查询用户列表
      const users = await this.prisma.user.findMany({
        where: finalWhere || undefined,
        skip,
        take: pageSize,
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          userRoles: {
            include: {
              role: true,
            },
          },
        },
      });

      // 处理返回结果，移除密码字段并格式化时间
      const result = users.map(user => {
        const { password, ...rest } = user;
        return {
          ...rest,
          // 格式化时间字段
          createTime: DateFormatUtil.formatToDateTime(user.createdAt),
          updateTime: DateFormatUtil.formatToDateTime(user.updatedAt),
          lastLoginTime: DateFormatUtil.formatToDateTime(user.lastLoginAt),
          // 移除原始时间字段
          createdAt: undefined,
          updatedAt: undefined,
          lastLoginAt: undefined,
          roles: user.userRoles.map(ur => ur.role),
        };
      });

      // 返回数据
      return {
        items: result,
        total,
        page,
        pageSize,
      };
    } catch (error) {
      this.logger.error(`查询用户列表失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 查询单个租户用户
   * @param id 用户ID
   * @param tenantId 租户ID
   * @returns 租户用户信息
   */
  async findOne(id: number, tenantId?: string): Promise<any> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    try {
      const user = await this.prisma.user.findFirst({
        where: {
          id,
          tenantId,
        },
        include: {
          userRoles: {
            include: {
              role: true,
            },
          },
        },
      });

      if (!user) {
        throw new NotFoundException(ApiMessage[ApiCode.USER_NOT_FOUND]);
      }

      // 处理返回结果，移除密码字段并格式化时间
      const { password, ...rest } = user;

      // 返回数据
      return {
        ...rest,
        // 格式化时间字段
        createTime: DateFormatUtil.formatToDateTime(user.createdAt),
        updateTime: DateFormatUtil.formatToDateTime(user.updatedAt),
        lastLoginTime: DateFormatUtil.formatToDateTime(user.lastLoginAt),
        // 移除原始时间字段
        createdAt: undefined,
        updatedAt: undefined,
        lastLoginAt: undefined,
        roles: user.userRoles.map(ur => ur.role),
      };
    } catch (error) {
      this.logger.error(`查询用户失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 更新租户用户
   * @param id 用户ID
   * @param updateUserDto 更新用户数据
   * @param tenantId 租户ID
   * @returns 更新后的租户用户
   */
  async update(id: number, updateUserDto: UpdateUserDto, tenantId?: string): Promise<any> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    try {
      // 检查用户是否存在
      const existingUser = await this.prisma.user.findFirst({
        where: {
          id,
          tenantId,
        },
      });

      if (!existingUser) {
        throw new NotFoundException(ApiMessage[ApiCode.USER_NOT_FOUND]);
      }

      // 准备更新数据
      const updateData: any = {};

      // 处理用户名更新
      if (updateUserDto.username) {
        updateData.username = updateUserDto.username;
        this.logger.debug(`更新用户名: ${updateUserDto.username}`);
      }

      if (updateUserDto.realName) {
        updateData.realName = updateUserDto.realName;
        this.logger.debug(`更新真实姓名: ${updateUserDto.realName}`);
      }

      if (updateUserDto.email) {
        updateData.emailAddress = updateUserDto.email;
        this.logger.debug(`更新邮箱: ${updateUserDto.email}`);
      }

      if (updateUserDto.status !== undefined) {
        updateData.status = updateUserDto.status;
        this.logger.debug(`更新状态: ${updateUserDto.status}`);
      }

      // 处理新增字段
      if (updateUserDto.phoneNumber !== undefined) {
        updateData.phoneNumber = updateUserDto.phoneNumber;
        this.logger.debug(`更新手机号: ${updateUserDto.phoneNumber}`);
      }

      if (updateUserDto.idCardNumber !== undefined) {
        updateData.idCardNumber = updateUserDto.idCardNumber;
        this.logger.debug(`更新身份证号: ${updateUserDto.idCardNumber}`);
      }

      if (updateUserDto.openid !== undefined) {
        updateData.openid = updateUserDto.openid;
        this.logger.debug(`更新openid: ${updateUserDto.openid}`);
      }

      if (updateUserDto.unionid !== undefined) {
        updateData.unionid = updateUserDto.unionid;
        this.logger.debug(`更新unionid: ${updateUserDto.unionid}`);
      }

      if (updateUserDto.adminRemark !== undefined) {
        updateData.adminRemark = updateUserDto.adminRemark;
        this.logger.debug(`更新管理员备注: ${updateUserDto.adminRemark}`);
      }

      // 密码不应该通过普通的用户信息更新接口进行修改
      // 应该使用专门的密码修改接口

      // 如果没有任何需要更新的字段，则记录警告
      if (Object.keys(updateData).length === 0) {
        this.logger.warn(`更新用户时没有提供任何需要更新的字段，用户ID: ${id}`);
      }

      // 更新用户
      const user = await this.prisma.user.update({
        where: {
          id,
        },
        data: updateData,
      });

      // 如果提供了角色ID，则更新用户角色关联
      if (updateUserDto.roleIds) {
        // 先删除现有的角色关联
        await this.prisma.userRole.deleteMany({
          where: {
            userId: id,
            tenantId,
          },
        });

        // 创建新的角色关联
        if (updateUserDto.roleIds.length > 0) {
          const userRoles = updateUserDto.roleIds.map(roleId => ({
            userId: id,
            roleId,
            tenantId,
          }));

          await this.prisma.userRole.createMany({
            data: userRoles,
          });
        }
      }

      // 返回更新后的用户（不包含密码）
      const { password, ...rest } = user;

      // 获取用户角色信息
      const userWithRoles = await this.prisma.user.findFirst({
        where: {
          id,
          tenantId,
        },
        include: {
          userRoles: {
            include: {
              role: true,
            },
          },
        },
      });

      // 返回数据，包含格式化的时间字段
      return {
        ...rest,
        // 格式化时间字段
        createTime: DateFormatUtil.formatToDateTime(user.createdAt),
        updateTime: DateFormatUtil.formatToDateTime(user.updatedAt),
        lastLoginTime: DateFormatUtil.formatToDateTime(user.lastLoginAt),
        // 移除原始时间字段
        createdAt: undefined,
        updatedAt: undefined,
        lastLoginAt: undefined,
        // 添加角色信息
        roles: userWithRoles.userRoles.map(ur => ur.role),
      };
    } catch (error) {
      this.logger.error(`更新用户失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 删除租户用户
   * @param id 用户ID
   * @param tenantId 租户ID
   * @returns 删除结果
   */
  async remove(id: number, tenantId?: string): Promise<any> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    try {
      // 检查用户是否存在
      const existingUser = await this.prisma.user.findFirst({
        where: {
          id,
          tenantId,
        },
      });

      if (!existingUser) {
        throw new NotFoundException(ApiMessage[ApiCode.USER_NOT_FOUND]);
      }

      // 删除用户角色关联
      await this.prisma.userRole.deleteMany({
        where: {
          userId: id,
          tenantId,
        },
      });

      // 删除用户
      await this.prisma.user.delete({
        where: { id },
      });

      // 返回数据
      return { success: true };
    } catch (error) {
      this.logger.error(`删除用户失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 验证用户密码
   * @param id 用户ID
   * @param password 密码
   * @param tenantId 租户ID
   * @returns 密码是否正确
   */
  async validatePassword(id: number, password: string, tenantId?: string): Promise<boolean> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    try {
      // 查询用户
      const user = await this.prisma.user.findFirst({
        where: {
          id,
          tenantId,
        },
      });

      if (!user) {
        throw new NotFoundException(ApiMessage[ApiCode.USER_NOT_FOUND]);
      }

      // 使用PasswordUtil验证密码
      return await PasswordUtil.verify(password, user.password);
    } catch (error) {
      this.logger.error(`验证密码失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 更新用户密码
   * @param id 用户ID
   * @param newPassword 新密码
   * @param tenantId 租户ID
   * @returns 更新结果
   */
  async updatePassword(id: number, newPassword: string, tenantId?: string): Promise<any> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    try {
      // 查询用户是否存在
      const user = await this.prisma.user.findFirst({
        where: {
          id,
          tenantId,
        },
      });

      if (!user) {
        throw new NotFoundException(ApiMessage[ApiCode.USER_NOT_FOUND]);
      }

      // 加密密码（使用bcrypt）
      const hashedPassword = await PasswordUtil.hash(newPassword);

      // 更新密码
      await this.prisma.user.update({
        where: { id },
        data: { password: hashedPassword },
      });

      return { success: true };
    } catch (error) {
      this.logger.error(`更新密码失败: ${error.message}`);
      throw error;
    }
  }
}
