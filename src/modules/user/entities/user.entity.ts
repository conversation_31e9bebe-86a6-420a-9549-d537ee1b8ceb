import { ApiProperty } from '@nestjs/swagger';
import { User as IUser } from '@prisma/client';

export class User implements Partial<IUser> {
  @ApiProperty({ readOnly: true })
  id: number;

  @ApiProperty()
  username: string;

  @ApiProperty({ format: 'email' })
  emailAddress: string;

  @ApiProperty()
  firstName: string;

  @ApiProperty()
  lastName: string;

  @ApiProperty()
  realName: string;

  @ApiProperty()
  avatar: string;

  @ApiProperty()
  status: number;

  @ApiProperty({ format: 'date-time' })
  lastLoginAt: Date;

  @ApiProperty()
  homePath: string;

  @ApiProperty({ type: 'object' })
  metadata: Record<string, any>;

  @ApiProperty()
  tenantId: number;

  // 新增字段
  @ApiProperty()
  phoneNumber: string;

  @ApiProperty()
  idCardNumber: string;

  @ApiProperty()
  openid: string;

  @ApiProperty()
  unionid: string;

  @ApiProperty()
  adminRemark: string;
}
