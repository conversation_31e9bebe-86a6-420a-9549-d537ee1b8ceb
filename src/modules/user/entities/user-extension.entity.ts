import { ApiProperty } from '@nestjs/swagger';
import { UserExtension as IUserExtension } from '@prisma/client';

export class UserExtension implements Partial<IUserExtension> {
  @ApiProperty({ readOnly: true })
  id: number;

  @ApiProperty()
  userId: number;

  @ApiProperty()
  tenantId: number;

  @ApiProperty({ type: 'object' })
  extendedInfo: Record<string, any>;

  @ApiProperty({ format: 'date-time' })
  createdAt: Date;

  @ApiProperty({ format: 'date-time' })
  updatedAt: Date;
}
