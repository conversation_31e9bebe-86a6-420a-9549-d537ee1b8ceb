import { ApiProperty } from '@nestjs/swagger';
import { UserRemark as IUserRemark } from '@prisma/client';

export class UserRemark implements Partial<IUserRemark> {
  @ApiProperty({ readOnly: true })
  id: number;

  @ApiProperty()
  userId: number;

  @ApiProperty()
  targetUserId: number;

  @ApiProperty()
  remark: string;

  @ApiProperty()
  tenantId: number;

  @ApiProperty({ format: 'date-time' })
  createdAt: Date;

  @ApiProperty({ format: 'date-time' })
  updatedAt: Date;
}
