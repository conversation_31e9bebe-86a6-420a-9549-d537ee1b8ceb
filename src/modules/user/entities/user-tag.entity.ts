import { ApiProperty } from '@nestjs/swagger';
import { UserTag as IUserTag } from '@prisma/client';

export class UserTag implements Partial<IUserTag> {
  @ApiProperty({ readOnly: true })
  id: number;

  @ApiProperty()
  name: string;

  @ApiProperty()
  color: string;

  @ApiProperty()
  tenantId: number;

  @ApiProperty({ format: 'date-time' })
  createdAt: Date;

  @ApiProperty({ format: 'date-time' })
  updatedAt: Date;
}
