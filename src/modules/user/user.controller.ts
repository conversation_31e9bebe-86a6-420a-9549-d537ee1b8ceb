import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Put,
  Query,
  Req,
  BadRequestException,
} from '@nestjs/common';
import { ApiOkResponse, ApiQuery, ApiTags, ApiOperation } from '@nestjs/swagger';

import { ChangePasswordDto } from './dto/change-password.dto';
import { CreateUserDto } from './dto/create-user.dto';
import { FindUserDto } from './dto/find-user.dto';
import { FindUsersDto } from './dto/find-users-dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserService } from './user.service';

import { BaseController } from '@/core/common/base/base.controller';
import { PaginationOptions } from '@/core/pagination-options/pagination-options';

@Controller('users')
@ApiTags('users')
export class UserController extends BaseController {
  constructor(private readonly userService: UserService) {
    super();
  }

  /**
   * 从请求中获取用户类型和租户ID
   * @param req 请求对象
   * @returns 用户类型和租户ID
   */
  protected getUserContext(req: any): { userType: string; tenantId?: string } {
    // 从JWT中获取用户类型和租户ID
    const userType = req.user?.userType || 'SYSTEM';
    const tenantId = req.user?.tenantId;

    return { userType, tenantId };
  }

  @Post()
  @ApiOkResponse({ type: FindUserDto })
  async create(@Body() createUserDto: CreateUserDto, @Req() req: any) {
    const { userType, tenantId } = this.getUserContext(req);
    const data = await this.userService.create(createUserDto, userType, tenantId);
    return this.success(data, '创建用户成功');
  }

  @Get('list')
  @ApiOkResponse({ type: FindUsersDto })
  @ApiQuery({
    name: 'page',
    type: 'number',
    required: false,
    description: 'Default = 1',
  })
  @ApiQuery({
    name: 'pageSize',
    type: 'number',
    required: false,
    description: 'Default = 10',
  })
  @ApiQuery({
    name: 'username',
    type: 'string',
    required: false,
    description: '用户名，模糊查询',
  })
  @ApiQuery({
    name: 'realName',
    type: 'string',
    required: false,
    description: '真实姓名，模糊查询',
  })
  @ApiQuery({
    name: 'email',
    type: 'string',
    required: false,
    description: '邮箱，模糊查询',
  })
  @ApiQuery({
    name: 'status',
    type: 'number',
    required: false,
    description: '状态：0-禁用，1-启用',
  })
  @ApiQuery({
    name: 'phoneNumber',
    type: 'string',
    required: false,
    description: '手机号，模糊查询',
  })
  @ApiQuery({
    name: 'idCardNumber',
    type: 'string',
    required: false,
    description: '身份证号，模糊查询',
  })
  @ApiQuery({
    name: 'openid',
    type: 'string',
    required: false,
    description: '微信openid，模糊查询',
  })
  @ApiQuery({
    name: 'unionid',
    type: 'string',
    required: false,
    description: '微信unionid，模糊查询',
  })
  @ApiQuery({
    name: 'startTime',
    type: 'string',
    required: false,
    description: '开始时间，格式：YYYY-MM-DD',
  })
  @ApiQuery({
    name: 'endTime',
    type: 'string',
    required: false,
    description: '结束时间，格式：YYYY-MM-DD',
  })
  async findAll(
    @Req() req: any,
    @Query('page', new DefaultValuePipe(new PaginationOptions().page), ParseIntPipe)
    page: number,
    @Query('pageSize', new DefaultValuePipe(new PaginationOptions().pageSize), ParseIntPipe)
    pageSize: number,
    @Query('username') username?: string,
    @Query('realName') realName?: string,
    @Query('email') email?: string,
    @Query('status') status?: number,
    @Query('phoneNumber') phoneNumber?: string,
    @Query('idCardNumber') idCardNumber?: string,
    @Query('openid') openid?: string,
    @Query('unionid') unionid?: string,
    @Query('startTime') startTime?: string,
    @Query('endTime') endTime?: string,
  ) {
    const { userType, tenantId } = this.getUserContext(req);
    const options = new PaginationOptions(pageSize, (page - 1) * pageSize, page, pageSize);

    // 收集所有查询参数
    const queryParams = {
      username,
      realName,
      email,
      status,
      phoneNumber,
      idCardNumber,
      openid,
      unionid,
      startTime,
      endTime,
    };

    // 调用服务层方法，传递查询参数
    const data = await this.userService.findAll(queryParams, options, userType, tenantId);
    return this.success(data);
  }

  @Get(':id')
  @ApiOkResponse({ type: FindUserDto })
  async findOne(@Param('id') id: string, @Req() req: any) {
    const { userType, tenantId } = this.getUserContext(req);
    const data = await this.userService.findOne(+id, userType, tenantId);
    return this.success(data);
  }

  @Put(':id')
  @Patch(':id')
  @ApiOkResponse({ type: FindUserDto })
  async update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto, @Req() req: any) {
    const { userType, tenantId } = this.getUserContext(req);
    const data = await this.userService.update(+id, updateUserDto, userType, tenantId);
    return this.success(data, '更新用户成功');
  }

  @Delete(':id')
  async remove(@Param('id') id: string, @Req() req: any) {
    const { userType, tenantId } = this.getUserContext(req);
    const data = await this.userService.remove(+id, userType, tenantId);
    return this.success(data, '删除用户成功');
  }

  /**
   * 修改密码
   * @param id 用户ID
   * @param changePasswordDto 修改密码DTO
   * @param req 请求对象
   * @returns 修改结果
   */
  @Post(':id/change-password')
  @ApiOperation({ summary: '修改密码' })
  @ApiOkResponse({ description: '修改成功' })
  async changePassword(
    @Param('id') id: string,
    @Body() changePasswordDto: ChangePasswordDto,
    @Req() req: any,
  ) {
    // 验证新密码和确认密码是否一致
    if (changePasswordDto.newPassword !== changePasswordDto.confirmPassword) {
      throw new BadRequestException('新密码和确认密码不一致');
    }

    const { userType, tenantId } = this.getUserContext(req);
    await this.userService.changePassword(
      +id,
      changePasswordDto.oldPassword,
      changePasswordDto.newPassword,
      userType,
      tenantId,
    );
    return this.success(null, '密码修改成功');
  }

  /**
   * 重置密码（管理员操作）
   * @param id 用户ID
   * @param resetPasswordDto 重置密码DTO
   * @param req 请求对象
   * @returns 重置结果
   */
  @Post(':id/reset-password')
  @ApiOperation({ summary: '重置密码（管理员操作）' })
  @ApiOkResponse({ description: '重置成功' })
  async resetPassword(
    @Param('id') id: string,
    @Body() resetPasswordDto: ResetPasswordDto,
    @Req() req: any,
  ) {
    // 验证新密码和确认密码是否一致
    if (resetPasswordDto.newPassword !== resetPasswordDto.confirmPassword) {
      throw new BadRequestException('新密码和确认密码不一致');
    }

    const { userType, tenantId } = this.getUserContext(req);
    await this.userService.resetPassword(+id, resetPasswordDto.newPassword, userType, tenantId);
    return this.success(null, '密码重置成功');
  }
}
