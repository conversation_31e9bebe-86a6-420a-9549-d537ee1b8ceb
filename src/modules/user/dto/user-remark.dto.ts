import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsOptional, IsString } from 'class-validator';

/**
 * 创建用户备注DTO
 */
export class CreateUserRemarkDto {
  @ApiProperty({ description: '被备注的用户ID' })
  @IsInt()
  @IsNotEmpty()
  targetUserId: number;

  @ApiProperty({ description: '备注内容' })
  @IsString()
  @IsNotEmpty()
  remark: string;
}

/**
 * 更新用户备注DTO
 */
export class UpdateUserRemarkDto {
  @ApiProperty({ description: '备注内容' })
  @IsString()
  @IsNotEmpty()
  remark: string;
}

/**
 * 用户备注DTO
 */
export class UserRemarkDto {
  @ApiProperty({ description: '备注ID' })
  id: number;

  @ApiProperty({ description: '备注创建者ID' })
  userId: number;

  @ApiProperty({ description: '被备注的用户ID' })
  targetUserId: number;

  @ApiProperty({ description: '备注内容' })
  remark: string;

  @ApiProperty({ description: '创建时间' })
  createTime: string;

  @ApiProperty({ description: '更新时间' })
  updateTime: string;
}

/**
 * 用户备注查询DTO
 */
export class FindUserRemarkDto {
  @ApiProperty({ description: '被备注的用户ID', required: false })
  @IsInt()
  @IsOptional()
  targetUserId?: number;

  @ApiProperty({ description: '备注创建者ID', required: false })
  @IsInt()
  @IsOptional()
  userId?: number;
}

/**
 * 用户备注列表DTO
 */
export class UserRemarkListDto {
  @ApiProperty({ description: '备注列表', type: [UserRemarkDto] })
  items: UserRemarkDto[];

  @ApiProperty({ description: '总数' })
  total: number;

  @ApiProperty({ description: '页码' })
  page: number;

  @ApiProperty({ description: '每页数量' })
  pageSize: number;
}
