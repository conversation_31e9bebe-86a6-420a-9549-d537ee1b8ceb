import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, MinLength } from 'class-validator';

/**
 * 重置密码DTO
 */
export class ResetPasswordDto {
  @ApiProperty({ description: '新密码' })
  @IsString()
  @IsNotEmpty({ message: '新密码不能为空' })
  @MinLength(6, { message: '新密码长度不能小于6位' })
  newPassword: string;

  @ApiProperty({ description: '确认新密码' })
  @IsString()
  @IsNotEmpty({ message: '确认新密码不能为空' })
  confirmPassword: string;
}
