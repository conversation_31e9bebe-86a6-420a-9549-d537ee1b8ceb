import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, IsDateString } from 'class-validator';

/**
 * 查询用户DTO
 */
export class QueryUserDto {
  @ApiProperty({ description: '用户名', required: false })
  @IsString()
  @IsOptional()
  username?: string;

  @ApiProperty({ description: '真实姓名', required: false })
  @IsString()
  @IsOptional()
  realName?: string;

  @ApiProperty({ description: '邮箱', required: false })
  @IsString()
  @IsOptional()
  email?: string;

  @ApiProperty({ description: '状态', required: false })
  @IsNumber()
  @IsOptional()
  status?: number;

  @ApiProperty({ description: '手机号', required: false })
  @IsString()
  @IsOptional()
  phoneNumber?: string;

  @ApiProperty({ description: '身份证号', required: false })
  @IsString()
  @IsOptional()
  idCardNumber?: string;

  @ApiProperty({ description: '微信openid', required: false })
  @IsString()
  @IsOptional()
  openid?: string;

  @ApiProperty({ description: '微信unionid', required: false })
  @IsString()
  @IsOptional()
  unionid?: string;

  @ApiProperty({ description: '开始时间', required: false })
  @IsDateString()
  @IsOptional()
  startTime?: string;

  @ApiProperty({ description: '结束时间', required: false })
  @IsDateString()
  @IsOptional()
  endTime?: string;
}
