import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEmail, IsNumber, IsOptional, IsString, Matches } from 'class-validator';

/**
 * 更新用户DTO，排除密码字段
 * 密码修改应该通过专门的密码修改接口进行
 */
export class UpdateUserDto {
  @ApiProperty({ description: '用户名' })
  @IsString()
  @IsOptional()
  username?: string;

  @ApiProperty({ description: '真实姓名' })
  @IsString()
  @IsOptional()
  realName?: string;

  @ApiProperty({ description: '邮箱', format: 'email' })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiProperty({ description: '状态', default: 1 })
  @IsNumber()
  @IsOptional()
  status?: number;

  @ApiProperty({ description: '角色ID列表', type: [String] })
  @IsArray()
  @IsOptional()
  roleIds?: string[];

  // 新增字段
  @ApiProperty({ description: '手机号', required: false })
  @IsString()
  @IsOptional()
  @Matches(/^1[3-9]\d{9}$/, { message: '手机号格式不正确' })
  phoneNumber?: string;

  @ApiProperty({ description: '身份证号', required: false })
  @IsString()
  @IsOptional()
  @Matches(/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, { message: '身份证号格式不正确' })
  idCardNumber?: string;

  @ApiProperty({ description: '微信openid', required: false })
  @IsString()
  @IsOptional()
  openid?: string;

  @ApiProperty({ description: '微信unionid', required: false })
  @IsString()
  @IsOptional()
  unionid?: string;

  @ApiProperty({ description: '管理员备注', required: false })
  @IsString()
  @IsOptional()
  adminRemark?: string;
}
