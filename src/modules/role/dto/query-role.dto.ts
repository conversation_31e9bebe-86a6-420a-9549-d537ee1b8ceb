import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, IsDateString } from 'class-validator';

/**
 * 查询角色DTO
 */
export class QueryRoleDto {
  @ApiProperty({ description: '角色ID', required: false })
  @IsString()
  @IsOptional()
  id?: string;

  @ApiProperty({ description: '角色名称', required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ description: '角色描述', required: false })
  @IsString()
  @IsOptional()
  remark?: string;

  @ApiProperty({ description: '状态', required: false })
  @IsNumber()
  @IsOptional()
  status?: number;

  @ApiProperty({ description: '开始时间', required: false })
  @IsDateString()
  @IsOptional()
  startTime?: string;

  @ApiProperty({ description: '结束时间', required: false })
  @IsDateString()
  @IsOptional()
  endTime?: string;
}
