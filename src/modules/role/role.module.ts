import { Module, OnModuleInit } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';

import { RoleStrategyProxy } from './role-strategy.proxy';
import { RoleController } from './role.controller';
import { RoleService } from './role.service';
import { RoleStrategyFactory } from './strategies/role-strategy.factory';
import { SystemRoleStrategy } from './strategies/system-role.strategy';
import { TenantRoleStrategy } from './strategies/tenant-role.strategy';

import { CommonModule } from '@/core/common/common.module';
import { PrismaModule } from '@/core/database/prisma/prisma.module';

@Module({
  imports: [PrismaModule, CommonModule],
  controllers: [RoleController],
  providers: [
    RoleService,
    RoleStrategyFactory,
    SystemRoleStrategy,
    TenantRoleStrategy,
    RoleStrategyProxy,
    {
      provide: 'ROLE_MODULE_OPTIONS',
      useValue: { isGlobal: true },
    },
    // 确保策略工厂在模块初始化时可用
    {
      provide: 'ROLE_STRATEGY_FACTORY_INITIALIZER',
      useFactory: (
        factory: RoleStrategyFactory,
        systemStrategy: SystemRoleStrategy,
        tenantStrategy: TenantRoleStrategy,
      ) => {
        console.log('ROLE_STRATEGY_FACTORY_INITIALIZER 被调用');

        // 注册策略
        factory.register(systemStrategy, 'SYSTEM');
        factory.register(tenantStrategy, 'TENANT');

        console.log('策略已在提供者中注册');

        return factory;
      },
      inject: [RoleStrategyFactory, SystemRoleStrategy, TenantRoleStrategy],
    },
  ],
  exports: [RoleService],
})
export class RoleModule implements OnModuleInit {
  constructor(private moduleRef: ModuleRef) {}

  /**
   * 模块初始化时注册策略
   */
  onModuleInit() {
    try {
      console.log('RoleModule.onModuleInit() 开始执行');

      // 使用get()方法获取工厂实例，因为它是DEFAULT作用域
      const factory = this.moduleRef.get(RoleStrategyFactory);
      console.log('获取到RoleStrategyFactory实例');

      // 获取策略实例
      const systemStrategy = this.moduleRef.get(SystemRoleStrategy);
      console.log('获取到SystemRoleStrategy实例');

      const tenantStrategy = this.moduleRef.get(TenantRoleStrategy);
      console.log('获取到TenantRoleStrategy实例');
      console.log(`TenantRoleStrategy.getType(): ${tenantStrategy.getType()}`);

      // 注册策略
      factory.register(systemStrategy, 'SYSTEM');
      console.log('注册SystemRoleStrategy成功');

      factory.register(tenantStrategy, 'TENANT');
      console.log('注册TenantRoleStrategy成功');

      // 验证注册是否成功
      const registeredSystemStrategy = factory.getStrategy('SYSTEM');
      const registeredTenantStrategy = factory.getStrategy('TENANT');

      console.log(`验证SystemRoleStrategy注册: ${!!registeredSystemStrategy}`);
      console.log(`验证TenantRoleStrategy注册: ${!!registeredTenantStrategy}`);

      console.log('角色策略已注册');
    } catch (error) {
      console.error('RoleModule.onModuleInit() 发生错误:', error);
    }
  }
}
