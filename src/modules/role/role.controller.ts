import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
  ParseIntPipe,
  DefaultV<PERSON>ue<PERSON>ipe,
  Logger,
  Injectable,
  Scope,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';

import { QueryRoleDto } from './dto/query-role.dto';
import { AssignRoleDto, CreateRoleDto, RoleDto, RoleListDto, UpdateRoleDto } from './dto/role.dto';
import { RoleService } from './role.service';

import { JwtAuthGuard } from '@/core/auth/guards/jwt-auth.guard';
import { BaseController } from '@/core/common/base/base.controller';
import { PaginationUtil } from '@/core/common/utils/pagination.util';
import { PaginationOptions } from '@/core/pagination-options/pagination-options';

/**
 * 角色控制器
 * 处理角色相关的API请求
 */
@ApiTags('角色')
@Controller()
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Injectable({ scope: Scope.DEFAULT })
export class RoleController extends BaseController {
  private readonly logger = new Logger(RoleController.name);

  constructor(private readonly roleService: RoleService) {
    super();
  }

  /**
   * 获取角色列表
   * @param page 页码
   * @param pageSize 每页数量
   * @param name 角色名称（可选）
   * @param status 角色状态（可选）
   * @param req 请求对象
   * @returns 角色列表
   */
  @Get('system/role/list')
  @ApiOperation({ summary: '获取角色列表' })
  @ApiResponse({ status: 200, description: '获取成功', type: RoleListDto })
  @ApiQuery({ name: 'page', type: 'number', required: false, description: '页码，默认1' })
  @ApiQuery({ name: 'pageSize', type: 'number', required: false, description: '每页数量，默认10' })
  @ApiQuery({ name: 'id', type: 'string', required: false, description: '角色ID，精确查询' })
  @ApiQuery({ name: 'name', type: 'string', required: false, description: '角色名称，模糊查询' })
  @ApiQuery({ name: 'remark', type: 'string', required: false, description: '角色描述，模糊查询' })
  @ApiQuery({
    name: 'status',
    type: 'number',
    required: false,
    description: '角色状态：0-禁用，1-启用',
  })
  @ApiQuery({
    name: 'startTime',
    type: 'string',
    required: false,
    description: '开始时间，格式：YYYY-MM-DD',
  })
  @ApiQuery({
    name: 'endTime',
    type: 'string',
    required: false,
    description: '结束时间，格式：YYYY-MM-DD',
  })
  async findAll(
    @Query('page', new DefaultValuePipe(PaginationUtil.DEFAULT_PAGE), ParseIntPipe) page: number,
    @Query('pageSize', new DefaultValuePipe(PaginationUtil.DEFAULT_PAGE_SIZE), ParseIntPipe)
    pageSize: number,
    @Query('id') id?: string,
    @Query('name') name?: string,
    @Query('remark') remark?: string,
    @Query('status') status?: number,
    @Query('startTime') startTime?: string,
    @Query('endTime') endTime?: string,
    @Req() req?: any,
  ) {
    try {
      const { userType, tenantId } = this.getUserContext(req);
      const options = new PaginationOptions(pageSize, (page - 1) * pageSize, page, pageSize);
      const queryParams = { id, name, remark, status, startTime, endTime };

      this.logger.debug(
        `角色控制器 - 查询角色列表，参数: ${JSON.stringify(queryParams)}, 用户类型: ${userType}, 租户ID: ${tenantId || 'system'}`,
      );

      const data = await this.roleService.findAll(queryParams, options, userType, tenantId);

      this.logger.debug(`角色控制器 - 查询结果: ${JSON.stringify(data).substring(0, 200)}...`);

      return this.success(data);
    } catch (error) {
      // 确保error是一个对象
      if (!error) {
        error = new Error('查询角色列表时发生未知错误');
      }

      const errorMessage = error.message || '未知错误';
      this.logger.error(`角色控制器 - 查询角色列表失败: ${errorMessage}`);

      // 抛出错误，让全局异常过滤器处理
      throw error;
    }
  }

  /**
   * 创建角色
   * @param createRoleDto 创建角色数据
   * @param req 请求对象
   * @returns 创建的角色
   */
  @Post('system/role')
  @ApiOperation({ summary: '创建角色' })
  @ApiResponse({ status: 200, description: '创建成功', type: RoleDto })
  async create(@Body() createRoleDto: CreateRoleDto, @Req() req: any) {
    const { userType, tenantId } = this.getUserContext(req);
    const data = await this.roleService.create(createRoleDto, userType, tenantId);
    return this.success(data);
  }

  /**
   * 获取角色详情
   * @param id 角色ID
   * @param req 请求对象
   * @returns 角色信息
   */
  @Get('system/role/:id')
  @ApiOperation({ summary: '获取角色详情' })
  @ApiResponse({ status: 200, description: '获取成功', type: RoleDto })
  @ApiParam({ name: 'id', description: '角色ID' })
  async findOne(@Param('id') id: string, @Req() req: any) {
    const { userType, tenantId } = this.getUserContext(req);
    const data = await this.roleService.findOne(id, userType, tenantId);
    return this.success(data);
  }

  /**
   * 更新角色
   * @param id 角色ID
   * @param updateRoleDto 更新角色数据
   * @param req 请求对象
   * @returns 更新后的角色
   */
  @Put('system/role/:id')
  @ApiOperation({ summary: '更新角色' })
  @ApiResponse({ status: 200, description: '更新成功', type: RoleDto })
  @ApiParam({ name: 'id', description: '角色ID' })
  async update(@Param('id') id: string, @Body() updateRoleDto: UpdateRoleDto, @Req() req: any) {
    const { userType, tenantId } = this.getUserContext(req);
    const data = await this.roleService.update(id, updateRoleDto, userType, tenantId);
    return this.success(data);
  }

  /**
   * 删除角色
   * @param id 角色ID
   * @param req 请求对象
   * @returns 删除结果
   */
  @Delete('system/role/:id')
  @ApiOperation({ summary: '删除角色' })
  @ApiResponse({ status: 200, description: '删除成功' })
  @ApiParam({ name: 'id', description: '角色ID' })
  async remove(@Param('id') id: string, @Req() req: any) {
    const { userType, tenantId } = this.getUserContext(req);
    const data = await this.roleService.remove(id, userType, tenantId);
    return this.success(data);
  }

  /**
   * 分配角色给用户
   * @param assignRoleDto 分配角色数据
   * @param req 请求对象
   * @returns 分配结果
   */
  @Post('system/role/assign')
  @ApiOperation({ summary: '分配角色给用户' })
  @ApiResponse({ status: 200, description: '分配成功' })
  async assignRolesToUser(@Body() assignRoleDto: AssignRoleDto, @Req() req: any) {
    const { userType, tenantId } = this.getUserContext(req);
    const data = await this.roleService.assignRolesToUser(assignRoleDto, userType, tenantId);
    return this.success(data);
  }

  /**
   * 移除用户角色
   * @param userId 用户ID
   * @param roleId 角色ID
   * @param req 请求对象
   * @returns 移除结果
   */
  @Delete('system/role/users/:userId/roles/:roleId')
  @ApiOperation({ summary: '移除用户角色' })
  @ApiResponse({ status: 200, description: '移除成功' })
  @ApiParam({ name: 'userId', description: '用户ID' })
  @ApiParam({ name: 'roleId', description: '角色ID' })
  async removeRoleFromUser(
    @Param('userId', ParseIntPipe) userId: number,
    @Param('roleId') roleId: string,
    @Req() req: any,
  ) {
    const { userType, tenantId } = this.getUserContext(req);
    const data = await this.roleService.removeRoleFromUser(userId, roleId, userType, tenantId);
    return this.success(data);
  }

  /**
   * 获取角色权限
   * @param roleId 角色ID
   * @param req 请求对象
   * @returns 权限列表
   */
  @Get('system/permission/roles/:roleId')
  @ApiOperation({ summary: '获取角色权限' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiParam({ name: 'roleId', description: '角色ID' })
  async getRolePermissions(@Param('roleId') roleId: string, @Req() req: any) {
    const { userType, tenantId } = this.getUserContext(req);
    const data = await this.roleService.getRolePermissions(roleId, userType, tenantId);
    return this.success(data);
  }
}
