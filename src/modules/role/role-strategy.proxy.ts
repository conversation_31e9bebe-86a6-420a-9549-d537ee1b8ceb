import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ModuleRef } from '@nestjs/core';

import { RoleDto, RoleListDto } from './dto/role.dto';
import { RoleStrategyType } from './strategies/role-strategy.factory';
import { SystemRoleStrategy } from './strategies/system-role.strategy';
import { TenantRoleStrategy } from './strategies/tenant-role.strategy';

import { TENANT_PRISMA_SERVICE } from '@/core/database/prisma/tenant-prisma.service';

/**
 * 角色策略代理
 * 用于解决策略模式中的作用域问题和租户数据库连接问题
 */
@Injectable()
export class RoleStrategyProxy {
  private readonly logger = new Logger(RoleStrategyProxy.name);

  constructor(
    private readonly moduleRef: ModuleRef,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 执行策略方法
   * @param userType 用户类型
   * @param methodName 方法名
   * @param args 方法参数
   * @param tenantId 租户ID（可选）
   * @returns 方法执行结果
   */
  async execute<T>(
    userType: string,
    methodName: string,
    args: any[],
    tenantId?: string,
  ): Promise<T> {
    try {
      // 获取正确的策略类型
      const strategyType =
        userType === 'SYSTEM' ? RoleStrategyType.SYSTEM : RoleStrategyType.TENANT;

      // 根据策略类型获取策略类
      const StrategyClass =
        strategyType === RoleStrategyType.SYSTEM ? SystemRoleStrategy : TenantRoleStrategy;

      try {
        // 创建策略实例
        const strategy = await this.createStrategy(StrategyClass, tenantId);

        // 确保策略实例有指定的方法
        if (typeof strategy[methodName] !== 'function') {
          this.logger.warn(`策略 ${strategy.getType()} 没有方法: ${methodName}，尝试使用系统策略`);

          // 如果策略没有指定的方法，尝试使用系统策略
          const systemStrategy = await this.moduleRef.resolve(SystemRoleStrategy);

          if (typeof systemStrategy[methodName] !== 'function') {
            throw new Error(`Both strategies do not have method: ${methodName}`);
          }

          // 执行系统策略方法
          return await systemStrategy[methodName](...args);
        }

        // 执行策略方法
        return await strategy[methodName](...args);
      } catch (strategyError) {
        // 如果创建策略实例失败，尝试使用系统策略
        this.logger.warn(`创建策略实例失败: ${strategyError.message}，尝试使用系统策略`);

        const systemStrategy = await this.moduleRef.resolve(SystemRoleStrategy);

        if (typeof systemStrategy[methodName] !== 'function') {
          throw new Error(`System strategy does not have method: ${methodName}`);
        }

        // 执行系统策略方法
        return await systemStrategy[methodName](...args);
      }
    } catch (error) {
      this.logger.error(`执行策略方法失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 创建策略实例
   * @param StrategyClass 策略类
   * @param tenantId 租户ID（可选）
   * @returns 策略实例
   */
  private async createStrategy(StrategyClass: any, tenantId?: string): Promise<any> {
    try {
      // 如果是系统策略，直接从模块引用中解析
      if (StrategyClass === SystemRoleStrategy) {
        return await this.moduleRef.resolve(SystemRoleStrategy);
      }

      // 如果是租户策略，需要特殊处理
      if (StrategyClass === TenantRoleStrategy) {
        // 如果没有提供租户ID，可能是系统用户访问租户资源，应该使用系统策略
        if (!tenantId) {
          this.logger.log('未提供租户ID，但请求的是租户策略，切换到系统策略');
          return await this.moduleRef.resolve(SystemRoleStrategy);
        }

        try {
          // 获取租户数据库连接
          const tenantPrisma = await this.getTenantPrisma(tenantId);

          // 如果租户数据库连接不可用，但没有抛出异常（因为我们在getTenantPrisma中处理了），使用系统策略
          if (!tenantPrisma) {
            this.logger.log('租户数据库连接不可用，切换到系统策略');
            return await this.moduleRef.resolve(SystemRoleStrategy);
          }

          // 创建租户策略实例，并手动注入依赖
          const strategy = new TenantRoleStrategy(tenantPrisma, this.configService, this.moduleRef);

          return strategy;
        } catch (error) {
          // 如果获取租户数据库连接失败，使用系统策略
          this.logger.warn(`获取租户数据库连接失败，切换到系统策略: ${error.message}`);
          return await this.moduleRef.resolve(SystemRoleStrategy);
        }
      }

      throw new Error(`Unknown strategy class: ${StrategyClass.name}`);
    } catch (error) {
      this.logger.error(`创建策略实例失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取租户数据库连接
   * @param tenantId 租户ID（可选）
   * @returns 租户数据库连接
   */
  private async getTenantPrisma(tenantId?: string): Promise<any> {
    try {
      // 尝试从请求中获取租户数据库连接
      const prisma = await this.moduleRef.resolve(TENANT_PRISMA_SERVICE, undefined, {
        strict: false,
      });

      if (!prisma) {
        this.logger.error('无法解析 TENANT_PRISMA_SERVICE，可能当前请求上下文中没有租户信息');

        // 如果是系统用户访问，可以返回null，由调用者处理
        if (!tenantId) {
          this.logger.log('未提供租户ID，可能是系统用户访问，返回null');
          return null;
        }

        throw new Error('租户数据库连接不可用');
      }

      return prisma;
    } catch (error) {
      this.logger.error(`获取租户数据库连接失败: ${error.message}`);

      // 如果是系统用户访问，可以返回null，由调用者处理
      if (!tenantId) {
        this.logger.log('未提供租户ID，可能是系统用户访问，返回null');
        return null;
      }

      throw new Error('租户数据库连接不可用');
    }
  }
}
