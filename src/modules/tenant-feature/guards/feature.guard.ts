import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { TenantFeatureService } from '../services/tenant-feature.service';

@Injectable()
export class FeatureGuard implements CanActivate {
  constructor(
    private readonly tenantFeatureService: TenantFeatureService,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // 获取所需功能
    const requiredFeatures = this.reflector.getAllAndOverride<string[]>('features', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredFeatures || requiredFeatures.length === 0) {
      return true;
    }

    // 获取请求中的用户信息
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // 系统用户默认有所有权限
    if (user.userType === 'SYSTEM') {
      return true;
    }

    const tenantId = user.tenantId;
    if (!tenantId) {
      return false;
    }

    // 检查所有需要的功能
    return this.tenantFeatureService.hasFeatures(tenantId, requiredFeatures);
  }
}
