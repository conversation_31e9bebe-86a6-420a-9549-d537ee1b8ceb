import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { TenantFeatureService } from '../services/tenant-feature.service';

@Injectable()
export class FeatureQuotaGuard implements CanActivate {
  constructor(
    private readonly tenantFeatureService: TenantFeatureService,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // 获取需要检查配额的功能
    const quotaFeature = this.reflector.getAllAndOverride<string>('quota-feature', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!quotaFeature) {
      return true;
    }

    // 获取请求中的用户信息
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    // 系统用户默认有无限配额
    if (user.userType === 'SYSTEM') {
      return true;
    }

    const tenantId = user.tenantId;
    if (!tenantId) {
      return false;
    }

    // 检查功能配额
    const quota = await this.tenantFeatureService.checkFeatureQuota(tenantId, quotaFeature);

    // 将配额信息添加到请求中，供后续使用
    request.featureQuota = quota;

    return quota.hasQuota;
  }
}
