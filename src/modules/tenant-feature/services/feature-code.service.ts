import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Cache } from 'cache-manager';

import {
  ALL_FEATURE_CODES,
  FEATURE_DESCRIPTIONS,
  FEATURE_MODULES,
} from '../../../core/common/constants/feature-codes.constant';
import { DateFormatUtil } from '../../../core/common/utils/date-format.util';
import { PublicPrismaService } from '../../../core/database/prisma/public-prisma.service';

@Injectable()
export class FeatureCodeService implements OnModuleInit {
  private readonly logger = new Logger(FeatureCodeService.name);
  private featureCodesCache: any[] = [];

  constructor(
    private readonly prisma: PublicPrismaService,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
  ) {}

  /**
   * 在模块初始化时，同步默认功能代码到数据库
   */
  async onModuleInit() {
    await this.syncDefaultFeatureCodes();
    await this.loadFeatureCodesToCache();
  }

  /**
   * 同步默认功能代码到数据库
   */
  private async syncDefaultFeatureCodes() {
    this.logger.log('开始同步默认功能代码到数据库...');

    const operations = ALL_FEATURE_CODES.map(code => {
      return this.prisma.featureCode.upsert({
        where: { code },
        update: {}, // 不更新已存在的记录
        create: {
          code,
          name: FEATURE_DESCRIPTIONS[code] || code,
          description: FEATURE_DESCRIPTIONS[code] || '未知功能',
          module: FEATURE_MODULES[code] || '未知模块',
          isActive: true,
          sortOrder: 0,
        },
      });
    });

    try {
      await this.prisma.$transaction(operations);
      this.logger.log(`成功同步 ${operations.length} 个默认功能代码`);
    } catch (error) {
      this.logger.error(`同步默认功能代码失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 加载所有功能代码到缓存
   */
  private async loadFeatureCodesToCache() {
    this.logger.log('加载功能代码到缓存...');

    try {
      const featureCodes = await this.prisma.featureCode.findMany({
        where: { isActive: true },
        orderBy: { sortOrder: 'asc' },
      });

      this.featureCodesCache = featureCodes;
      await this.cacheManager.set('feature-codes', featureCodes, 3600); // 缓存1小时

      this.logger.log(`成功加载 ${featureCodes.length} 个功能代码到缓存`);
    } catch (error) {
      this.logger.error(`加载功能代码到缓存失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取所有功能代码
   * @param includeInactive 是否包含未激活的功能代码
   * @param forceRefresh 是否强制刷新缓存
   */
  async getAllFeatureCodes(includeInactive = false, forceRefresh = false): Promise<any[]> {
    if (forceRefresh) {
      await this.loadFeatureCodesToCache();
    }

    // 尝试从缓存获取
    let featureCodes = await this.cacheManager.get<any[]>('feature-codes');

    if (!featureCodes || forceRefresh) {
      // 从数据库查询
      const query: any = {};
      if (!includeInactive) {
        query.isActive = true;
      }

      featureCodes = await this.prisma.featureCode.findMany({
        where: query,
        orderBy: { sortOrder: 'asc' },
      });

      // 更新缓存
      await this.cacheManager.set('feature-codes', featureCodes, 3600);
      this.featureCodesCache = featureCodes;
    }

    // 格式化时间字段并确保元数据正确
    return featureCodes.map(code => {
      // 确保元数据是对象而不是null
      const metadata = code.metadata || {};

      return {
        id: code.id,
        code: code.code,
        name: code.name,
        description: code.description,
        module: code.module,
        isActive: code.isActive,
        sortOrder: code.sortOrder,
        metadata: metadata, // 确保元数据字段被正确处理
        // 格式化时间字段
        createTime: DateFormatUtil.formatToDateTime(code.createdAt),
        updateTime: DateFormatUtil.formatToDateTime(code.updatedAt),
        // 保留原始时间字段，以便前端可以根据需要使用
        createdAt: code.createdAt,
        updatedAt: code.updatedAt,
      };
    });
  }

  /**
   * 获取功能代码详情
   * @param code 功能代码
   */
  async getFeatureCode(code: string): Promise<any> {
    if (!code) {
      this.logger.warn(`尝试获取功能代码详情，但代码为空`);
      return null;
    }

    this.logger.debug(`获取功能代码详情: ${code}`);

    // 先从缓存查找
    const cachedFeature = this.featureCodesCache.find(f => f.code === code);
    if (cachedFeature) {
      this.logger.debug(`从缓存中找到功能代码: ${code}`);
      // 确保元数据是对象而不是null
      const metadata = cachedFeature.metadata || {};

      // 格式化时间字段并确保元数据正确
      return {
        id: cachedFeature.id,
        code: cachedFeature.code,
        name: cachedFeature.name,
        description: cachedFeature.description,
        module: cachedFeature.module,
        isActive: cachedFeature.isActive,
        sortOrder: cachedFeature.sortOrder,
        metadata: metadata, // 确保元数据字段被正确处理
        // 格式化时间字段
        createTime: DateFormatUtil.formatToDateTime(cachedFeature.createdAt),
        updateTime: DateFormatUtil.formatToDateTime(cachedFeature.updatedAt),
        // 保留原始时间字段
        createdAt: cachedFeature.createdAt,
        updatedAt: cachedFeature.updatedAt,
      };
    }

    try {
      // 从数据库查询
      this.logger.debug(`从数据库查询功能代码: ${code}`);
      const feature = await this.prisma.featureCode.findUnique({
        where: { code },
      });

      if (!feature) {
        this.logger.warn(`功能代码不存在: ${code}`);
        return null;
      }

      // 确保元数据是对象而不是null
      const metadata = feature.metadata || {};

      // 格式化时间字段并确保元数据正确
      return {
        id: feature.id,
        code: feature.code,
        name: feature.name,
        description: feature.description,
        module: feature.module,
        isActive: feature.isActive,
        sortOrder: feature.sortOrder,
        metadata: metadata, // 确保元数据字段被正确处理
        // 格式化时间字段
        createTime: DateFormatUtil.formatToDateTime(feature.createdAt),
        updateTime: DateFormatUtil.formatToDateTime(feature.updatedAt),
        // 保留原始时间字段
        createdAt: feature.createdAt,
        updatedAt: feature.updatedAt,
      };
    } catch (error) {
      this.logger.error(`查询功能代码失败: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * 创建或更新功能代码
   * @param data 功能代码数据
   */
  async upsertFeatureCode(data: any): Promise<any> {
    if (!data || !data.code) {
      this.logger.error('创建或更新功能代码失败: 功能代码不能为空');
      throw new Error('功能代码不能为空');
    }

    const { code } = data;

    this.logger.debug(`创建或更新功能代码: ${code}`);

    try {
      // 确保必要字段存在
      if (!data.name) {
        throw new Error('功能名称不能为空');
      }
      if (!data.description) {
        throw new Error('功能描述不能为空');
      }
      if (!data.module) {
        throw new Error('所属模块不能为空');
      }

      const result = await this.prisma.featureCode.upsert({
        where: { code },
        update: {
          name: data.name,
          description: data.description,
          module: data.module,
          isActive: data.isActive ?? true,
          sortOrder: data.sortOrder ?? 0,
          metadata: data.metadata ?? {},
        },
        create: {
          code,
          name: data.name,
          description: data.description,
          module: data.module,
          isActive: data.isActive ?? true,
          sortOrder: data.sortOrder ?? 0,
          metadata: data.metadata ?? {},
        },
      });

      this.logger.debug(`功能代码 ${code} 创建/更新成功`);

      // 清除缓存
      await this.clearCache();

      // 确保元数据是对象而不是null
      const metadata = result.metadata || {};

      // 格式化时间字段并确保元数据正确
      return {
        id: result.id,
        code: result.code,
        name: result.name,
        description: result.description,
        module: result.module,
        isActive: result.isActive,
        sortOrder: result.sortOrder,
        metadata: metadata, // 确保元数据字段被正确处理
        // 格式化时间字段
        createTime: DateFormatUtil.formatToDateTime(result.createdAt),
        updateTime: DateFormatUtil.formatToDateTime(result.updatedAt),
        // 保留原始时间字段
        createdAt: result.createdAt,
        updatedAt: result.updatedAt,
      };
    } catch (error) {
      this.logger.error(`创建或更新功能代码失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 删除功能代码
   * @param code 功能代码
   */
  async deleteFeatureCode(code: string): Promise<void> {
    await this.prisma.featureCode.delete({
      where: { code },
    });

    // 清除缓存
    await this.clearCache();
  }

  /**
   * 清除缓存
   */
  private async clearCache(): Promise<void> {
    await this.cacheManager.del('feature-codes');
    await this.loadFeatureCodesToCache();
  }
}
