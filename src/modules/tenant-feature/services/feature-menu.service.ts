import { Inject, Injectable, Logger } from '@nestjs/common';

import { CacheService } from '../../../core/cache/cache.service';
import { PublicPrismaService } from '../../../core/database/prisma/public-prisma.service';
import { TENANT_PRISMA_SERVICE } from '../../../core/database/prisma/tenant-prisma.service';
import { FeatureMenusDto } from '../dto/feature-menu.dto';

@Injectable()
export class FeatureMenuService {
  private readonly logger = new Logger(FeatureMenuService.name);

  constructor(
    private prisma: PublicPrismaService,
    private cacheService: CacheService,
  ) {}

  /**
   * 获取功能代码关联的菜单
   * @param featureCode 功能代码
   */
  async getFeatureMenus(featureCode: string) {
    try {
      const featureMenus = await this.prisma.featureMenu.findMany({
        where: { featureCode },
        include: {
          menu: true,
        },
      });

      return featureMenus.map(fm => ({
        id: fm.id,
        menuId: fm.menuId,
        featureCode: fm.featureCode,
        menuName: fm.menu.name,
        menuPath: fm.menu.path,
        createdAt: fm.createdAt,
        updatedAt: fm.updatedAt,
      }));
    } catch (error) {
      this.logger.error(`获取功能关联菜单失败: ${featureCode}`, error.stack);
      throw error;
    }
  }

  /**
   * 设置功能代码关联的菜单
   * @param featureCode 功能代码
   * @param menuIds 菜单ID列表
   */
  async setFeatureMenus(featureCode: string, menuIds: number[]) {
    try {
      // 开始事务
      await this.prisma.$transaction(async tx => {
        // 删除现有关联
        await tx.featureMenu.deleteMany({
          where: { featureCode },
        });

        // 创建新关联
        if (menuIds.length > 0) {
          await tx.featureMenu.createMany({
            data: menuIds.map(menuId => ({
              featureCode,
              menuId,
            })),
          });
        }
      });

      this.logger.log(`功能菜单关联设置成功: ${featureCode}, 菜单数量: ${menuIds.length}`);

      // 清除相关缓存
      await this.clearFeatureMenuCache(featureCode);

      return true;
    } catch (error) {
      this.logger.error(`设置功能关联菜单失败: ${featureCode}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取功能代码列表关联的所有菜单ID
   * @param featureCodes 功能代码列表
   */
  async getMenuIdsByFeatureCodes(featureCodes: string[]): Promise<number[]> {
    try {
      const featureMenus = await this.prisma.featureMenu.findMany({
        where: {
          featureCode: { in: featureCodes },
        },
      });

      // 提取菜单ID并去重
      const menuIds = featureMenus.map(fm => fm.menuId);
      return [...new Set(menuIds)];
    } catch (error) {
      this.logger.error(`获取功能关联菜单ID失败`, error.stack);
      throw error;
    }
  }

  /**
   * 获取功能模板关联的菜单
   * @param templateCode 功能模板代码
   */
  async getFeatureTemplateMenus(templateCode: string) {
    try {
      // 获取模板包含的功能代码
      const template = await this.prisma.featureTemplate.findUnique({
        where: { code: templateCode },
      });

      if (!template) {
        return [];
      }

      // 从features字段中提取功能代码
      const features = template.features as Record<string, any>;
      const featureCodes = Object.keys(features);

      // 获取功能关联的菜单ID
      const menuIds = await this.getMenuIdsByFeatureCodes(featureCodes);

      // 获取菜单详情
      const menus = await this.prisma.systemMenu.findMany({
        where: {
          id: { in: menuIds },
          status: 1,
        },
      });

      // 构建菜单树
      return this.buildMenuTree(menus, featureCodes);
    } catch (error) {
      this.logger.error(`获取功能模板关联菜单失败: ${templateCode}`, error.stack);
      throw error;
    }
  }

  /**
   * 构建菜单树
   * @param menus 菜单列表
   * @param featureCodes 功能代码列表
   */
  private async buildMenuTree(menus: any[], featureCodes: string[]) {
    // 获取每个菜单关联的功能代码
    const menuFeatureCodes = await this.getMenuFeatureCodes(menus.map(m => m.id));

    // 构建菜单树
    const menuMap = new Map();
    menus.forEach(menu => {
      menuMap.set(menu.id, {
        ...menu,
        featureCodes: menuFeatureCodes[menu.id] || [],
        children: [],
      });
    });

    const rootMenus = [];

    menus.forEach(menu => {
      const menuWithChildren = menuMap.get(menu.id);
      if (menu.pid === null || !menuMap.has(menu.pid)) {
        rootMenus.push(menuWithChildren);
      } else {
        const parentMenu = menuMap.get(menu.pid);
        parentMenu.children.push(menuWithChildren);
      }
    });

    return rootMenus;
  }

  /**
   * 获取菜单关联的功能代码
   * @param menuIds 菜单ID列表
   */
  private async getMenuFeatureCodes(menuIds: number[]) {
    const featureMenus = await this.prisma.featureMenu.findMany({
      where: {
        menuId: { in: menuIds },
      },
    });

    const result = {};
    featureMenus.forEach(fm => {
      if (!result[fm.menuId]) {
        result[fm.menuId] = [];
      }
      result[fm.menuId].push(fm.featureCode);
    });

    return result;
  }

  /**
   * 清除功能菜单缓存
   * @param featureCode 功能代码
   */
  private async clearFeatureMenuCache(featureCode: string) {
    try {
      // 清除功能菜单缓存
      const featureMenuCacheKey = `feature:menu:${featureCode}`;
      await this.cacheService.delete(featureMenuCacheKey);

      // 清除相关租户的菜单缓存
      const tenantFeatures = await this.prisma.tenantFeature.findMany({
        where: {
          featureCode,
          enabled: true,
        },
      });

      for (const tf of tenantFeatures) {
        const pattern = `menu:TENANT:${tf.tenantId}:*`;
        await this.cacheService.deletePattern(pattern);
      }

      this.logger.log(`功能菜单缓存清除成功: ${featureCode}`);
    } catch (error) {
      this.logger.error(`功能菜单缓存清除失败: ${featureCode}`, error.stack);
    }
  }
}
