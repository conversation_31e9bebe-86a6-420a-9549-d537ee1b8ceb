import {
  Body,
  Controller,
  ForbiddenException,
  Get,
  Inject,
  Logger,
  Param,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';

import { JwtAuthGuard } from '../../../core/auth/guards/jwt-auth.guard';
import { BaseController } from '../../../core/common/base/base.controller';
import { PublicPrismaService } from '../../../core/database/prisma/public-prisma.service';
import { CategoryDto, TenantConfigDto, TenantConfigTestDto } from '../dto/tenant-config.dto';
import { TenantConfigService } from '../services/tenant-config.service';

@ApiTags('租户配置管理')
@Controller()
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class TenantConfigController extends BaseController {
  private readonly logger = new Logger(TenantConfigController.name);

  constructor(
    private readonly tenantConfigService: TenantConfigService,
    @Inject(PublicPrismaService) private readonly prisma: PublicPrismaService,
  ) {
    super();
  }

  /**
   * 获取所有配置类别
   */
  @Get('system/tenant-configs/categories')
  @ApiOperation({ summary: '获取所有配置类别' })
  async getCategories() {
    const categories = await this.tenantConfigService.getCategories();
    return this.success(categories);
  }

  /**
   * 测试接口 - 创建测试数据
   */
  @Get('system/tenant-configs/create-test-data')
  @ApiOperation({ summary: '创建测试数据' })
  async createTestData() {
    try {
      // 清除旧数据
      await this.prisma.tenantConfig.deleteMany({
        where: {
          tenantId: 1,
          category: 'email',
        },
      });

      // 创建新数据
      const testData = [
        {
          tenantId: 1,
          category: 'email',
          key: 'provider',
          value: 'smtp',
          encrypted: false,
        },
        {
          tenantId: 1,
          category: 'email',
          key: 'host',
          value: 'smtp.example.com',
          encrypted: false,
        },
        {
          tenantId: 1,
          category: 'email',
          key: 'port',
          value: '587',
          encrypted: false,
        },
        {
          tenantId: 1,
          category: 'email',
          key: 'username',
          value: '<EMAIL>',
          encrypted: false,
        },
        {
          tenantId: 1,
          category: 'email',
          key: 'password',
          value: 'password123',
          encrypted: true,
        },
      ];

      // 批量创建
      const result = await this.prisma.tenantConfig.createMany({
        data: testData,
      });

      return this.success({
        message: '测试数据创建成功',
        count: result.count,
      });
    } catch (error) {
      this.logger.error(`创建测试数据失败: ${error.message}`, error.stack);
      return this.error(500, error.message);
    }
  }

  /**
   * 获取租户配置
   */
  @Get('system/tenant-configs/:category')
  @ApiOperation({ summary: '获取租户配置' })
  @ApiParam({ name: 'category', required: true })
  @ApiQuery({ name: 'tenantId', required: false, type: Number })
  async getConfig(
    @Param() params: CategoryDto,
    @Query('tenantId') tenantId: number,
    @Req() request: any,
  ) {
    try {
      // 如果是系统管理员，使用查询参数中的tenantId
      // 如果是租户管理员，使用当前用户的tenantId
      const user = request.user;
      const actualTenantId = user.userType === 'SYSTEM' ? tenantId : user.tenantId;

      if (!actualTenantId) {
        return this.error(10001);
      }

      this.logger.log(
        `获取租户配置，租户ID: ${actualTenantId} (${typeof actualTenantId}), 类别: ${params.category}, 用户类型: ${user.userType}`,
      );

      // 直接从数据库查询，绕过服务层和缓存
      try {
        const configItems = await this.prisma.tenantConfig.findMany({
          where: {
            tenantId: actualTenantId,
            category: params.category,
          },
        });

        this.logger.log(`直接从数据库查询到 ${configItems.length} 条配置记录`);
        if (configItems.length > 0) {
          this.logger.log(`配置记录示例: ${JSON.stringify(configItems[0])}`);

          // 尝试手动构建配置对象
          const manualConfig = configItems.reduce((acc, item) => {
            acc[item.key] = item.value;
            return acc;
          }, {});

          this.logger.log(`手动构建的配置对象: ${JSON.stringify(manualConfig)}`);
        } else {
          this.logger.log(`数据库中没有找到配置记录，请检查租户ID和类别是否正确`);
        }
      } catch (error) {
        this.logger.error(`直接查询数据库失败: ${error.message}`, error.stack);
      }

      // 通过服务层获取配置
      const config = await this.tenantConfigService.getConfig(
        actualTenantId,
        params.category,
        user.userType,
      );

      this.logger.log(`服务层返回的配置: ${JSON.stringify(config)}`);

      return this.success(config);
    } catch (error) {
      if (error instanceof ForbiddenException) {
        return this.error(10003);
      }
      throw error;
    }
  }

  /**
   * 更新租户配置
   */
  @Post('system/tenant-configs/:category')
  @ApiOperation({ summary: '更新租户配置' })
  @ApiParam({ name: 'category', required: true })
  @ApiQuery({ name: 'tenantId', required: false, type: Number })
  async updateConfig(
    @Param() params: CategoryDto,
    @Query('tenantId') tenantId: number,
    @Body() data: Record<string, any>,
    @Req() request: any,
  ) {
    try {
      // 如果是系统管理员，使用查询参数中的tenantId
      // 如果是租户管理员，使用当前用户的tenantId
      const user = request.user;
      const actualTenantId = user.userType === 'SYSTEM' ? tenantId : user.tenantId;

      if (!actualTenantId) {
        return this.error(10001);
      }

      await this.tenantConfigService.updateConfig(
        actualTenantId,
        params.category,
        data,
        user.userType,
      );

      return this.success(null, '配置已更新');
    } catch (error) {
      if (error instanceof ForbiddenException) {
        return this.error(10003);
      }
      throw error;
    }
  }

  /**
   * 测试租户配置
   */
  @Post('system/tenant-configs/:category/test')
  @ApiOperation({ summary: '测试租户配置' })
  @ApiParam({ name: 'category', required: true })
  @ApiQuery({ name: 'tenantId', required: false, type: Number })
  async testConfig(
    @Param() params: CategoryDto,
    @Query('tenantId') tenantId: number,
    @Body() testData: Record<string, any>,
    @Req() request: any,
  ) {
    try {
      // 如果是系统管理员，使用查询参数中的tenantId
      // 如果是租户管理员，使用当前用户的tenantId
      const user = request.user;
      const actualTenantId = user.userType === 'SYSTEM' ? tenantId : user.tenantId;

      if (!actualTenantId) {
        return this.error(10001);
      }

      const result = await this.tenantConfigService.testConfig(
        actualTenantId,
        params.category,
        testData,
        user.userType,
      );

      // 检查是否为开发中状态
      if (result.inDevelopment) {
        return this.success(result, result.message);
      }

      return this.success(result, '测试成功');
    } catch (error) {
      if (error instanceof ForbiddenException) {
        return this.error(10003);
      }
      return this.error(10007);
    }
  }

  /**
   * 获取配置模板
   */
  @Get('system/tenant-configs/templates/:category')
  @ApiOperation({ summary: '获取配置模板' })
  @ApiParam({ name: 'category', required: true })
  async getConfigTemplates(@Param() params: CategoryDto) {
    const templates = await this.tenantConfigService.getConfigTemplates(params.category);
    return this.success(templates);
  }
}
