import {
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsNumber, IsObject, IsOptional, IsString } from 'class-validator';

import { AdminOnly } from '../../../core/auth/decorators/admin-only.decorator';
import { JwtAuthGuard } from '../../../core/auth/guards/jwt-auth.guard';
import { BaseController } from '../../../core/common/base/base.controller';
import { FeatureCodeService } from '../services/feature-code.service';

/**
 * 功能代码DTO
 */
class FeatureCodeDto {
  /**
   * 功能代码
   * @example "ai.ppt"
   */
  @IsNotEmpty({ message: '功能代码不能为空' })
  @IsString({ message: '功能代码必须是字符串' })
  code: string;

  /**
   * 功能名称
   * @example "AI PPT生成"
   */
  @IsNotEmpty({ message: '功能名称不能为空' })
  @IsString({ message: '功能名称必须是字符串' })
  name: string;

  /**
   * 功能描述
   * @example "AI PPT生成功能"
   */
  @IsNotEmpty({ message: '功能描述不能为空' })
  @IsString({ message: '功能描述必须是字符串' })
  description: string;

  /**
   * 所属模块
   * @example "AI模块"
   */
  @IsNotEmpty({ message: '所属模块不能为空' })
  @IsString({ message: '所属模块必须是字符串' })
  module: string;

  /**
   * 是否激活
   * @example true
   */
  @IsOptional()
  @IsBoolean({ message: '是否激活必须是布尔值' })
  isActive?: boolean;

  /**
   * 排序顺序
   * @example 0
   */
  @IsOptional()
  @IsNumber({}, { message: '排序顺序必须是数字' })
  sortOrder?: number;

  /**
   * 额外元数据
   * @example {}
   */
  @IsOptional()
  @IsObject({ message: '额外元数据必须是对象' })
  metadata?: Record<string, any>;
}

/**
 * 功能代码编码DTO
 */
class FeatureCodeParamDto {
  /**
   * 功能代码
   * @example "ai.ppt"
   */
  code: string;
}

@ApiTags('功能代码管理')
@Controller('system/feature-codes')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class FeatureCodeController extends BaseController {
  private readonly logger = new Logger(FeatureCodeController.name);

  constructor(private readonly featureCodeService: FeatureCodeService) {
    super();
  }

  /**
   * 获取所有功能代码
   */
  @Get('list')
  @ApiOperation({ summary: '获取所有功能代码' })
  @ApiQuery({
    name: 'includeInactive',
    required: false,
    type: Boolean,
    description: '是否包含未激活的功能代码',
  })
  @ApiQuery({
    name: 'forceRefresh',
    required: false,
    type: Boolean,
    description: '是否强制刷新缓存',
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 0 },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number', example: 1 },
              code: { type: 'string', example: 'ai.ppt' },
              name: { type: 'string', example: 'AI PPT生成' },
              description: { type: 'string', example: 'AI PPT生成功能' },
              module: { type: 'string', example: 'AI模块' },
              isActive: { type: 'boolean', example: true },
              sortOrder: { type: 'number', example: 0 },
              metadata: { type: 'object', example: {} },
            },
          },
        },
        message: { type: 'string', example: '获取成功' },
      },
    },
  })
  async getAllFeatureCodes(
    @Query('includeInactive') includeInactive?: string,
    @Query('forceRefresh') forceRefresh?: string,
  ) {
    // 将字符串转换为布尔值，支持 'true', '1', 'yes' 等值
    const parseBooleanParam = (param?: string): boolean => {
      if (!param) return false;
      return ['true', '1', 'yes', 'on'].includes(param.toLowerCase());
    };

    const featureCodes = await this.featureCodeService.getAllFeatureCodes(
      parseBooleanParam(includeInactive),
      parseBooleanParam(forceRefresh),
    );

    return this.success(featureCodes);
  }

  /**
   * 获取功能代码详情
   */
  @Get(':code')
  @ApiOperation({ summary: '获取功能代码详情' })
  @ApiParam({ name: 'code', required: true, description: '功能代码' })
  async getFeatureCode(@Param('code') code: string) {
    if (!code) {
      return this.error(10001, '功能代码不能为空');
    }

    const featureCode = await this.featureCodeService.getFeatureCode(code);

    if (!featureCode) {
      return this.error(10002, '功能代码不存在');
    }

    return this.success(featureCode);
  }

  /**
   * 创建或更新功能代码
   */
  @Post('upsert')
  @ApiOperation({ summary: '创建或更新功能代码' })
  @AdminOnly()
  async upsertFeatureCode(@Body() dto: FeatureCodeDto) {
    this.logger.log(`创建或更新功能代码: ${JSON.stringify(dto)}`);

    if (!dto.code) {
      return this.error(10001, '功能代码不能为空');
    }

    try {
      const featureCode = await this.featureCodeService.upsertFeatureCode(dto);
      return this.success(featureCode, '保存成功');
    } catch (error) {
      this.logger.error(`创建或更新功能代码失败: ${error.message}`, error.stack);
      return this.error(10004, `保存失败: ${error.message}`);
    }
  }

  /**
   * 删除功能代码
   */
  @Delete(':code')
  @ApiOperation({ summary: '删除功能代码' })
  @ApiParam({ name: 'code', required: true, description: '功能代码' })
  @AdminOnly()
  async deleteFeatureCode(@Param('code') code: string) {
    if (!code) {
      return this.error(10001, '功能代码不能为空');
    }

    try {
      await this.featureCodeService.deleteFeatureCode(code);
      return this.success(null, '删除成功');
    } catch (error) {
      this.logger.error(`删除功能代码失败: ${error.message}`, error.stack);
      return this.error(10004, '删除失败');
    }
  }
}
