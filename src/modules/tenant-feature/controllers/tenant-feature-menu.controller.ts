import { Body, Controller, Get, Param, ParseIntPipe, Post, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';

import { AdminOnly } from '../../../core/auth/decorators/admin-only.decorator';
import { JwtAuthGuard } from '../../../core/auth/guards/jwt-auth.guard';
import { BaseController } from '../../../core/common/base/base.controller';
import { BatchFeatureMenusDto, SyncTenantMenusDto } from '../dto/feature-menu.dto';
import { TenantFeatureService } from '../services/tenant-feature.service';
import { TenantMenuSyncService } from '../services/tenant-menu-sync.service';

@ApiTags('租户功能菜单管理')
@Controller('system/tenant-features')
@UseGuards(JwtAuthGuard)
@AdminOnly()
export class TenantFeatureMenuController extends BaseController {
  constructor(
    private tenantFeatureService: TenantFeatureService,
    private tenantMenuSyncService: TenantMenuSyncService,
  ) {
    super();
  }

  @Get(':tenantId/menus')
  @ApiOperation({ summary: '获取租户可用菜单' })
  @ApiParam({ name: 'tenantId', description: '租户ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getTenantMenus(@Param('tenantId', ParseIntPipe) tenantId: number) {
    const data = await this.tenantFeatureService.getTenantMenus(tenantId);
    return this.success(data);
  }

  @Post(':tenantId/sync-menus')
  @ApiOperation({ summary: '同步租户菜单' })
  @ApiParam({ name: 'tenantId', description: '租户ID' })
  @ApiResponse({ status: 200, description: '同步成功' })
  async syncTenantMenus(@Param('tenantId', ParseIntPipe) tenantId: number) {
    await this.tenantMenuSyncService.syncTenantMenusByFeatures(tenantId);
    return this.success(true, '租户菜单同步成功');
  }
}
