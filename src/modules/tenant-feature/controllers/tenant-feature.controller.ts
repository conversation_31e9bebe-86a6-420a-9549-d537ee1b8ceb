import {
  Body,
  Controller,
  ForbiddenException,
  Get,
  Param,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';

import { AdminOnly } from '../../../core/auth/decorators/admin-only.decorator';
import { JwtAuthGuard } from '../../../core/auth/guards/jwt-auth.guard';
import { BaseController } from '../../../core/common/base/base.controller';
import {
  ApplyTemplateDto,
  DisableFeatureDto,
  EnableFeatureDto,
  FeatureCodeDto,
} from '../dto/tenant-feature.dto';
import { TenantFeatureService } from '../services/tenant-feature.service';
import { TenantMenuSyncService } from '../services/tenant-menu-sync.service';

@ApiTags('租户功能管理')
@Controller()
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class TenantFeatureController extends BaseController {
  constructor(
    private readonly tenantFeatureService: TenantFeatureService,
    private readonly tenantMenuSyncService: TenantMenuSyncService,
  ) {
    super();
  }

  /**
   * 获取租户功能列表
   */
  @Get('system/tenant-features/list')
  @ApiOperation({ summary: '获取租户功能列表' })
  @ApiQuery({ name: 'tenantId', required: true, type: Number })
  @AdminOnly()
  async getTenantFeatures(@Query('tenantId') tenantId: number) {
    const features = await this.tenantFeatureService.getTenantFeatures(tenantId);
    return this.success(features);
  }

  /**
   * 启用功能
   */
  @Post('system/tenant-features/enable')
  @ApiOperation({ summary: '启用功能' })
  @AdminOnly()
  async enableFeature(@Body() dto: EnableFeatureDto) {
    await this.tenantFeatureService.enableFeature(dto.tenantId, dto.featureCode, dto);

    // 自动同步菜单
    await this.tenantMenuSyncService.syncTenantMenusByFeatures(dto.tenantId);

    return this.success(null, '功能已启用，菜单已同步');
  }

  /**
   * 禁用功能
   */
  @Post('system/tenant-features/disable')
  @ApiOperation({ summary: '禁用功能' })
  @AdminOnly()
  async disableFeature(@Body() dto: DisableFeatureDto) {
    await this.tenantFeatureService.disableFeature(dto.tenantId, dto.featureCode);

    // 自动同步菜单
    await this.tenantMenuSyncService.syncTenantMenusByFeatures(dto.tenantId);

    return this.success(null, '功能已禁用，菜单已同步');
  }

  /**
   * 应用功能模板
   */
  @Post('system/tenant-features/apply-template')
  @ApiOperation({ summary: '应用功能模板' })
  @AdminOnly()
  async applyTemplate(@Body() dto: ApplyTemplateDto) {
    try {
      await this.tenantFeatureService.applyFeatureTemplate(dto.tenantId, dto.templateCode);

      // 自动同步菜单
      await this.tenantMenuSyncService.syncTenantMenusByFeatures(dto.tenantId);

      return this.success(null, '模板已应用，菜单已同步');
    } catch (error) {
      return this.error(10002, null);
    }
  }

  /**
   * 获取当前租户功能
   */
  @Get('tenant/features/my-features')
  @ApiOperation({ summary: '获取当前租户功能' })
  async getMyFeatures(@Req() request: any) {
    const user = request.user;

    // 如果不是租户用户，返回空
    if (user.userType !== 'TENANT') {
      return this.success([]);
    }

    const features = await this.tenantFeatureService.getTenantFeatures(user.tenantId);

    // 转换为前端所需格式
    const result = features.map(feature => ({
      code: feature.featureCode,
      enabled: feature.enabled,
      quota: feature.quota,
      usedQuota: feature.usedQuota,
      remaining: feature.quota !== null ? Math.max(0, feature.quota - feature.usedQuota) : null,
      expiresAt: feature.expiresAt,
      config: feature.config || {},
    }));

    return this.success(result);
  }

  /**
   * 获取功能配置
   */
  @Get('tenant/features/config/:featureCode')
  @ApiOperation({ summary: '获取功能配置' })
  @ApiParam({ name: 'featureCode', required: true })
  async getFeatureConfig(@Param() params: FeatureCodeDto, @Req() request: any) {
    const user = request.user;

    // 如果不是租户用户，返回空
    if (user.userType !== 'TENANT') {
      return this.success(null);
    }

    try {
      const config = await this.tenantFeatureService.getFeatureConfig(
        user.tenantId,
        params.featureCode,
      );

      return this.success(config);
    } catch (error) {
      if (error instanceof ForbiddenException) {
        return this.error(10003);
      }
      throw error;
    }
  }
}
