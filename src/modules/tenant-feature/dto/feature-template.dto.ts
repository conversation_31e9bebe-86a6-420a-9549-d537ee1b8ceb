import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsObject, IsOptional, IsString } from 'class-validator';

export class FeatureTemplateDto {
  @ApiProperty({ description: '模板代码' })
  @IsString()
  @IsNotEmpty()
  code: string;

  @ApiProperty({ description: '模板名称' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: '功能配置' })
  @IsObject()
  features: Record<string, any>;

  @ApiProperty({ description: '是否激活', default: true })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean = true;
}

export class FeatureTemplateCodeDto {
  @ApiProperty({ description: '模板代码' })
  @IsString()
  @IsNotEmpty()
  code: string;
}
