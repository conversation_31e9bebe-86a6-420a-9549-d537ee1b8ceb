import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsDateString,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';

export class EnableFeatureDto {
  @ApiProperty({ description: '租户ID' })
  @IsNumber()
  tenantId: number;

  @ApiProperty({ description: '功能代码' })
  @IsString()
  @IsNotEmpty()
  featureCode: string;

  @ApiProperty({ description: '功能配置', required: false })
  @IsOptional()
  @IsObject()
  config?: any;

  @ApiProperty({ description: '过期时间', required: false })
  @IsOptional()
  @IsDateString()
  expiresAt?: string;

  @ApiProperty({ description: '配额', required: false })
  @IsOptional()
  @IsNumber()
  quota?: number;
}

export class DisableFeatureDto {
  @ApiProperty({ description: '租户ID' })
  @IsNumber()
  tenantId: number;

  @ApiProperty({ description: '功能代码' })
  @IsString()
  @IsNotEmpty()
  featureCode: string;
}

export class ApplyTemplateDto {
  @ApiProperty({ description: '租户ID' })
  @IsNumber()
  tenantId: number;

  @ApiProperty({ description: '模板代码' })
  @IsString()
  @IsNotEmpty()
  templateCode: string;
}

export class FeatureCodeDto {
  @ApiProperty({ description: '功能代码' })
  @IsString()
  @IsNotEmpty()
  featureCode: string;
}
