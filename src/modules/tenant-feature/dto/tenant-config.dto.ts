import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsObject, IsOptional, IsString } from 'class-validator';

export class TenantConfigDto {
  @ApiProperty({ description: '租户ID', required: false })
  @IsOptional()
  @IsNumber()
  tenantId?: number;

  @ApiProperty({ description: '配置类别' })
  @IsString()
  @IsNotEmpty()
  category: string;

  @ApiProperty({ description: '配置数据' })
  @IsObject()
  data: Record<string, any>;
}

export class TenantConfigTestDto {
  @ApiProperty({ description: '租户ID', required: false })
  @IsOptional()
  @IsNumber()
  tenantId?: number;

  @ApiProperty({ description: '配置类别' })
  @IsString()
  @IsNotEmpty()
  category: string;

  @ApiProperty({ description: '测试数据' })
  @IsObject()
  testData: Record<string, any>;
}

export class CategoryDto {
  @ApiProperty({ description: '配置类别' })
  @IsString()
  @IsNotEmpty()
  category: string;
}
