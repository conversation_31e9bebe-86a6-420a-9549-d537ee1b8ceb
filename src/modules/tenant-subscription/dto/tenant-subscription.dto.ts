import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsNumber,
  IsEnum,
  IsOptional,
  IsBoolean,
  IsDateString,
  ValidateNested,
  IsObject,
} from 'class-validator';

/**
 * 订阅状态枚举
 */
export enum SubscriptionStatus {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  SUSPENDED = 'suspended',
  CANCELLED = 'cancelled',
}

/**
 * 计费周期枚举
 */
export enum SubscriptionBillingCycle {
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  YEARLY = 'yearly',
}

/**
 * 创建租户订阅DTO
 */
export class CreateTenantSubscriptionDto {
  @ApiProperty({ description: '租户ID' })
  @IsString()
  tenantId: string;

  @ApiProperty({ description: '订阅计划ID' })
  @IsNumber()
  planId: number;

  @ApiProperty({ description: '订阅时长（月数）' })
  @IsNumber()
  duration: number;

  @ApiProperty({
    description: '计费周期',
    enum: SubscriptionBillingCycle,
  })
  @IsEnum(SubscriptionBillingCycle)
  billingCycle: SubscriptionBillingCycle;

  @ApiPropertyOptional({ description: '开始日期' })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({ description: '结束日期' })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({ description: '是否自动续费', default: false })
  @IsOptional()
  @IsBoolean()
  autoRenew?: boolean;

  @ApiPropertyOptional({ description: '订阅元数据' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * 更新租户订阅DTO
 */
export class UpdateTenantSubscriptionDto extends PartialType(CreateTenantSubscriptionDto) {
  @ApiPropertyOptional({
    description: '订阅状态',
    enum: SubscriptionStatus,
  })
  @IsOptional()
  @IsEnum(SubscriptionStatus)
  status?: SubscriptionStatus;
}

/**
 * 查询租户订阅DTO
 */
export class QueryTenantSubscriptionDto {
  @ApiPropertyOptional({ description: '租户ID' })
  @IsOptional()
  @IsString()
  tenantId?: string;

  @ApiPropertyOptional({ description: '订阅计划ID' })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  planId?: number;

  @ApiPropertyOptional({
    description: '订阅状态',
    enum: SubscriptionStatus,
  })
  @IsOptional()
  @IsEnum(SubscriptionStatus)
  status?: SubscriptionStatus;

  @ApiPropertyOptional({ description: '页码', default: 1 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page?: number;

  @ApiPropertyOptional({ description: '每页数量', default: 10 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  pageSize?: number;
}

/**
 * 租户订阅响应DTO
 */
export class TenantSubscriptionResponseDto {
  @ApiProperty({ description: '订阅ID' })
  id: number;

  @ApiProperty({ description: '租户ID' })
  tenantId: string;

  @ApiProperty({ description: '租户名称' })
  tenantName: string;

  @ApiProperty({ description: '订阅计划ID' })
  planId: number;

  @ApiProperty({ description: '订阅计划名称' })
  planName: string;

  @ApiProperty({ description: '订阅计划代码' })
  planCode: string;

  @ApiProperty({ description: '订阅时长（月数）' })
  duration: number;

  @ApiProperty({
    description: '计费周期',
    enum: SubscriptionBillingCycle,
  })
  billingCycle: SubscriptionBillingCycle;

  @ApiProperty({ description: '开始日期' })
  startDate: string;

  @ApiProperty({ description: '结束日期' })
  endDate: string;

  @ApiProperty({
    description: '订阅状态',
    enum: SubscriptionStatus,
  })
  status: SubscriptionStatus;

  @ApiProperty({ description: '是否自动续费' })
  autoRenew: boolean;

  @ApiProperty({ description: '订阅元数据' })
  metadata: Record<string, any>;

  @ApiProperty({ description: '创建时间' })
  createTime: string;

  @ApiProperty({ description: '更新时间' })
  updateTime: string;
}

/**
 * 租户订阅统计DTO
 */
export class TenantSubscriptionStatsDto {
  @ApiProperty({ description: '总订阅数' })
  totalSubscriptions: number;

  @ApiProperty({ description: '活跃订阅数' })
  activeSubscriptions: number;

  @ApiProperty({ description: '即将到期订阅数' })
  expiringSubscriptions: number;

  @ApiProperty({ description: '已过期订阅数' })
  expiredSubscriptions: number;

  @ApiProperty({ description: '总收入' })
  totalRevenue: number;

  @ApiProperty({ description: '月收入' })
  monthlyRevenue: number;

  @ApiProperty({ description: '计划分布' })
  planDistribution: Array<{
    planId: number;
    planName: string;
    subscriptionCount: number;
    percentage: number;
  }>;
}
