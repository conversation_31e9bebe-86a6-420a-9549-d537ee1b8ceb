import { Injectable } from '@nestjs/common';

import {
  CreateSubscriptionPlanDto,
  UpdateSubscriptionPlanDto,
  QuerySubscriptionPlanDto,
} from './dto/subscription-plan.dto';
import {
  CreateTenantSubscriptionDto,
  UpdateTenantSubscriptionDto,
  QueryTenantSubscriptionDto,
} from './dto/tenant-subscription.dto';
import { SystemSubscriptionStrategy } from './strategies/system-subscription.strategy';

import { BaseService } from '@/core/common/base/base.service';
import { PaginationOptions } from '@/core/pagination-options/pagination-options';

/**
 * 租户订阅服务
 * 处理系统层面的租户订阅和计划管理
 */
@Injectable()
export class TenantSubscriptionService extends BaseService {
  constructor(private readonly systemStrategy: SystemSubscriptionStrategy) {
    super(TenantSubscriptionService.name);
  }

  // ==================== 订阅计划管理 ====================

  /**
   * 创建订阅计划
   */
  async createPlan(createPlanDto: CreateSubscriptionPlanDto) {
    try {
      // 检查计划代码是否已存在
      const codeExists = await this.systemStrategy.isPlanCodeExists(createPlanDto.code);
      if (codeExists) {
        this.alreadyExists('订阅计划', '计划代码', createPlanDto.code);
      }

      return this.systemStrategy.createPlan(createPlanDto);
    } catch (error) {
      this.logError('创建订阅计划失败', error);
      throw error;
    }
  }

  /**
   * 查询订阅计划列表
   */
  async findAllPlans(queryDto: QuerySubscriptionPlanDto, options: PaginationOptions) {
    try {
      const where: any = {};
      if (queryDto.status) {
        where.status = queryDto.status;
      }
      if (queryDto.isActive !== undefined) {
        where.isActive = queryDto.isActive;
      }

      return this.systemStrategy.findAllPlans(where, options);
    } catch (error) {
      this.logError('查询订阅计划列表失败', error);
      throw error;
    }
  }

  /**
   * 查询单个订阅计划
   */
  async findPlanById(id: number) {
    try {
      const plan = await this.systemStrategy.findPlanById(id);
      if (!plan) {
        this.notFound('订阅计划', id);
      }
      return plan;
    } catch (error) {
      this.logError(`查询订阅计划详情失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 更新订阅计划
   */
  async updatePlan(id: number, updatePlanDto: UpdateSubscriptionPlanDto) {
    try {
      // 检查计划是否存在
      const existingPlan = await this.systemStrategy.findPlanById(id);
      if (!existingPlan) {
        this.notFound('订阅计划', id);
      }

      // 如果更新了代码，检查新代码是否已存在
      if (updatePlanDto.code && updatePlanDto.code !== existingPlan.code) {
        const codeExists = await this.systemStrategy.isPlanCodeExists(updatePlanDto.code, id);
        if (codeExists) {
          this.alreadyExists('订阅计划', '计划代码', updatePlanDto.code);
        }
      }

      return this.systemStrategy.updatePlan(id, updatePlanDto);
    } catch (error) {
      this.logError(`更新订阅计划失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 删除订阅计划
   */
  async removePlan(id: number) {
    try {
      // 检查计划是否存在
      const existingPlan = await this.systemStrategy.findPlanById(id);
      if (!existingPlan) {
        this.notFound('订阅计划', id);
      }

      return this.systemStrategy.removePlan(id);
    } catch (error) {
      this.logError(`删除订阅计划失败，ID: ${id}`, error);
      throw error;
    }
  }

  // ==================== 租户订阅管理 ====================

  /**
   * 创建租户订阅
   */
  async createSubscription(createSubscriptionDto: CreateTenantSubscriptionDto) {
    try {
      // 检查计划是否存在
      const plan = await this.systemStrategy.findPlanById(createSubscriptionDto.planId);
      if (!plan) {
        this.notFound('订阅计划', createSubscriptionDto.planId);
      }

      // 检查租户是否已有活跃订阅
      const activeSubscription = await this.systemStrategy.findActiveSubscriptionByTenant(
        createSubscriptionDto.tenantId,
      );
      if (activeSubscription) {
        this.validationError('租户已有活跃订阅，请先取消或等待过期');
      }

      return this.systemStrategy.createSubscription(createSubscriptionDto);
    } catch (error) {
      this.logError('创建租户订阅失败', error);
      throw error;
    }
  }

  /**
   * 查询租户订阅列表
   */
  async findAllSubscriptions(queryDto: QueryTenantSubscriptionDto, options: PaginationOptions) {
    try {
      const where: any = {};
      if (queryDto.tenantId) {
        where.tenantId = queryDto.tenantId;
      }
      if (queryDto.planId) {
        where.planId = queryDto.planId;
      }
      if (queryDto.status) {
        where.status = queryDto.status;
      }

      return this.systemStrategy.findAllSubscriptions(where, options);
    } catch (error) {
      this.logError('查询租户订阅列表失败', error);
      throw error;
    }
  }

  /**
   * 查询单个租户订阅
   */
  async findSubscriptionById(id: number) {
    try {
      const subscription = await this.systemStrategy.findSubscriptionById(id);
      if (!subscription) {
        this.notFound('租户订阅', id);
      }
      return subscription;
    } catch (error) {
      this.logError(`查询租户订阅详情失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 更新租户订阅
   */
  async updateSubscription(id: number, updateSubscriptionDto: UpdateTenantSubscriptionDto) {
    try {
      // 检查订阅是否存在
      const existingSubscription = await this.systemStrategy.findSubscriptionById(id);
      if (!existingSubscription) {
        this.notFound('租户订阅', id);
      }

      return this.systemStrategy.updateSubscription(id, updateSubscriptionDto);
    } catch (error) {
      this.logError(`更新租户订阅失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 删除租户订阅
   */
  async removeSubscription(id: number) {
    try {
      // 检查订阅是否存在
      const existingSubscription = await this.systemStrategy.findSubscriptionById(id);
      if (!existingSubscription) {
        this.notFound('租户订阅', id);
      }

      return this.systemStrategy.removeSubscription(id);
    } catch (error) {
      this.logError(`删除租户订阅失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 获取租户的活跃订阅
   */
  async getTenantActiveSubscription(tenantId: string) {
    try {
      return this.systemStrategy.findActiveSubscriptionByTenant(tenantId);
    } catch (error) {
      this.logError(`获取租户活跃订阅失败，租户ID: ${tenantId}`, error);
      throw error;
    }
  }

  /**
   * 获取订阅统计数据
   */
  async getSubscriptionStats() {
    try {
      return this.systemStrategy.getSubscriptionStats();
    } catch (error) {
      this.logError('获取订阅统计数据失败', error);
      throw error;
    }
  }

  /**
   * 获取租户订阅历史
   */
  async getTenantSubscriptionHistory(tenantId: string) {
    try {
      return this.systemStrategy.getTenantSubscriptionHistory(tenantId);
    } catch (error) {
      this.logError(`获取租户订阅历史失败，租户ID: ${tenantId}`, error);
      throw error;
    }
  }
}
