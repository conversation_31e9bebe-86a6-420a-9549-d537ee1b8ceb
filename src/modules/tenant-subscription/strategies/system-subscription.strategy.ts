import { Injectable } from '@nestjs/common';

import { TenantSubscriptionStrategyInterface } from './tenant-subscription-strategy.interface';
import { CreateSubscriptionPlanDto, UpdateSubscriptionPlanDto } from '../dto/subscription-plan.dto';
import {
  CreateTenantSubscriptionDto,
  UpdateTenantSubscriptionDto,
} from '../dto/tenant-subscription.dto';

import { BaseService } from '@/core/common/base/base.service';
import { PublicPrismaService } from '@/core/database/prisma/public-prisma.service';
import { PaginationOptions } from '@/core/pagination-options/pagination-options';

/**
 * 系统订阅策略
 * 处理系统数据库中的订阅计划和租户订阅
 * 注意：这个文件引用了尚未创建的数据库表，需要先创建相应的 Prisma schema
 */
@Injectable()
export class SystemSubscriptionStrategy
  extends BaseService
  implements TenantSubscriptionStrategyInterface
{
  constructor(private readonly publicPrisma: PublicPrismaService) {
    super(SystemSubscriptionStrategy.name);
  }

  // ==================== 订阅计划管理 ====================

  /**
   * 创建订阅计划
   */
  async createPlan(createPlanDto: CreateSubscriptionPlanDto): Promise<any> {
    try {
      const now = new Date();

      // 注意：这里需要等待 subscriptionPlan 表在 Prisma schema 中定义
      return (this.publicPrisma as any).subscriptionPlan.create({
        data: {
          ...createPlanDto,
          status: 'active',
          createTime: now,
          updateTime: now,
        },
      });
    } catch (error) {
      this.logError('创建订阅计划失败', error);
      throw error;
    }
  }

  /**
   * 查询订阅计划列表
   */
  async findAllPlans(where: any, options: PaginationOptions): Promise<any> {
    try {
      const [items, total] = await Promise.all([
        (this.publicPrisma as any).subscriptionPlan.findMany({
          where,
          orderBy: [{ sortOrder: 'asc' }, { createTime: 'desc' }],
          skip: options.skip,
          take: options.take,
        }),
        (this.publicPrisma as any).subscriptionPlan.count({ where }),
      ]);

      return {
        items,
        total,
        page: options.page,
        pageSize: options.pageSize,
        totalPages: Math.ceil(total / options.pageSize),
      };
    } catch (error) {
      this.logError('查询订阅计划列表失败', error);
      throw error;
    }
  }

  /**
   * 根据ID查询订阅计划
   */
  async findPlanById(id: number): Promise<any> {
    try {
      return (this.publicPrisma as any).subscriptionPlan.findUnique({
        where: { id },
      });
    } catch (error) {
      this.logError(`根据ID查询订阅计划失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 根据代码查询订阅计划
   */
  async findPlanByCode(code: string): Promise<any> {
    try {
      return (this.publicPrisma as any).subscriptionPlan.findUnique({
        where: { code },
      });
    } catch (error) {
      this.logError(`根据代码查询订阅计划失败，代码: ${code}`, error);
      throw error;
    }
  }

  /**
   * 更新订阅计划
   */
  async updatePlan(id: number, updatePlanDto: UpdateSubscriptionPlanDto): Promise<any> {
    try {
      return (this.publicPrisma as any).subscriptionPlan.update({
        where: { id },
        data: {
          ...updatePlanDto,
          updateTime: new Date(),
        },
      });
    } catch (error) {
      this.logError(`更新订阅计划失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 删除订阅计划（软删除）
   */
  async removePlan(id: number): Promise<any> {
    try {
      return (this.publicPrisma as any).subscriptionPlan.update({
        where: { id },
        data: {
          status: 'archived',
          updateTime: new Date(),
        },
      });
    } catch (error) {
      this.logError(`删除订阅计划失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 检查计划代码是否存在
   */
  async isPlanCodeExists(code: string, excludeId?: number): Promise<boolean> {
    try {
      const where: any = { code };
      if (excludeId) {
        where.id = { not: excludeId };
      }

      const count = await (this.publicPrisma as any).subscriptionPlan.count({ where });
      return count > 0;
    } catch (error) {
      this.logError(`检查计划代码是否存在失败，代码: ${code}`, error);
      return false;
    }
  }

  // ==================== 租户订阅管理 ====================

  /**
   * 创建租户订阅
   */
  async createSubscription(createSubscriptionDto: CreateTenantSubscriptionDto): Promise<any> {
    try {
      const now = new Date();

      // 计算开始和结束时间
      const startDate = createSubscriptionDto.startDate
        ? new Date(createSubscriptionDto.startDate)
        : now;

      let endDate: Date;
      if (createSubscriptionDto.endDate) {
        endDate = new Date(createSubscriptionDto.endDate);
      } else {
        // 根据时长和计费周期计算结束时间
        endDate = new Date(startDate);
        const { duration, billingCycle } = createSubscriptionDto;

        switch (billingCycle) {
          case 'monthly':
            endDate.setMonth(endDate.getMonth() + duration);
            break;
          case 'quarterly':
            endDate.setMonth(endDate.getMonth() + duration * 3);
            break;
          case 'yearly':
            endDate.setFullYear(endDate.getFullYear() + duration);
            break;
        }
      }

      // 注意：这里需要等待 tenantSubscription 表在 Prisma schema 中定义
      return (this.publicPrisma as any).tenantSubscription.create({
        data: {
          tenantId: createSubscriptionDto.tenantId,
          planId: createSubscriptionDto.planId,
          duration: createSubscriptionDto.duration,
          billingCycle: createSubscriptionDto.billingCycle,
          startDate,
          endDate,
          status: 'active',
          autoRenew: createSubscriptionDto.autoRenew || false,
          metadata: createSubscriptionDto.metadata || {},
          createTime: now,
          updateTime: now,
        },
        include: {
          plan: true,
        },
      });
    } catch (error) {
      this.logError('创建租户订阅失败', error);
      throw error;
    }
  }

  /**
   * 查询租户订阅列表
   */
  async findAllSubscriptions(where: any, options: PaginationOptions): Promise<any> {
    try {
      const [items, total] = await Promise.all([
        (this.publicPrisma as any).tenantSubscription.findMany({
          where,
          include: {
            plan: true,
          },
          orderBy: { createTime: 'desc' },
          skip: options.skip,
          take: options.take,
        }),
        (this.publicPrisma as any).tenantSubscription.count({ where }),
      ]);

      return {
        items,
        total,
        page: options.page,
        pageSize: options.pageSize,
        totalPages: Math.ceil(total / options.pageSize),
      };
    } catch (error) {
      this.logError('查询租户订阅列表失败', error);
      throw error;
    }
  }

  /**
   * 根据ID查询租户订阅
   */
  async findSubscriptionById(id: number): Promise<any> {
    try {
      return (this.publicPrisma as any).tenantSubscription.findUnique({
        where: { id },
        include: {
          plan: true,
        },
      });
    } catch (error) {
      this.logError(`根据ID查询租户订阅失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 查询租户的活跃订阅
   */
  async findActiveSubscriptionByTenant(tenantId: string): Promise<any> {
    try {
      return (this.publicPrisma as any).tenantSubscription.findFirst({
        where: {
          tenantId,
          status: 'active',
          endDate: {
            gte: new Date(),
          },
        },
        include: {
          plan: true,
        },
        orderBy: { endDate: 'desc' },
      });
    } catch (error) {
      this.logError(`查询租户活跃订阅失败，租户ID: ${tenantId}`, error);
      throw error;
    }
  }

  /**
   * 更新租户订阅
   */
  async updateSubscription(
    id: number,
    updateSubscriptionDto: UpdateTenantSubscriptionDto,
  ): Promise<any> {
    try {
      return (this.publicPrisma as any).tenantSubscription.update({
        where: { id },
        data: {
          ...updateSubscriptionDto,
          updateTime: new Date(),
        },
        include: {
          plan: true,
        },
      });
    } catch (error) {
      this.logError(`更新租户订阅失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 删除租户订阅
   */
  async removeSubscription(id: number): Promise<any> {
    try {
      return (this.publicPrisma as any).tenantSubscription.update({
        where: { id },
        data: {
          status: 'cancelled',
          updateTime: new Date(),
        },
      });
    } catch (error) {
      this.logError(`删除租户订阅失败，ID: ${id}`, error);
      throw error;
    }
  }

  // ==================== 统计方法 ====================

  /**
   * 获取订阅统计数据
   */
  async getSubscriptionStats(): Promise<any> {
    try {
      const now = new Date();
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(now.getDate() + 30);

      const [
        totalSubscriptions,
        activeSubscriptions,
        expiringSubscriptions,
        expiredSubscriptions,
        planDistribution,
      ] = await Promise.all([
        (this.publicPrisma as any).tenantSubscription.count(),
        (this.publicPrisma as any).tenantSubscription.count({
          where: {
            status: 'active',
            endDate: { gte: now },
          },
        }),
        (this.publicPrisma as any).tenantSubscription.count({
          where: {
            status: 'active',
            endDate: {
              gte: now,
              lte: thirtyDaysFromNow,
            },
          },
        }),
        (this.publicPrisma as any).tenantSubscription.count({
          where: {
            endDate: { lt: now },
          },
        }),
        (this.publicPrisma as any).tenantSubscription.groupBy({
          by: ['planId'],
          _count: { planId: true },
          where: {
            status: 'active',
          },
        }),
      ]);

      // 获取计划详情
      const planIds = planDistribution.map(item => item.planId);
      const plans = await (this.publicPrisma as any).subscriptionPlan.findMany({
        where: { id: { in: planIds } },
        select: { id: true, name: true },
      });

      const planDistributionWithDetails = planDistribution.map(item => {
        const plan = plans.find(p => p.id === item.planId);
        const percentage =
          totalSubscriptions > 0 ? (item._count.planId / totalSubscriptions) * 100 : 0;

        return {
          planId: item.planId,
          planName: plan?.name || '未知计划',
          subscriptionCount: item._count.planId,
          percentage: Math.round(percentage * 100) / 100,
        };
      });

      return {
        totalSubscriptions,
        activeSubscriptions,
        expiringSubscriptions,
        expiredSubscriptions,
        planDistribution: planDistributionWithDetails,
      };
    } catch (error) {
      this.logError('获取订阅统计数据失败', error);
      throw error;
    }
  }

  /**
   * 获取租户订阅历史
   */
  async getTenantSubscriptionHistory(tenantId: string): Promise<any> {
    try {
      return (this.publicPrisma as any).tenantSubscription.findMany({
        where: { tenantId },
        include: {
          plan: true,
        },
        orderBy: { createTime: 'desc' },
      });
    } catch (error) {
      this.logError(`获取租户订阅历史失败，租户ID: ${tenantId}`, error);
      throw error;
    }
  }
}
