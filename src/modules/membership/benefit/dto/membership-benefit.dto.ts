import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsNumber,
  IsEnum,
  IsOptional,
  IsBoolean,
  IsObject,
  Min,
  IsArray,
} from 'class-validator';

/**
 * 会员权益分类枚举
 */
export enum BenefitCategory {
  AI = 'ai',
  CONTENT = 'content',
  SERVICE = 'service',
  RESOURCE = 'resource',
  OTHER = 'other',
}

/**
 * 创建会员权益DTO
 */
export class CreateMembershipBenefitDto {
  @ApiProperty({ description: '权益代码', example: 'ai_ppt_pro' })
  @IsString()
  code: string;

  @ApiProperty({ description: '权益名称', example: 'AI PPT专业版' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: '权益描述', example: '使用AI生成专业级PPT' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: '权益分类',
    enum: BenefitCategory,
    example: BenefitCategory.AI,
  })
  @IsEnum(BenefitCategory)
  category: BenefitCategory;

  @ApiPropertyOptional({ description: '权益图标', example: 'ppt-icon' })
  @IsOptional()
  @IsString()
  icon?: string;

  @ApiPropertyOptional({ description: '权益详情', example: '可使用所有专业模板，支持高级编辑功能' })
  @IsOptional()
  @IsString()
  details?: string;

  @ApiPropertyOptional({ description: '排序顺序', example: 1 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  sortOrder?: number;

  @ApiPropertyOptional({ description: '是否激活', default: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({
    description: '元数据',
    example: { featureCode: 'ai.ppt', templates: ['basic', 'professional'] },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * 更新会员权益DTO
 */
export class UpdateMembershipBenefitDto extends PartialType(CreateMembershipBenefitDto) {}

/**
 * 查询会员权益DTO
 */
export class QueryMembershipBenefitDto {
  @ApiPropertyOptional({ description: '权益分类', enum: BenefitCategory })
  @IsOptional()
  @IsEnum(BenefitCategory)
  category?: BenefitCategory;

  @ApiPropertyOptional({ description: '是否激活' })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isActive?: boolean;

  @ApiPropertyOptional({ description: '页码', default: 1 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page?: number;

  @ApiPropertyOptional({ description: '每页数量', default: 10 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  pageSize?: number;
}

/**
 * 会员权益响应DTO
 */
export class MembershipBenefitResponseDto {
  @ApiProperty({ description: '权益ID' })
  id: number;

  @ApiProperty({ description: '租户ID' })
  tenantId: string;

  @ApiProperty({ description: '权益代码' })
  code: string;

  @ApiProperty({ description: '权益名称' })
  name: string;

  @ApiProperty({ description: '权益描述' })
  description: string;

  @ApiProperty({ description: '权益分类', enum: BenefitCategory })
  category: BenefitCategory;

  @ApiProperty({ description: '权益图标' })
  icon?: string;

  @ApiProperty({ description: '权益详情' })
  details?: string;

  @ApiProperty({ description: '是否激活' })
  isActive: boolean;

  @ApiProperty({ description: '排序顺序' })
  sortOrder: number;

  @ApiProperty({ description: '元数据' })
  metadata?: Record<string, any>;

  @ApiProperty({ description: '创建时间' })
  createTime: string;

  @ApiProperty({ description: '更新时间' })
  updateTime: string;
}

/**
 * 计划权益关联DTO
 */
export class PlanBenefitRelationDto {
  @ApiProperty({ description: '权益ID数组', type: [Number] })
  @IsArray()
  @IsNumber({}, { each: true })
  benefitIds: number[];

  @ApiPropertyOptional({
    description: '权益配额，key为权益ID，value为配额值',
    example: { '1': 50, '2': 100 },
  })
  @IsOptional()
  @IsObject()
  quotas?: Record<string, number>;
}

/**
 * 计划权益响应DTO
 */
export class PlanBenefitResponseDto {
  @ApiProperty({ description: '计划ID' })
  planId: number;

  @ApiProperty({ description: '计划名称' })
  planName: string;

  @ApiProperty({ description: '权益列表', type: [Object] })
  benefits: Array<{
    id: number;
    code: string;
    name: string;
    quota?: number;
  }>;
}
