import { Module } from '@nestjs/common';

import {
  MembershipBenefitController,
  PlanBenefitController,
  UserBenefitController,
} from './membership-benefit.controller';
import { MembershipBenefitService } from './membership-benefit.service';

@Module({
  controllers: [MembershipBenefitController, PlanBenefitController, UserBenefitController],
  providers: [MembershipBenefitService],
  exports: [MembershipBenefitService],
})
export class MembershipBenefitModule {}
