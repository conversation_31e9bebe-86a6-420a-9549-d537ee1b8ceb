import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsNumber,
  IsEnum,
  IsOptional,
  IsBoolean,
  IsDateString,
  IsObject,
  Min,
  IsUUID,
  IsArray,
} from 'class-validator';

/**
 * 用户会员状态枚举
 */
export enum UserMembershipStatus {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled',
}

/**
 * 计费周期枚举
 */
export enum MembershipBillingCycle {
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  YEARLY = 'yearly',
}

/**
 * 创建用户会员DTO
 */
export class CreateUserMembershipDto {
  @ApiProperty({ description: '用户ID' })
  @IsNumber()
  userId: number;

  @ApiProperty({ description: '会员计划ID' })
  @IsNumber()
  planId: number;

  @ApiProperty({ description: '会员时长（月数）' })
  @IsNumber()
  @Min(1)
  duration: number;

  @ApiProperty({
    description: '计费周期',
    enum: MembershipBillingCycle,
  })
  @IsEnum(MembershipBillingCycle)
  billingCycle: MembershipBillingCycle;

  @ApiPropertyOptional({ description: '开始日期' })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({ description: '结束日期' })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({ description: '是否自动续费', default: false })
  @IsOptional()
  @IsBoolean()
  autoRenew?: boolean;

  @ApiPropertyOptional({ description: '元数据' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * 更新用户会员DTO
 */
export class UpdateUserMembershipDto extends PartialType(CreateUserMembershipDto) {
  @ApiPropertyOptional({
    description: '会员状态',
    enum: UserMembershipStatus,
  })
  @IsOptional()
  @IsEnum(UserMembershipStatus)
  status?: UserMembershipStatus;
}

/**
 * 查询用户会员DTO
 */
export class QueryUserMembershipDto {
  @ApiPropertyOptional({ description: '用户ID' })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  userId?: number;

  @ApiPropertyOptional({ description: '会员计划ID' })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  planId?: number;

  @ApiPropertyOptional({
    description: '会员状态',
    enum: UserMembershipStatus,
  })
  @IsOptional()
  @IsEnum(UserMembershipStatus)
  status?: UserMembershipStatus;

  @ApiPropertyOptional({ description: '页码', default: 1 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page?: number;

  @ApiPropertyOptional({ description: '每页数量', default: 10 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  pageSize?: number;
}

/**
 * 用户会员响应DTO
 */
export class UserMembershipResponseDto {
  @ApiProperty({ description: '会员ID' })
  id: number;

  @ApiProperty({ description: '用户ID' })
  userId: number;

  @ApiProperty({ description: '用户名' })
  username: string;

  @ApiProperty({ description: '会员计划ID' })
  planId: number;

  @ApiProperty({ description: '会员计划名称' })
  planName: string;

  @ApiProperty({ description: '会员计划代码' })
  planCode: string;

  @ApiProperty({ description: '开始日期' })
  startDate: string;

  @ApiProperty({ description: '结束日期' })
  endDate: string;

  @ApiProperty({
    description: '会员状态',
    enum: UserMembershipStatus,
  })
  status: UserMembershipStatus;

  @ApiProperty({ description: '是否自动续费' })
  autoRenew: boolean;

  @ApiProperty({ description: '元数据' })
  metadata: Record<string, any>;

  @ApiProperty({ description: '创建时间' })
  createTime: string;

  @ApiProperty({ description: '更新时间' })
  updateTime: string;
}

/**
 * 会员权益使用DTO
 */
export class MembershipBenefitUsageDto {
  @ApiProperty({ description: '权益代码' })
  @IsString()
  benefitCode: string;

  @ApiProperty({ description: '使用数量', default: 1 })
  @IsNumber()
  @Min(1)
  quantity: number;

  @ApiPropertyOptional({ description: '相关元数据' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * 会员权益检查DTO
 */
export class CheckMembershipBenefitDto {
  @ApiProperty({ description: '用户ID' })
  @IsNumber()
  userId: number;

  @ApiProperty({ description: '权益代码' })
  @IsString()
  benefitCode: string;

  @ApiPropertyOptional({ description: '需要的数量', default: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  quantity?: number;
}

/**
 * 会员权益检查响应DTO
 */
export class BenefitCheckResponseDto {
  @ApiProperty({ description: '是否有权限' })
  hasAccess: boolean;

  @ApiProperty({ description: '是否有足够配额' })
  hasQuota: boolean;

  @ApiProperty({ description: '总配额' })
  totalQuota: number;

  @ApiProperty({ description: '已使用配额' })
  usedQuota: number;

  @ApiProperty({ description: '剩余配额' })
  remainingQuota: number;

  @ApiProperty({ description: '权益详情' })
  benefit: {
    code: string;
    name: string;
    description: string;
    category: string;
    config: Record<string, any>;
  };

  @ApiProperty({ description: '会员计划' })
  plan: {
    id: number;
    code: string;
    name: string;
  };
}

/**
 * 用户激活会员卡密DTO
 */
export class ActivateMembershipCardDto {
  @ApiProperty({ description: '卡密码' })
  @IsString()
  cardKey: string;

  @ApiProperty({ description: '用户ID' })
  @IsNumber()
  userId: number;
}

/**
 * 会员统计DTO
 */
export class MembershipStatsDto {
  @ApiProperty({ description: '总会员数' })
  totalMembers: number;

  @ApiProperty({ description: '活跃会员数' })
  activeMembers: number;

  @ApiProperty({ description: '即将到期会员数' })
  expiringMembers: number;

  @ApiProperty({ description: '已过期会员数' })
  expiredMembers: number;

  @ApiProperty({ description: '会员计划分布' })
  planDistribution: Array<{
    planId: number;
    planName: string;
    memberCount: number;
    percentage: number;
  }>;
}
