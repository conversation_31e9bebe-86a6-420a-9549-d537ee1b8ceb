import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
} from '@nestjs/common';
import { ApiOkResponse, ApiQuery, ApiTags, ApiOperation } from '@nestjs/swagger';

import {
  CreateMembershipPlanDto,
  UpdateMembershipPlanDto,
  QueryMembershipPlanDto,
  MembershipPlanResponseDto,
  PlanStatus,
} from './dto/membership-plan.dto';
import { MembershipPlanService } from './membership-plan.service';

import { BaseController } from '@/core/common/base/base.controller';
import { PaginationOptions } from '@/core/pagination-options/pagination-options';

@Controller('membership/plans')
@ApiTags('membership-plans')
export class MembershipPlanController extends BaseController {
  constructor(private readonly membershipPlanService: MembershipPlanService) {
    super();
  }

  /**
   * 从请求中获取租户ID
   * @param req 请求对象
   * @returns 租户ID
   */
  protected getTenantId(req: any): string {
    // 从JWT中获取租户ID
    const tenantId = req.user?.tenantId;

    if (!tenantId) {
      throw new Error('租户ID不能为空');
    }

    return tenantId;
  }

  @Post()
  @ApiOperation({ summary: '创建会员计划' })
  @ApiOkResponse({ type: MembershipPlanResponseDto })
  async create(@Body() createDto: CreateMembershipPlanDto, @Req() req: any) {
    const tenantId = this.getTenantId(req);
    const data = await this.membershipPlanService.create(createDto, tenantId);
    return this.success(data, '创建会员计划成功');
  }

  @Get('list')
  @ApiOperation({ summary: '获取会员计划列表' })
  @ApiOkResponse({ type: [MembershipPlanResponseDto] })
  @ApiQuery({
    name: 'page',
    type: 'number',
    required: false,
    description: '页码，默认为1',
  })
  @ApiQuery({
    name: 'pageSize',
    type: 'number',
    required: false,
    description: '每页数量，默认为10',
  })
  @ApiQuery({
    name: 'status',
    enum: PlanStatus,
    required: false,
    description: '状态筛选',
  })
  async findAll(
    @Req() req: any,
    @Query('page', new DefaultValuePipe(new PaginationOptions().page), ParseIntPipe)
    page: number,
    @Query('pageSize', new DefaultValuePipe(new PaginationOptions().pageSize), ParseIntPipe)
    pageSize: number,
    @Query('status') status?: PlanStatus,
  ) {
    const tenantId = this.getTenantId(req);
    const options = new PaginationOptions(pageSize, (page - 1) * pageSize, page, pageSize);

    const queryDto: QueryMembershipPlanDto = { status, page, pageSize };

    const data = await this.membershipPlanService.findAll(queryDto, options, tenantId);
    return this.success(data);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取会员计划详情' })
  @ApiOkResponse({ type: MembershipPlanResponseDto })
  async findOne(@Param('id') id: string, @Req() req: any) {
    const tenantId = this.getTenantId(req);
    const data = await this.membershipPlanService.findOne(+id, tenantId);
    return this.success(data);
  }

  @Put(':id')
  @ApiOperation({ summary: '更新会员计划' })
  @ApiOkResponse({ type: MembershipPlanResponseDto })
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateMembershipPlanDto,
    @Req() req: any,
  ) {
    const tenantId = this.getTenantId(req);
    const data = await this.membershipPlanService.update(+id, updateDto, tenantId);
    return this.success(data, '更新会员计划成功');
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除会员计划' })
  async remove(@Param('id') id: string, @Req() req: any) {
    const tenantId = this.getTenantId(req);
    await this.membershipPlanService.remove(+id, tenantId);
    return this.success(null, '删除会员计划成功');
  }

  @Get('code-exists')
  @ApiOperation({ summary: '检查计划代码是否存在' })
  @ApiQuery({
    name: 'code',
    type: 'string',
    required: true,
    description: '计划代码',
  })
  async checkCodeExists(@Query('code') code: string, @Req() req: any) {
    const tenantId = this.getTenantId(req);
    const exists = await this.membershipPlanService.isCodeExists(code, tenantId);
    return this.success({ exists });
  }
}
