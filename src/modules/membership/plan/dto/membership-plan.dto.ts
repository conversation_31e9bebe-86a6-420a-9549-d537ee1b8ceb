import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsBoolean,
  IsObject,
  IsEnum,
  Min,
  MaxLength,
} from 'class-validator';

export enum BillingCycle {
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  YEARLY = 'yearly',
}

export enum PlanStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * 创建会员计划DTO
 */
export class CreateMembershipPlanDto {
  @ApiProperty({ description: '计划代码', example: 'basic' })
  @IsString()
  @IsNotEmpty({ message: '计划代码不能为空' })
  @MaxLength(50, { message: '计划代码长度不能超过50个字符' })
  code: string;

  @ApiProperty({ description: '计划名称', example: '基础版' })
  @IsString()
  @IsNotEmpty({ message: '计划名称不能为空' })
  @MaxLength(100, { message: '计划名称长度不能超过100个字符' })
  name: string;

  @ApiProperty({ description: '计划描述', example: '适合个人用户的基础功能套餐' })
  @IsString()
  @IsNotEmpty({ message: '计划描述不能为空' })
  @MaxLength(500, { message: '计划描述长度不能超过500个字符' })
  description: string;

  @ApiProperty({ description: '价格', example: 99.0 })
  @IsNumber({}, { message: '价格必须是数字' })
  @Min(0, { message: '价格不能小于0' })
  price: number;

  @ApiPropertyOptional({ description: '原价', example: 199.0 })
  @IsOptional()
  @IsNumber({}, { message: '原价必须是数字' })
  @Min(0, { message: '原价不能小于0' })
  originalPrice?: number;

  @ApiProperty({ description: '计费周期', enum: BillingCycle, example: BillingCycle.MONTHLY })
  @IsEnum(BillingCycle, { message: '计费周期必须是有效值' })
  billingCycle: BillingCycle;

  @ApiPropertyOptional({
    description: '功能配置',
    example: { 'ai.ppt': { enabled: true, quota: 10 } },
  })
  @IsOptional()
  @IsObject({ message: '功能配置必须是对象' })
  features?: Record<string, any>;

  @ApiPropertyOptional({ description: '是否激活', example: true })
  @IsOptional()
  @IsBoolean({ message: '是否激活必须是布尔值' })
  isActive?: boolean;

  @ApiPropertyOptional({ description: '排序顺序', example: 1 })
  @IsOptional()
  @IsNumber({}, { message: '排序顺序必须是数字' })
  @Min(0, { message: '排序顺序不能小于0' })
  sortOrder?: number;
}

/**
 * 更新会员计划DTO
 */
export class UpdateMembershipPlanDto {
  @ApiPropertyOptional({ description: '计划名称', example: '基础版Plus' })
  @IsOptional()
  @IsString()
  @IsNotEmpty({ message: '计划名称不能为空' })
  @MaxLength(100, { message: '计划名称长度不能超过100个字符' })
  name?: string;

  @ApiPropertyOptional({ description: '计划描述', example: '适合个人用户的增强功能套餐' })
  @IsOptional()
  @IsString()
  @IsNotEmpty({ message: '计划描述不能为空' })
  @MaxLength(500, { message: '计划描述长度不能超过500个字符' })
  description?: string;

  @ApiPropertyOptional({ description: '价格', example: 129.0 })
  @IsOptional()
  @IsNumber({}, { message: '价格必须是数字' })
  @Min(0, { message: '价格不能小于0' })
  price?: number;

  @ApiPropertyOptional({ description: '原价', example: 199.0 })
  @IsOptional()
  @IsNumber({}, { message: '原价必须是数字' })
  @Min(0, { message: '原价不能小于0' })
  originalPrice?: number;

  @ApiPropertyOptional({
    description: '功能配置',
    example: { 'ai.ppt': { enabled: true, quota: 20 } },
  })
  @IsOptional()
  @IsObject({ message: '功能配置必须是对象' })
  features?: Record<string, any>;

  @ApiPropertyOptional({ description: '是否激活', example: true })
  @IsOptional()
  @IsBoolean({ message: '是否激活必须是布尔值' })
  isActive?: boolean;

  @ApiPropertyOptional({ description: '排序顺序', example: 2 })
  @IsOptional()
  @IsNumber({}, { message: '排序顺序必须是数字' })
  @Min(0, { message: '排序顺序不能小于0' })
  sortOrder?: number;
}

/**
 * 会员计划查询DTO
 */
export class QueryMembershipPlanDto {
  @ApiPropertyOptional({ description: '状态筛选', enum: PlanStatus, example: PlanStatus.ACTIVE })
  @IsOptional()
  @IsEnum(PlanStatus, { message: '状态必须是有效值' })
  status?: PlanStatus;

  @ApiPropertyOptional({ description: '页码', example: 1 })
  @IsOptional()
  @IsNumber({}, { message: '页码必须是数字' })
  @Min(1, { message: '页码不能小于1' })
  page?: number;

  @ApiPropertyOptional({ description: '每页数量', example: 10 })
  @IsOptional()
  @IsNumber({}, { message: '每页数量必须是数字' })
  @Min(1, { message: '每页数量不能小于1' })
  pageSize?: number;
}

/**
 * 会员计划响应DTO
 */
export class MembershipPlanResponseDto {
  @ApiProperty({ description: '计划ID', example: 1 })
  id: number;

  @ApiProperty({ description: '租户ID', example: 1 })
  tenantId: number;

  @ApiProperty({ description: '计划代码', example: 'basic' })
  code: string;

  @ApiProperty({ description: '计划名称', example: '基础版' })
  name: string;

  @ApiProperty({ description: '计划描述', example: '适合个人用户的基础功能套餐' })
  description: string;

  @ApiProperty({ description: '价格', example: 99.0 })
  price: number;

  @ApiProperty({ description: '原价', example: 199.0 })
  originalPrice: number;

  @ApiProperty({ description: '计费周期', enum: BillingCycle, example: BillingCycle.MONTHLY })
  billingCycle: BillingCycle;

  @ApiProperty({ description: '功能配置', example: { 'ai.ppt': { enabled: true, quota: 10 } } })
  features: Record<string, any>;

  @ApiProperty({ description: '是否激活', example: true })
  isActive: boolean;

  @ApiProperty({ description: '排序顺序', example: 1 })
  sortOrder: number;

  @ApiProperty({ description: '创建时间', example: '2025-06-01 12:00:00' })
  createTime: string;

  @ApiProperty({ description: '更新时间', example: '2025-06-01 12:00:00' })
  updateTime: string;
}
