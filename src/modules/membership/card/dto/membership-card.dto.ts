import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsNumber,
  IsEnum,
  IsOptional,
  IsBoolean,
  IsObject,
  IsArray,
  IsDateString,
  Min,
  MinLength,
} from 'class-validator';

/**
 * 会员卡状态枚举
 */
export enum MembershipCardStatus {
  CREATED = 'created', // 已创建
  ACTIVATED = 'activated', // 已激活
  EXPIRED = 'expired', // 已过期
  CANCELLED = 'cancelled', // 已取消
}

/**
 * 创建会员卡DTO
 */
export class CreateMembershipCardDto {
  @ApiProperty({ description: '会员计划ID' })
  @IsNumber()
  planId: number;

  @ApiProperty({ description: '卡密前缀', example: 'VIP' })
  @IsString()
  @MinLength(2)
  prefix: string;

  @ApiProperty({ description: '生成数量', example: 10 })
  @IsNumber()
  @Min(1)
  quantity: number;

  @ApiProperty({ description: '有效期(月)', example: 1 })
  @IsNumber()
  @Min(1)
  duration: number;

  @ApiPropertyOptional({ description: '批次说明', example: '618促销批次' })
  @IsOptional()
  @IsString()
  batchNote?: string;

  @ApiPropertyOptional({ description: '卡密长度', default: 8 })
  @IsOptional()
  @IsNumber()
  @Min(6)
  codeLength?: number;

  @ApiPropertyOptional({ description: '生效日期' })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({ description: '失效日期' })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({ description: '元数据' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * 更新会员卡DTO
 */
export class UpdateMembershipCardDto {
  @ApiPropertyOptional({ description: '卡密状态' })
  @IsOptional()
  @IsEnum(MembershipCardStatus)
  status?: MembershipCardStatus;

  @ApiPropertyOptional({ description: '批次说明' })
  @IsOptional()
  @IsString()
  batchNote?: string;

  @ApiPropertyOptional({ description: '生效日期' })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({ description: '失效日期' })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({ description: '元数据' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * 查询会员卡DTO
 */
export class QueryMembershipCardDto {
  @ApiPropertyOptional({ description: '会员计划ID' })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  planId?: number;

  @ApiPropertyOptional({ description: '卡密状态' })
  @IsOptional()
  @IsEnum(MembershipCardStatus)
  status?: MembershipCardStatus;

  @ApiPropertyOptional({ description: '批次说明' })
  @IsOptional()
  @IsString()
  batchNote?: string;

  @ApiPropertyOptional({ description: '创建开始日期' })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({ description: '创建结束日期' })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({ description: '页码', default: 1 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page?: number;

  @ApiPropertyOptional({ description: '每页数量', default: 10 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  pageSize?: number;
}

/**
 * 会员卡响应DTO
 */
export class MembershipCardResponseDto {
  @ApiProperty({ description: '会员卡ID' })
  id: number;

  @ApiProperty({ description: '租户ID' })
  tenantId: string;

  @ApiProperty({ description: '会员计划ID' })
  planId: number;

  @ApiProperty({ description: '会员计划名称' })
  planName: string;

  @ApiProperty({ description: '卡密代码' })
  cardCode: string;

  @ApiProperty({ description: '卡密状态' })
  status: MembershipCardStatus;

  @ApiProperty({ description: '有效期(月)' })
  duration: number;

  @ApiProperty({ description: '批次说明' })
  batchNote?: string;

  @ApiProperty({ description: '激活用户ID' })
  activatedUserId?: number;

  @ApiProperty({ description: '激活时间' })
  activatedTime?: Date;

  @ApiProperty({ description: '生效日期' })
  startDate?: Date;

  @ApiProperty({ description: '失效日期' })
  endDate?: Date;

  @ApiProperty({ description: '元数据' })
  metadata?: Record<string, any>;

  @ApiProperty({ description: '创建时间' })
  createTime: Date;

  @ApiProperty({ description: '更新时间' })
  updateTime: Date;
}

/**
 * 会员卡批次统计DTO
 */
export class CardBatchStatsDto {
  @ApiProperty({ description: '批次说明' })
  batchNote: string;

  @ApiProperty({ description: '会员计划ID' })
  planId: number;

  @ApiProperty({ description: '会员计划名称' })
  planName: string;

  @ApiProperty({ description: '总数量' })
  totalCount: number;

  @ApiProperty({ description: '已激活数量' })
  activatedCount: number;

  @ApiProperty({ description: '未激活数量' })
  inactiveCount: number;

  @ApiProperty({ description: '已过期数量' })
  expiredCount: number;

  @ApiProperty({ description: '激活率' })
  activationRate: string;

  @ApiProperty({ description: '创建时间' })
  createTime: Date;
}

/**
 * 激活会员卡DTO
 */
export class ActivateMembershipCardDto {
  @ApiProperty({ description: '用户ID' })
  @IsNumber()
  userId: number;

  @ApiProperty({ description: '卡密代码' })
  @IsString()
  cardCode: string;
}

/**
 * 导出会员卡DTO
 */
export class ExportMembershipCardDto {
  @ApiProperty({ description: '会员计划ID' })
  @IsNumber()
  planId: number;

  @ApiPropertyOptional({ description: '卡密状态' })
  @IsOptional()
  @IsEnum(MembershipCardStatus)
  status?: MembershipCardStatus;

  @ApiPropertyOptional({ description: '批次说明' })
  @IsOptional()
  @IsString()
  batchNote?: string;

  @ApiPropertyOptional({ description: '导出格式', default: 'csv', enum: ['csv', 'excel'] })
  @IsOptional()
  @IsString()
  format?: 'csv' | 'excel';
}
