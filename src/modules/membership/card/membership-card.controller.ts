import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiOkResponse, ApiQuery } from '@nestjs/swagger';

import {
  CreateMembershipCardDto,
  UpdateMembershipCardDto,
  QueryMembershipCardDto,
  MembershipCardResponseDto,
  MembershipCardStatus,
  CardBatchStatsDto,
  ActivateMembershipCardDto,
  ExportMembershipCardDto,
} from './dto/membership-card.dto';
import { MembershipCardService } from './membership-card.service';

import { BaseController } from '@/core/common/base/base.controller';
import { ApiPaginatedResponse } from '@/core/decorators/api-paginated-response.decorator';
import { CurrentTenant } from '@/core/decorators/current-tenant.decorator';
import { PaginationOptions } from '@/core/pagination-options/pagination-options';

/**
 * 租户会员卡控制器
 * 处理租户会员卡的管理
 */
@ApiTags('租户会员卡管理')
@Controller('tenant/membership/cards')
export class MembershipCardController extends BaseController {
  constructor(private readonly membershipCardService: MembershipCardService) {
    super();
  }

  @Post()
  @ApiOperation({ summary: '创建会员卡批次' })
  async createBatch(@CurrentTenant() tenantId: string, @Body() createDto: CreateMembershipCardDto) {
    const data = await this.membershipCardService.createBatch(createDto, tenantId);
    return this.success(data, '创建会员卡批次成功');
  }

  @Get('list')
  @ApiOperation({ summary: '查询会员卡列表' })
  @ApiPaginatedResponse(MembershipCardResponseDto)
  @ApiQuery({ name: 'planId', type: Number, required: false })
  @ApiQuery({ name: 'status', enum: MembershipCardStatus, required: false })
  @ApiQuery({ name: 'batchNote', type: String, required: false })
  @ApiQuery({ name: 'startDate', type: String, required: false })
  @ApiQuery({ name: 'endDate', type: String, required: false })
  @ApiQuery({ name: 'page', type: Number, required: false })
  @ApiQuery({ name: 'pageSize', type: Number, required: false })
  async findAll(
    @CurrentTenant() tenantId: string,
    @Query('planId', new DefaultValuePipe(0), ParseIntPipe) planId: number = 0,
    @Query('status') status?: MembershipCardStatus,
    @Query('batchNote') batchNote?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number = 1,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe) pageSize: number = 10,
  ) {
    const options = new PaginationOptions(pageSize, (page - 1) * pageSize, page, pageSize);
    const queryDto: QueryMembershipCardDto = {
      planId: planId || undefined,
      status,
      batchNote,
      startDate,
      endDate,
      page,
      pageSize,
    };
    const data = await this.membershipCardService.findAll(queryDto, options, tenantId);
    return this.success(data);
  }

  @Get('stats')
  @ApiOperation({ summary: '查询会员卡批次统计' })
  @ApiOkResponse({ type: [CardBatchStatsDto] })
  async getBatchStats(@CurrentTenant() tenantId: string) {
    const data = await this.membershipCardService.getBatchStats(tenantId);
    return this.success(data);
  }

  @Put(':id')
  @ApiOperation({ summary: '更新会员卡' })
  @ApiOkResponse({ type: MembershipCardResponseDto })
  async update(
    @CurrentTenant() tenantId: string,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateMembershipCardDto,
  ) {
    const data = await this.membershipCardService.update(id, updateDto, tenantId);
    return this.success(data, '更新会员卡成功');
  }

  @Post('export')
  @ApiOperation({ summary: '导出会员卡' })
  async exportCards(@CurrentTenant() tenantId: string, @Body() exportDto: ExportMembershipCardDto) {
    const data = await this.membershipCardService.exportCards(exportDto, tenantId);
    return this.success(data);
  }
}

/**
 * 用户会员卡控制器
 * 处理用户会员卡的激活
 */
@ApiTags('用户会员卡')
@Controller('membership/cards')
export class UserMembershipCardController extends BaseController {
  constructor(private readonly membershipCardService: MembershipCardService) {
    super();
  }

  @Post('activate')
  @ApiOperation({ summary: '激活会员卡' })
  async activate(
    @CurrentTenant() tenantId: string,
    @Body() activateDto: ActivateMembershipCardDto,
  ) {
    const data = await this.membershipCardService.activateCard(activateDto, tenantId);
    return this.success(data, '激活会员卡成功');
  }
}
