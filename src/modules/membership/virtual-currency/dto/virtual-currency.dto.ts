import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsNumber,
  IsEnum,
  IsOptional,
  IsBoolean,
  IsObject,
  Min,
  IsArray,
  IsDecimal,
} from 'class-validator';

/**
 * 虚拟币交易类型枚举
 */
export enum TransactionType {
  PURCHASE = 'purchase', // 购买
  CONSUME = 'consume', // 消费
  GIFT = 'gift', // 赠送
  REFUND = 'refund', // 退款
}

/**
 * 创建虚拟币类型DTO
 */
export class CreateVirtualCurrencyTypeDto {
  @ApiProperty({ description: '虚拟币代码', example: 'ai_coin' })
  @IsString()
  code: string;

  @ApiProperty({ description: '虚拟币名称', example: 'AI币' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: '虚拟币描述', example: '用于AI功能消费的虚拟币' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: '虚拟币图标', example: 'coin-icon' })
  @IsOptional()
  @IsString()
  icon?: string;

  @ApiProperty({ description: '兑换比率', example: 1 })
  @IsNumber()
  @Min(0)
  exchangeRate: number;

  @ApiPropertyOptional({ description: '是否激活', default: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({
    description: '元数据',
    example: { color: '#FFD700', displayFormat: '{value}个' },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * 更新虚拟币类型DTO
 */
export class UpdateVirtualCurrencyTypeDto extends PartialType(CreateVirtualCurrencyTypeDto) {}

/**
 * 查询虚拟币类型DTO
 */
export class QueryVirtualCurrencyTypeDto {
  @ApiPropertyOptional({ description: '是否激活' })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isActive?: boolean;

  @ApiPropertyOptional({ description: '页码', default: 1 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page?: number;

  @ApiPropertyOptional({ description: '每页数量', default: 10 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  pageSize?: number;
}

/**
 * 虚拟币类型响应DTO
 */
export class VirtualCurrencyTypeResponseDto {
  @ApiProperty({ description: '虚拟币类型ID' })
  id: number;

  @ApiProperty({ description: '租户ID' })
  tenantId: string;

  @ApiProperty({ description: '虚拟币代码' })
  code: string;

  @ApiProperty({ description: '虚拟币名称' })
  name: string;

  @ApiProperty({ description: '虚拟币描述' })
  description: string;

  @ApiProperty({ description: '虚拟币图标' })
  icon?: string;

  @ApiProperty({ description: '兑换比率' })
  exchangeRate: number;

  @ApiProperty({ description: '是否激活' })
  isActive: boolean;

  @ApiProperty({ description: '元数据' })
  metadata?: Record<string, any>;

  @ApiProperty({ description: '创建时间' })
  createTime: string;

  @ApiProperty({ description: '更新时间' })
  updateTime: string;
}

/**
 * 用户充值虚拟币DTO
 */
export class RechargeVirtualCurrencyDto {
  @ApiProperty({ description: '用户ID' })
  @IsNumber()
  userId: number;

  @ApiProperty({ description: '虚拟币类型ID' })
  @IsNumber()
  currencyTypeId: number;

  @ApiProperty({ description: '充值金额' })
  @IsNumber()
  @Min(0.01)
  amount: number;

  @ApiPropertyOptional({ description: '备注' })
  @IsOptional()
  @IsString()
  remark?: string;

  @ApiPropertyOptional({ description: '相关订单号' })
  @IsOptional()
  @IsString()
  orderNo?: string;

  @ApiPropertyOptional({ description: '元数据' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * 用户消费虚拟币DTO
 */
export class ConsumeVirtualCurrencyDto {
  @ApiProperty({ description: '用户ID' })
  @IsNumber()
  userId: number;

  @ApiProperty({ description: '虚拟币类型ID' })
  @IsNumber()
  currencyTypeId: number;

  @ApiProperty({ description: '消费金额' })
  @IsNumber()
  @Min(0.01)
  amount: number;

  @ApiProperty({ description: '消费描述' })
  @IsString()
  description: string;

  @ApiPropertyOptional({ description: '相关功能代码' })
  @IsOptional()
  @IsString()
  featureCode?: string;

  @ApiPropertyOptional({ description: '元数据' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * 查询用户钱包DTO
 */
export class QueryUserWalletDto {
  @ApiProperty({ description: '用户ID' })
  @IsNumber()
  userId: number;

  @ApiPropertyOptional({ description: '虚拟币类型ID' })
  @IsOptional()
  @IsNumber()
  currencyTypeId?: number;
}

/**
 * 钱包余额响应DTO
 */
export class WalletBalanceResponseDto {
  @ApiProperty({ description: '钱包ID' })
  id: number;

  @ApiProperty({ description: '用户ID' })
  userId: number;

  @ApiProperty({ description: '虚拟币类型ID' })
  currencyTypeId: number;

  @ApiProperty({ description: '虚拟币代码' })
  currencyCode: string;

  @ApiProperty({ description: '虚拟币名称' })
  currencyName: string;

  @ApiProperty({ description: '余额' })
  balance: number;

  @ApiProperty({ description: '虚拟币图标' })
  icon?: string;

  @ApiProperty({ description: '元数据' })
  metadata?: Record<string, any>;
}

/**
 * 查询交易记录DTO
 */
export class QueryTransactionDto {
  @ApiProperty({ description: '用户ID' })
  @IsNumber()
  userId: number;

  @ApiPropertyOptional({ description: '虚拟币类型ID' })
  @IsOptional()
  @IsNumber()
  currencyTypeId?: number;

  @ApiPropertyOptional({
    description: '交易类型',
    enum: TransactionType,
  })
  @IsOptional()
  @IsEnum(TransactionType)
  type?: TransactionType;

  @ApiPropertyOptional({ description: '开始时间', example: '2023-01-01' })
  @IsOptional()
  @IsString()
  startTime?: string;

  @ApiPropertyOptional({ description: '结束时间', example: '2023-12-31' })
  @IsOptional()
  @IsString()
  endTime?: string;

  @ApiPropertyOptional({ description: '页码', default: 1 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page?: number;

  @ApiPropertyOptional({ description: '每页数量', default: 10 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  pageSize?: number;
}

/**
 * 交易记录响应DTO
 */
export class TransactionResponseDto {
  @ApiProperty({ description: '交易ID' })
  id: number;

  @ApiProperty({ description: '钱包ID' })
  walletId: number;

  @ApiProperty({ description: '虚拟币类型ID' })
  currencyTypeId: number;

  @ApiProperty({ description: '虚拟币代码' })
  currencyCode: string;

  @ApiProperty({ description: '虚拟币名称' })
  currencyName: string;

  @ApiProperty({ description: '交易金额' })
  amount: number;

  @ApiProperty({ description: '交易后余额' })
  balance: number;

  @ApiProperty({
    description: '交易类型',
    enum: TransactionType,
  })
  type: TransactionType;

  @ApiProperty({ description: '交易描述' })
  description: string;

  @ApiProperty({ description: '元数据' })
  metadata?: Record<string, any>;

  @ApiProperty({ description: '创建时间' })
  createTime: string;
}

/**
 * 功能币消耗配置DTO
 */
export class FeatureCurrencyCostDto {
  @ApiProperty({ description: '虚拟币代码' })
  @IsString()
  currencyCode: string;

  @ApiProperty({ description: '基础消耗量' })
  @IsNumber()
  @Min(0)
  cost: number;

  @ApiPropertyOptional({
    description: '会员折扣，key为会员计划代码，value为折扣率',
    example: { basic: 0.9, pro: 0.7, enterprise: 0.5 },
  })
  @IsOptional()
  @IsObject()
  memberDiscounts?: Record<string, number>;

  @ApiPropertyOptional({
    description: '批量购买折扣',
    example: [
      { quantity: 10, discount: 0.9 },
      { quantity: 50, discount: 0.8 },
    ],
  })
  @IsOptional()
  @IsArray()
  bulkDiscounts?: Array<{ quantity: number; discount: number }>;

  @ApiPropertyOptional({ description: '是否激活', default: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}
