import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';

/**
 * 查询部门DTO
 */
export class QueryDepartmentDto {
  @ApiProperty({ description: '部门名称', required: false })
  @IsString({ message: '部门名称必须是字符串' })
  @IsOptional()
  name?: string;

  @ApiProperty({ description: '部门状态：0-禁用，1-启用', required: false })
  @IsNumber({}, { message: '状态必须是数字' })
  @IsOptional()
  status?: number;
}
