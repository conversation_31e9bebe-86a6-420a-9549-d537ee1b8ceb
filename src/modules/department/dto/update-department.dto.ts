import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsN<PERSON>ber, IsOptional, IsString, Min } from 'class-validator';

/**
 * 更新部门DTO
 */
export class UpdateDepartmentDto {
  @ApiProperty({ description: '部门名称' })
  @IsNotEmpty({ message: '部门名称不能为空' })
  @IsString({ message: '部门名称必须是字符串' })
  name: string;

  @ApiProperty({ description: '父部门ID，0表示顶级部门', required: false, nullable: true })
  @IsNumber({}, { message: '父部门ID必须是数字' })
  @Min(0, { message: '父部门ID不能小于0' })
  @IsOptional()
  pid?: number | null;

  @ApiProperty({ description: '部门状态：0-禁用，1-启用' })
  @IsNumber({}, { message: '状态必须是数字' })
  status: number;

  @ApiProperty({ description: '排序号' })
  @IsNumber({}, { message: '排序号必须是数字' })
  orderNo: number;

  @ApiProperty({ description: '备注', required: false })
  @IsString({ message: '备注必须是字符串' })
  @IsOptional()
  remark?: string;
}
