import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, Min } from 'class-validator';

/**
 * 创建部门DTO
 */
export class CreateDepartmentDto {
  @ApiProperty({ description: '部门名称' })
  @IsNotEmpty({ message: '部门名称不能为空' })
  @IsString({ message: '部门名称必须是字符串' })
  name: string;

  @ApiProperty({ description: '父部门ID，0表示顶级部门', default: 0 })
  @IsNumber({}, { message: '父部门ID必须是数字' })
  @Min(0, { message: '父部门ID不能小于0' })
  pid: number = 0;

  @ApiProperty({ description: '部门状态：0-禁用，1-启用', default: 1 })
  @IsNumber({}, { message: '状态必须是数字' })
  @IsOptional()
  status?: number = 1;

  @ApiProperty({ description: '排序号', default: 0 })
  @IsNumber({}, { message: '排序号必须是数字' })
  @IsOptional()
  orderNo?: number = 0;

  @ApiProperty({ description: '备注', required: false })
  @IsString({ message: '备注必须是字符串' })
  @IsOptional()
  remark?: string;
}
