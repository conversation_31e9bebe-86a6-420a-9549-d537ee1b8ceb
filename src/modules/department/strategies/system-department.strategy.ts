import { Injectable, Logger } from '@nestjs/common';

import { DepartmentStrategyType } from './department-strategy.factory';
import { IDepartmentStrategy } from './department-strategy.interface';
import { CreateDepartmentDto } from '../dto/create-department.dto';
import { DepartmentDto, DepartmentTreeDto, DepartmentListItemDto } from '../dto/department.dto';
import { QueryDepartmentDto } from '../dto/query-department.dto';
import { UpdateDepartmentStatusDto } from '../dto/update-department-status.dto';
import { UpdateDepartmentDto } from '../dto/update-department.dto';

import { ResourceExistsException } from '@/core/common/exceptions/resource-exists.exception';
import { ResourceNotFoundException } from '@/core/common/exceptions/resource-not-found.exception';
import { ValidationException } from '@/core/common/exceptions/validation.exception';
import { DateFormatUtil } from '@/core/common/utils/date-format.util';
import { PublicPrismaService } from '@/core/database/prisma/public-prisma.service';

/**
 * 系统部门策略
 * 处理系统部门相关操作
 */
@Injectable()
export class SystemDepartmentStrategy implements IDepartmentStrategy {
  private readonly logger = new Logger(SystemDepartmentStrategy.name);

  constructor(private readonly prisma: PublicPrismaService) {
    this.logger.log('SystemDepartmentStrategy 已创建');
    if (!this.prisma) {
      this.logger.error('PublicPrismaService 未注入');
    } else {
      this.logger.log('PublicPrismaService 已注入');

      // 检查数据库连接
      this.checkDatabaseConnection();
    }
  }

  /**
   * 检查数据库连接并输出表信息
   */
  private async checkDatabaseConnection() {
    try {
      // 检查数据库连接
      const result = await this.prisma.$queryRaw`SELECT 1 as connected`;
      this.logger.log(`数据库连接检查: ${JSON.stringify(result)}`);

      // 尝试查询部门表
      const departments = await this.prisma.systemDepartment.findMany({
        take: 10,
      });

      departments.forEach(dept => {
        this.logger.log(
          `部门: ID=${dept.id}, 名称=${dept.name}, 父ID=${dept.pid}, 状态=${dept.status}`,
        );
      });
    } catch (error) {
      this.logger.error(`数据库连接检查失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取策略类型
   * @returns 策略类型
   */
  getType(): string {
    return DepartmentStrategyType.SYSTEM;
  }

  /**
   * 创建系统部门
   * @param createDepartmentDto 创建部门数据
   * @returns 创建的部门
   */
  async create(createDepartmentDto: CreateDepartmentDto): Promise<DepartmentDto> {
    try {
      // 验证父部门是否存在
      if (createDepartmentDto.pid > 0) {
        const parentExists = await this.prisma.systemDepartment.findUnique({
          where: {
            id: createDepartmentDto.pid,
          },
        });

        if (!parentExists) {
          throw new ResourceNotFoundException('父部门', createDepartmentDto.pid);
        }
      }

      // 验证部门名称在同一父部门下是否已存在
      const nameExists = await this.prisma.systemDepartment.findFirst({
        where: {
          name: createDepartmentDto.name,
          pid: createDepartmentDto.pid,
        },
      });

      if (nameExists) {
        throw new ResourceExistsException('部门', '名称', createDepartmentDto.name);
      }

      // 创建部门
      // 如果没有传pid或pid为0，设置为null（表示顶级部门）
      const pid = createDepartmentDto.pid === 0 ? null : createDepartmentDto.pid;

      // 创建部门
      const department = await this.prisma.systemDepartment.create({
        data: {
          name: createDepartmentDto.name,
          pid: pid,
          status: createDepartmentDto.status ?? 1,
          orderNo: createDepartmentDto.orderNo ?? 0,
          remark: createDepartmentDto.remark,
        },
      });

      // 返回时将pid为null的部门转换为pid为0的部门
      return {
        id: department.id,
        name: department.name,
        pid: department.pid === null ? 0 : department.pid,
        status: department.status,
        orderNo: department.orderNo,
        createTime: DateFormatUtil.formatToDateTime(department.createdAt),
        updateTime: DateFormatUtil.formatToDateTime(department.updatedAt),
        remark: department.remark,
      };
    } catch (error) {
      this.logger.error(`创建系统部门失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取系统部门树形列表
   * @param query 查询条件
   * @returns 部门树形列表
   */
  async findTree(query: QueryDepartmentDto): Promise<DepartmentTreeDto[]> {
    try {
      // 构建查询条件
      const where: any = {};

      // 如果明确指定了 status，则添加到查询条件
      if (query.status !== undefined) {
        where.status = query.status;
      }

      if (query.name) {
        where.name = query.name;
      }

      // 查询所有部门
      const departments = await this.prisma.systemDepartment.findMany({
        where,
        orderBy: [{ pid: 'asc' }, { orderNo: 'asc' }, { createdAt: 'asc' }],
      });

      // 转换为DTO，将pid为null或1的部门转换为pid为0的部门
      const deptDtos = departments.map(dept => ({
        id: dept.id,
        name: dept.name,
        pid: dept.pid === null || dept.pid === 1 ? 0 : dept.pid,
        status: dept.status,
        orderNo: dept.orderNo,
        createTime: DateFormatUtil.formatToDateTime(dept.createdAt),
        updateTime: DateFormatUtil.formatToDateTime(dept.updatedAt),
        remark: dept.remark,
        children: [],
      }));

      // 构建树形结构
      return this.buildDepartmentTree(deptDtos);
    } catch (error) {
      this.logger.error(`获取系统部门树形列表失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取系统部门列表（扁平结构）
   * @param query 查询条件
   * @returns 部门列表
   */
  async findList(query: QueryDepartmentDto): Promise<DepartmentListItemDto[]> {
    try {
      // 检查 prisma.systemDepartment 是否存在
      if (!this.prisma['systemDepartment']) {
        this.logger.warn('系统部门表不存在，返回空列表');
        return [];
      }

      // 构建查询条件
      const where: any = {};

      // 默认包含所有状态
      // 注意：不要使用复杂的条件，直接使用简单的等于条件

      // 如果明确指定了 status，则覆盖默认条件
      if (query.status) {
        where.status = query.status;
      }

      try {
        // 记录查询条件
        this.logger.log(`查询条件: ${JSON.stringify(where)}`);

        // 先检查表中是否有数据
        const count = await this.prisma.systemDepartment.count();
        this.logger.log(`部门表中共有 ${count} 条记录`);

        // 查询所有部门，包括根部门
        const departments = await this.prisma.systemDepartment.findMany({
          where,
          orderBy: [{ pid: 'asc' }, { orderNo: 'asc' }, { createdAt: 'asc' }],
        });

        // 记录查询结果
        this.logger.log(`查询到 ${departments.length} 个部门`);
        departments.forEach(dept => {
          this.logger.log(
            `部门ID: ${dept.id}, 名称: ${dept.name}, 父ID: ${dept.pid}, 状态: ${dept.status}`,
          );
        });

        // 转换为DTO，将pid为null的部门转换为pid为0的部门
        const deptDtos = departments.map(dept => ({
          id: dept.id,
          name: dept.name,
          pid: dept.pid === null ? 0 : dept.pid,
          status: dept.status,
          orderNo: dept.orderNo,
          createTime: DateFormatUtil.formatToDateTime(dept.createdAt),
          updateTime: DateFormatUtil.formatToDateTime(dept.updatedAt),
          remark: dept.remark,
          children: [],
        }));

        // 构建树形结构
        return this.buildDepartmentTree(deptDtos);
      } catch (queryError) {
        this.logger.error(`查询系统部门失败: ${queryError.message}`);
        // 查询失败时返回空数组，而不是模拟数据
        return [];
      }
    } catch (error) {
      this.logger.error(`获取系统部门列表失败: ${error.message}`, error.stack);
      // 发生错误时返回空数组，而不是模拟数据
      return [];
    }
  }

  /**
   * 获取系统部门详情
   * @param id 部门ID
   * @returns 部门详情
   */
  async findOne(id: number): Promise<DepartmentDto> {
    try {
      const department = await this.prisma.systemDepartment.findUnique({
        where: {
          id,
        },
      });

      if (!department) {
        throw new ResourceNotFoundException('部门', id);
      }

      // 返回时将pid为null的部门转换为pid为0的部门
      return {
        id: department.id,
        name: department.name,
        pid: department.pid === null ? 0 : department.pid,
        status: department.status,
        orderNo: department.orderNo,
        createTime: DateFormatUtil.formatToDateTime(department.createdAt),
        updateTime: DateFormatUtil.formatToDateTime(department.updatedAt),
        remark: department.remark,
      };
    } catch (error) {
      this.logger.error(`获取系统部门详情失败，ID: ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 更新系统部门
   * @param id 部门ID
   * @param updateDepartmentDto 更新部门数据
   * @returns 更新后的部门
   */
  async update(id: number, updateDepartmentDto: UpdateDepartmentDto): Promise<DepartmentDto> {
    try {
      // 检查部门是否存在
      const department = await this.prisma.systemDepartment.findUnique({
        where: {
          id,
        },
      });

      if (!department) {
        throw new ResourceNotFoundException('部门', id);
      }

      // 如果pid为0，设置为null（表示顶级部门）
      if (updateDepartmentDto.pid === 0) {
        updateDepartmentDto.pid = null;
      }

      // 验证父部门是否存在
      if (updateDepartmentDto.pid !== null && updateDepartmentDto.pid !== undefined) {
        // 确保pid是一个有效的数字
        const pidValue = Number(updateDepartmentDto.pid);
        if (!isNaN(pidValue) && pidValue > 0) {
          const parentExists = await this.prisma.systemDepartment.findUnique({
            where: {
              id: pidValue,
            },
          });

          if (!parentExists) {
            throw new ResourceNotFoundException('父部门', pidValue);
          }

          // 不能将部门的父部门设置为自己或其子部门
          if (pidValue === id) {
            throw new ValidationException('不能将部门的父部门设置为自己');
          }

          // 检查是否形成循环依赖
          const childDepts = await this.findChildDepartments(id);
          if (childDepts.some(dept => dept.id === pidValue)) {
            throw new ValidationException('不能将部门的父部门设置为其子部门');
          }
        }
      }

      // 验证部门名称在同一父部门下是否已存在（排除自己）
      const whereCondition: any = {
        name: updateDepartmentDto.name,
        id: { not: id },
      };

      // 只有当pid有明确值时才添加到查询条件中
      if (updateDepartmentDto.pid !== undefined) {
        whereCondition.pid = updateDepartmentDto.pid;
      }

      const nameExists = await this.prisma.systemDepartment.findFirst({
        where: whereCondition,
      });

      if (nameExists) {
        throw new ResourceExistsException('部门', '名称', updateDepartmentDto.name);
      }

      // 准备更新数据
      const updateData: any = {};

      if (updateDepartmentDto.name !== undefined) {
        updateData.name = updateDepartmentDto.name;
      }

      if (updateDepartmentDto.pid !== undefined) {
        updateData.pid = updateDepartmentDto.pid;
      }

      if (updateDepartmentDto.status !== undefined) {
        updateData.status = updateDepartmentDto.status;
      }

      if (updateDepartmentDto.orderNo !== undefined) {
        updateData.orderNo = updateDepartmentDto.orderNo;
      }

      if (updateDepartmentDto.remark !== undefined) {
        updateData.remark = updateDepartmentDto.remark;
      }

      // 更新部门
      const updatedDepartment = await this.prisma.systemDepartment.update({
        where: { id },
        data: updateData,
      });

      // 返回时将pid为null的部门转换为pid为0的部门
      return {
        id: updatedDepartment.id,
        name: updatedDepartment.name,
        pid: updatedDepartment.pid === null ? 0 : updatedDepartment.pid,
        status: updatedDepartment.status,
        orderNo: updatedDepartment.orderNo,
        createTime: DateFormatUtil.formatToDateTime(updatedDepartment.createdAt),
        updateTime: DateFormatUtil.formatToDateTime(updatedDepartment.updatedAt),
        remark: updatedDepartment.remark,
      };
    } catch (error) {
      this.logger.error(`更新系统部门失败，ID: ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 更新系统部门状态
   * @param id 部门ID
   * @param updateStatusDto 更新状态数据
   * @returns 更新结果
   */
  async updateStatus(
    id: number,
    updateStatusDto: UpdateDepartmentStatusDto,
  ): Promise<{ id: number; status: number }> {
    try {
      // 检查部门是否存在
      const department = await this.prisma.systemDepartment.findUnique({
        where: {
          id,
        },
      });

      if (!department) {
        throw new ResourceNotFoundException('部门', id);
      }

      // 更新部门状态
      const updatedDepartment = await this.prisma.systemDepartment.update({
        where: { id },
        data: {
          status: updateStatusDto.status,
        },
        select: {
          id: true,
          status: true,
        },
      });

      return updatedDepartment;
    } catch (error) {
      this.logger.error(`更新系统部门状态失败，ID: ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 删除系统部门
   * @param id 部门ID
   * @returns 删除结果
   */
  async remove(id: number): Promise<{ success: boolean }> {
    try {
      // 检查部门是否存在
      const department = await this.prisma.systemDepartment.findUnique({
        where: {
          id,
        },
      });

      if (!department) {
        throw new ResourceNotFoundException('部门', id);
      }

      // 检查是否有子部门
      const hasChildren = await this.prisma.systemDepartment.findFirst({
        where: {
          pid: id,
        },
      });

      if (hasChildren) {
        throw new ValidationException('该部门下有子部门，不能删除');
      }

      // 删除部门
      await this.prisma.systemDepartment.delete({
        where: { id },
      });

      return { success: true };
    } catch (error) {
      this.logger.error(`删除系统部门失败，ID: ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 检查系统部门名称是否存在
   * @param name 部门名称
   * @param pid 父部门ID
   * @param _tenantId 租户ID（不使用，但保留参数以兼容接口）
   * @param excludeId 排除的部门ID（可选）
   * @returns 是否存在
   */
  async checkNameExists(
    name: string,
    pid: number,
    _tenantId?: string,
    excludeId?: number,
  ): Promise<boolean> {
    // 忽略_tenantId参数，仅用于接口兼容
    try {
      // 如果pid为0，设置为null（表示顶级部门）
      const actualPid = pid === 0 ? null : pid;

      const where: any = {
        name,
        pid: actualPid,
      };

      if (excludeId) {
        where.id = { not: excludeId };
      }

      const department = await this.prisma.systemDepartment.findFirst({
        where,
      });

      return !!department;
    } catch (error) {
      this.logger.error(
        `检查系统部门名称是否存在失败，名称: ${name}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * 构建部门树形结构
   * @param departments 部门列表
   * @returns 树形结构
   */
  private buildDepartmentTree(departments: DepartmentTreeDto[]): DepartmentTreeDto[] {
    // 创建一个映射表，用于快速查找部门
    const deptMap = new Map<number, DepartmentTreeDto>();
    departments.forEach(dept => deptMap.set(dept.id, dept));

    // 构建树形结构
    const tree: DepartmentTreeDto[] = [];

    departments.forEach(dept => {
      // 如果是顶级部门（pid为0）或者找不到父部门，直接添加到树中
      if (dept.pid === 0 || !deptMap.has(dept.pid)) {
        tree.push(dept);
      } else {
        // 否则，添加到父部门的children中
        const parent = deptMap.get(dept.pid);
        if (parent) {
          parent.children.push(dept);
        } else {
          // 如果找不到父部门，作为顶级节点
          tree.push(dept);
        }
      }
    });

    return tree;
  }

  /**
   * 查找部门的所有子部门
   * @param departmentId 部门ID
   * @returns 子部门列表
   */
  private async findChildDepartments(departmentId: number): Promise<{ id: number }[]> {
    const children = await this.prisma.systemDepartment.findMany({
      where: {
        pid: departmentId,
      },
      select: {
        id: true,
      },
    });

    const result = [...children];

    // 递归查找子部门的子部门
    for (const child of children) {
      const grandChildren = await this.findChildDepartments(child.id);
      result.push(...grandChildren);
    }

    return result;
  }
}
