import { <PERSON><PERSON><PERSON> } from '@nestjs/common';

import { DepartmentStrategyProxy } from './department-strategy.proxy';
import { DepartmentController } from './department.controller';
import { DepartmentService } from './department.service';
import { DepartmentStrategyFactory } from './strategies/department-strategy.factory';
import { SystemDepartmentStrategy } from './strategies/system-department.strategy';
import { TenantDepartmentStrategy } from './strategies/tenant-department.strategy';

import { CommonModule } from '@/core/common/common.module';
import { PrismaModule } from '@/core/database/prisma/prisma.module';
import { PublicPrismaService } from '@/core/database/prisma/public-prisma.service';

@Module({
  imports: [PrismaModule, CommonModule],
  controllers: [DepartmentController],
  providers: [
    DepartmentService,
    DepartmentStrategyFactory,
    SystemDepartmentStrategy,
    TenantDepartmentStrategy,
    DepartmentStrategyProxy,
  ],
  exports: [DepartmentService],
})
export class DepartmentModule {}
