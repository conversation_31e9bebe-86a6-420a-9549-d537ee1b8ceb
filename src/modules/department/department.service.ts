import { Injectable, OnModuleInit } from '@nestjs/common';

import { DepartmentStrategyProxy } from './department-strategy.proxy';
import { CreateDepartmentDto } from './dto/create-department.dto';
import { DepartmentDto, DepartmentTreeDto, DepartmentListItemDto } from './dto/department.dto';
import { QueryDepartmentDto } from './dto/query-department.dto';
import { UpdateDepartmentStatusDto } from './dto/update-department-status.dto';
import { UpdateDepartmentDto } from './dto/update-department.dto';
import { DepartmentStrategyFactory } from './strategies/department-strategy.factory';
import { SystemDepartmentStrategy } from './strategies/system-department.strategy';
import { TenantDepartmentStrategy } from './strategies/tenant-department.strategy';

import { BaseService } from '@/core/common/base/base.service';
import { QueryBuilderService } from '@/core/common/services/query-builder.service';

@Injectable()
export class DepartmentService extends BaseService implements OnModuleInit {
  constructor(
    private readonly departmentStrategyFactory: DepartmentStrategyFactory,
    private readonly systemDepartmentStrategy: SystemDepartmentStrategy,
    private readonly tenantDepartmentStrategy: TenantDepartmentStrategy,
    private readonly queryBuilderService: QueryBuilderService,
    private readonly departmentStrategyProxy: DepartmentStrategyProxy,
  ) {
    super(DepartmentService.name);
  }

  /**
   * 模块初始化时注册策略
   */
  onModuleInit() {
    // 注册策略
    this.departmentStrategyFactory.register(this.systemDepartmentStrategy);
    this.departmentStrategyFactory.register(this.tenantDepartmentStrategy);

    // 记录已注册的策略
    this.logger.log(
      `已注册的部门策略: ${Array.from(this.departmentStrategyFactory['strategies'].keys()).join(', ')}`,
      'DepartmentService',
    );
  }

  /**
   * 创建部门
   * @param createDepartmentDto 创建部门数据
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @returns 创建的部门
   */
  async create(
    createDepartmentDto: CreateDepartmentDto,
    userType: string,
    tenantId?: string,
  ): Promise<DepartmentDto> {
    this.logger.log(`创建部门，用户类型: ${userType}, 租户ID: ${tenantId || 'system'}`);

    // 验证用户类型
    if (!userType) {
      this.validationError('用户类型不能为空');
    }

    // 验证租户用户必须提供租户ID
    if (userType !== 'SYSTEM' && !tenantId) {
      this.validationError('租户用户必须提供租户ID');
    }

    // 使用代理执行策略方法
    return await this.departmentStrategyProxy.execute<DepartmentDto>(
      userType,
      'create',
      userType === 'SYSTEM' ? [createDepartmentDto] : [createDepartmentDto, tenantId],
      tenantId,
    );
  }

  /**
   * 获取部门树形列表
   * @param query 查询条件
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @returns 部门树形列表
   */
  async findTree(
    query: QueryDepartmentDto,
    userType: string,
    tenantId?: string,
  ): Promise<DepartmentTreeDto[]> {
    this.logger.log(`获取部门树形列表，用户类型: ${userType}, 租户ID: ${tenantId || 'system'}`);

    // 验证用户类型
    if (!userType) {
      this.validationError('用户类型不能为空');
    }

    // 验证租户用户必须提供租户ID
    if (userType !== 'SYSTEM' && !tenantId) {
      this.validationError('租户用户必须提供租户ID');
    }

    // 使用代理执行策略方法
    return await this.departmentStrategyProxy.execute<DepartmentTreeDto[]>(
      userType,
      'findTree',
      userType === 'SYSTEM' ? [query] : [query, tenantId],
      tenantId,
    );
  }

  /**
   * 获取部门列表（扁平结构）
   * @param query 查询条件
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @returns 部门列表
   */
  async findList(
    query: QueryDepartmentDto,
    userType: string,
    tenantId?: string,
  ): Promise<DepartmentListItemDto[]> {
    this.logger.log(`获取部门列表，用户类型: ${userType}, 租户ID: ${tenantId || 'system'}`);

    // 验证用户类型
    if (!userType) {
      this.validationError('用户类型不能为空');
    }

    // 验证租户用户必须提供租户ID
    if (userType !== 'SYSTEM' && !tenantId) {
      this.validationError('租户用户必须提供租户ID');
    }

    // 使用代理执行策略方法
    return await this.departmentStrategyProxy.execute<DepartmentListItemDto[]>(
      userType,
      'findList',
      userType === 'SYSTEM' ? [query] : [query, tenantId],
      tenantId,
    );
  }

  /**
   * 获取部门详情
   * @param id 部门ID
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @returns 部门详情
   */
  async findOne(id: number, userType: string, tenantId?: string): Promise<DepartmentDto> {
    this.logger.log(
      `获取部门详情，ID: ${id}, 用户类型: ${userType}, 租户ID: ${tenantId || 'system'}`,
    );

    // 验证用户ID
    if (!id) {
      this.validationError('部门ID不能为空');
    }

    // 验证用户类型
    if (!userType) {
      this.validationError('用户类型不能为空');
    }

    // 验证租户用户必须提供租户ID
    if (userType !== 'SYSTEM' && !tenantId) {
      this.validationError('租户用户必须提供租户ID');
    }

    // 使用代理执行策略方法
    const department = await this.departmentStrategyProxy.execute<DepartmentDto>(
      userType,
      'findOne',
      userType === 'SYSTEM' ? [id] : [id, tenantId],
      tenantId,
    );

    if (!department) {
      this.notFound('部门', id);
    }

    return department;
  }

  /**
   * 更新部门
   * @param id 部门ID
   * @param updateDepartmentDto 更新部门数据
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @returns 更新后的部门
   */
  async update(
    id: number,
    updateDepartmentDto: UpdateDepartmentDto,
    userType: string,
    tenantId?: string,
  ): Promise<DepartmentDto> {
    this.logger.log(`更新部门，ID: ${id}, 用户类型: ${userType}, 租户ID: ${tenantId || 'system'}`);

    // 验证部门ID
    if (!id) {
      this.validationError('部门ID不能为空');
    }

    // 验证用户类型
    if (!userType) {
      this.validationError('用户类型不能为空');
    }

    // 验证租户用户必须提供租户ID
    if (userType !== 'SYSTEM' && !tenantId) {
      this.validationError('租户用户必须提供租户ID');
    }

    // 检查部门是否存在
    await this.findOne(id, userType, tenantId);

    // 使用代理执行策略方法
    return await this.departmentStrategyProxy.execute<DepartmentDto>(
      userType,
      'update',
      userType === 'SYSTEM' ? [id, updateDepartmentDto] : [id, updateDepartmentDto, tenantId],
      tenantId,
    );
  }

  /**
   * 更新部门状态
   * @param id 部门ID
   * @param updateStatusDto 更新状态数据
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @returns 更新结果
   */
  async updateStatus(
    id: number,
    updateStatusDto: UpdateDepartmentStatusDto,
    userType: string,
    tenantId?: string,
  ): Promise<{ id: number; status: number }> {
    this.logger.log(
      `更新部门状态，ID: ${id}, 用户类型: ${userType}, 租户ID: ${tenantId || 'system'}`,
    );

    // 验证部门ID
    if (!id) {
      this.validationError('部门ID不能为空');
    }

    // 验证用户类型
    if (!userType) {
      this.validationError('用户类型不能为空');
    }

    // 验证租户用户必须提供租户ID
    if (userType !== 'SYSTEM' && !tenantId) {
      this.validationError('租户用户必须提供租户ID');
    }

    // 检查部门是否存在
    await this.findOne(id, userType, tenantId);

    // 使用代理执行策略方法
    return await this.departmentStrategyProxy.execute<{ id: number; status: number }>(
      userType,
      'updateStatus',
      userType === 'SYSTEM' ? [id, updateStatusDto] : [id, updateStatusDto, tenantId],
      tenantId,
    );
  }

  /**
   * 删除部门
   * @param id 部门ID
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @returns 删除结果
   */
  async remove(id: number, userType: string, tenantId?: string): Promise<{ success: boolean }> {
    this.logger.log(`删除部门，ID: ${id}, 用户类型: ${userType}, 租户ID: ${tenantId || 'system'}`);

    // 验证部门ID
    if (!id) {
      this.validationError('部门ID不能为空');
    }

    // 验证用户类型
    if (!userType) {
      this.validationError('用户类型不能为空');
    }

    // 验证租户用户必须提供租户ID
    if (userType !== 'SYSTEM' && !tenantId) {
      this.validationError('租户用户必须提供租户ID');
    }

    // 检查部门是否存在
    await this.findOne(id, userType, tenantId);

    // 使用代理执行策略方法
    return await this.departmentStrategyProxy.execute<{ success: boolean }>(
      userType,
      'remove',
      userType === 'SYSTEM' ? [id] : [id, tenantId],
      tenantId,
    );
  }

  /**
   * 检查部门名称是否存在
   * @param name 部门名称
   * @param pid 父部门ID
   * @param userType 用户类型
   * @param tenantId 租户ID（可选）
   * @param excludeId 排除的部门ID（可选，用于编辑时检查）
   * @returns 是否存在
   */
  async checkNameExists(
    name: string,
    pid: number,
    userType: string,
    tenantId?: string,
    excludeId?: number,
  ): Promise<boolean> {
    this.logger.log(
      `检查部门名称是否存在，名称: ${name}, 父部门ID: ${pid}, 用户类型: ${userType}, 租户ID: ${tenantId || 'system'}`,
    );

    // 验证部门名称
    if (!name) {
      this.validationError('部门名称不能为空');
    }

    // 验证用户类型
    if (!userType) {
      this.validationError('用户类型不能为空');
    }

    // 验证租户用户必须提供租户ID
    if (userType !== 'SYSTEM' && !tenantId) {
      this.validationError('租户用户必须提供租户ID');
    }

    // 使用代理执行策略方法
    return await this.departmentStrategyProxy.execute<boolean>(
      userType,
      'checkNameExists',
      userType === 'SYSTEM' ? [name, pid, undefined, excludeId] : [name, pid, tenantId, excludeId],
      tenantId,
    );
  }
}
