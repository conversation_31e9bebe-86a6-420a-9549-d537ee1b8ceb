import { Mo<PERSON><PERSON>, OnModuleInit } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';

import { MenuStrategyProxy } from './menu-strategy.proxy';
import { MenuController } from './menu.controller';
import { MenuService } from './menu.service';
import { MenuStrategyFactory } from './strategies/menu-strategy.factory';
import { SystemMenuStrategy } from './strategies/system-menu.strategy';
import { TenantMenuStrategy } from './strategies/tenant-menu.strategy';

import { CommonModule } from '@/core/common/common.module';
import { PrismaModule } from '@/core/database/prisma/prisma.module';

@Module({
  imports: [PrismaModule, CommonModule],
  controllers: [MenuController],
  providers: [
    MenuService,
    MenuStrategyFactory,
    SystemMenuStrategy,
    TenantMenuStrategy,
    MenuStrategyProxy,
  ],
  exports: [MenuService],
})
export class MenuModule implements OnModuleInit {
  constructor(private moduleRef: ModuleRef) {}

  /**
   * 模块初始化时注册策略
   */
  onModuleInit() {
    const factory = this.moduleRef.get(MenuStrategyFactory);
    const systemStrategy = this.moduleRef.get(SystemMenuStrategy);
    const tenantStrategy = this.moduleRef.get(TenantMenuStrategy);

    // 注册策略
    factory.register(systemStrategy);
    factory.register(tenantStrategy);
  }
}
