import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';

import { CreateMenuDto, MenuDto, MenuTreeDto, UpdateMenuDto } from './dto/menu.dto';
import { MenuService } from './menu.service';

import { JwtAuthGuard } from '@/core/auth/guards/jwt-auth.guard';
import { BaseController } from '@/core/common/base/base.controller';

/**
 * 菜单控制器
 * 处理菜单相关的API请求
 */
@ApiTags('菜单')
@Controller()
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class MenuController extends BaseController {
  constructor(private readonly menuService: MenuService) {
    super();
  }

  /**
   * 获取所有菜单（前端导航用）
   * @param req 请求对象
   * @returns 菜单树
   */
  @Get('menu/all')
  @ApiOperation({ summary: '获取所有菜单（前端导航用）' })
  @ApiResponse({ status: 200, description: '获取成功', type: [MenuTreeDto] })
  async getAllMenus(@Req() req: any) {
    console.log('菜单控制器 - getAllMenus 被调用，用户信息:', JSON.stringify(req.user));
    try {
      const data = await this.menuService.getMenuTree(req.user);
      console.log(
        '菜单控制器 - 获取菜单树成功，数据长度:',
        Array.isArray(data) ? data.length : '非数组',
      );
      return this.success(data);
    } catch (error) {
      console.error('菜单控制器 - 获取菜单树失败:', error);
      throw error;
    }
  }

  /**
   * 获取菜单列表（管理用）
   * @param req 请求对象
   * @returns 菜单列表
   */
  @Get('system/menu/list')
  @ApiOperation({ summary: '获取菜单列表（管理用）' })
  @ApiResponse({ status: 200, description: '获取成功', type: [MenuDto] })
  async getMenuList(@Req() req: any) {
    const data = await this.menuService.getMenuList(req.user);
    return this.success(data);
  }

  /**
   * 创建菜单
   * @param createMenuDto 创建菜单DTO
   * @param req 请求对象
   * @returns 创建的菜单
   */
  @Post('system/menu')
  @ApiOperation({ summary: '创建菜单' })
  @ApiResponse({ status: 200, description: '创建成功', type: MenuDto })
  async create(@Body() createMenuDto: CreateMenuDto, @Req() req: any) {
    const data = await this.menuService.createMenu(createMenuDto, req.user);
    return this.success(data, '创建菜单成功');
  }

  /**
   * 更新菜单
   * @param id 菜单ID
   * @param updateMenuDto 更新菜单DTO
   * @param req 请求对象
   * @returns 更新后的菜单
   */
  @Put('system/menu/:id')
  @ApiOperation({ summary: '更新菜单' })
  @ApiParam({ name: 'id', description: '菜单ID' })
  @ApiResponse({ status: 200, description: '更新成功', type: MenuDto })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateMenuDto: UpdateMenuDto,
    @Req() req: any,
  ) {
    const data = await this.menuService.updateMenu(id, updateMenuDto, req.user);
    return this.success(data, '更新菜单成功');
  }

  /**
   * 删除菜单
   * @param id 菜单ID
   * @param req 请求对象
   * @returns 删除结果
   */
  @Delete('system/menu/:id')
  @ApiOperation({ summary: '删除菜单' })
  @ApiParam({ name: 'id', description: '菜单ID' })
  @ApiResponse({ status: 200, description: '删除成功' })
  async remove(@Param('id', ParseIntPipe) id: number, @Req() req: any) {
    const data = await this.menuService.removeMenu(id, req.user);
    return this.success(data, '删除菜单成功');
  }

  /**
   * 检查菜单名称是否存在
   * @param name 菜单名称
   * @param id 排除的菜单ID（可选）
   * @param req 请求对象
   * @returns 是否存在
   */
  @Get('system/menu/name-exists')
  @ApiOperation({ summary: '检查菜单名称是否存在' })
  @ApiQuery({ name: 'name', description: '菜单名称' })
  @ApiQuery({ name: 'id', description: '排除的菜单ID', required: false })
  @ApiResponse({ status: 200, description: '检查成功', type: Boolean })
  async checkNameExists(@Query('name') name: string, @Req() req: any, @Query('id') id?: string) {
    const exists = await this.menuService.isNameExists(name, req.user, id);
    return this.success({ exists });
  }

  /**
   * 检查菜单路径是否存在
   * @param path 菜单路径
   * @param id 排除的菜单ID（可选）
   * @param req 请求对象
   * @returns 是否存在
   */
  @Get('system/menu/path-exists')
  @ApiOperation({ summary: '检查菜单路径是否存在' })
  @ApiQuery({ name: 'path', description: '菜单路径' })
  @ApiQuery({ name: 'id', description: '排除的菜单ID', required: false })
  @ApiResponse({ status: 200, description: '检查成功', type: Boolean })
  async checkPathExists(@Query('path') path: string, @Req() req: any, @Query('id') id?: string) {
    const exists = await this.menuService.isPathExists(path, req.user, id);
    return this.success({ exists });
  }
}
