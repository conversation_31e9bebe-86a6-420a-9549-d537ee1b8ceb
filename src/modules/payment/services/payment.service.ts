import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';

import {
  CreatePaymentDto,
  PaymentStatus,
  QueryPaymentDto,
  RefundRequestDto,
  ProcessRefundDto,
} from '../dto/payment.dto';

import { BaseService } from '@/core/common/base/base.service';
import { TenantPrismaService } from '@/core/database/prisma/tenant-prisma.service';
import { PaginationOptions } from '@/core/pagination-options/pagination-options';

/**
 * 支付服务
 */
@Injectable()
export class PaymentService extends BaseService {
  constructor(
    private readonly tenantPrisma: TenantPrismaService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    super(PaymentService.name);
  }

  /**
   * 创建支付订单
   * @param userId 用户ID
   * @param createDto 创建支付订单DTO
   * @param tenantId 租户ID
   * @returns 支付结果
   */
  async createPayment(userId: number, createDto: CreatePaymentDto, tenantId: string) {
    try {
      // 生成订单号
      const orderNo = this.generateOrderNo();

      // 创建支付订单记录
      const payment = await (this.tenantPrisma as any).payment.create({
        data: {
          orderNo,
          userId,
          amount: createDto.amount,
          status: PaymentStatus.PENDING,
          paymentMethod: createDto.paymentMethod,
          businessType: createDto.businessType,
          businessId: createDto.businessId,
          subject: createDto.subject,
          description: createDto.description,
          metadata: createDto.metadata || {},
        },
      });

      // 这里应该调用实际的支付网关API获取支付链接和二维码
      // 简化实现，直接返回模拟数据
      return {
        orderNo: payment.orderNo,
        amount: payment.amount,
        paymentUrl: `https://payment-gateway.com/pay?orderNo=${payment.orderNo}`,
        qrCode: 'base64-encoded-qr-code-image', // 实际项目中应该是真实的二维码图片
      };
    } catch (error) {
      this.logError('创建支付订单失败', error);
      throw error;
    }
  }

  /**
   * 查询支付状态
   * @param orderNo 订单号
   * @param tenantId 租户ID
   * @returns 支付状态
   */
  async getPaymentStatus(orderNo: string, tenantId: string) {
    try {
      const payment = await (this.tenantPrisma as any).payment.findUnique({
        where: {
          orderNo,
        },
        include: {
          user: {
            select: {
              username: true,
            },
          },
        },
      });

      if (!payment) {
        this.validationError(`订单 ${orderNo} 不存在`);
      }

      return {
        orderNo: payment.orderNo,
        status: payment.status,
        paymentTime: payment.paymentTime,
        amount: payment.amount,
        paymentMethod: payment.paymentMethod,
        businessType: payment.businessType,
        businessId: payment.businessId,
      };
    } catch (error) {
      this.logError(`查询支付状态失败，订单号: ${orderNo}`, error);
      throw error;
    }
  }

  /**
   * 获取支付订单列表
   * @param queryDto 查询参数
   * @param options 分页选项
   * @param tenantId 租户ID
   * @returns 支付订单列表
   */
  async getPaymentList(queryDto: QueryPaymentDto, options: PaginationOptions, tenantId: string) {
    try {
      const { businessType, status, startTime, endTime } = queryDto;

      // 构建查询条件
      const where: any = {};

      if (businessType) {
        where.businessType = businessType;
      }

      if (status) {
        where.status = status;
      }

      if (startTime || endTime) {
        where.createTime = {};
        if (startTime) {
          where.createTime.gte = new Date(startTime);
        }
        if (endTime) {
          where.createTime.lte = new Date(endTime);
        }
      }

      // 查询总数
      const total = await (this.tenantPrisma as any).payment.count({ where });

      // 查询数据
      const items = await (this.tenantPrisma as any).payment.findMany({
        where,
        include: {
          user: {
            select: {
              username: true,
            },
          },
        },
        skip: options.skip,
        take: options.take,
        orderBy: {
          createTime: 'desc',
        },
      });

      // 格式化响应
      const formattedItems = items.map(item => ({
        id: item.id,
        orderNo: item.orderNo,
        userId: item.userId,
        username: item.user?.username,
        amount: item.amount,
        status: item.status,
        paymentMethod: item.paymentMethod,
        businessType: item.businessType,
        businessId: item.businessId,
        subject: item.subject,
        createTime: item.createTime,
        paymentTime: item.paymentTime,
        metadata: item.metadata,
      }));

      return {
        items: formattedItems,
        total,
        page: options.page,
        pageSize: options.pageSize,
      };
    } catch (error) {
      this.logError('获取支付订单列表失败', error);
      throw error;
    }
  }

  /**
   * 处理支付回调
   * @param provider 支付提供商
   * @param callbackData 回调数据
   * @param tenantId 租户ID
   * @returns 处理结果
   */
  async handlePaymentCallback(provider: string, callbackData: any, tenantId: string) {
    try {
      // 不同支付提供商的回调数据格式不同，需要分别处理
      let orderNo = '';
      let paymentStatus = PaymentStatus.PENDING;

      switch (provider) {
        case 'alipay':
          orderNo = callbackData.out_trade_no;
          paymentStatus =
            callbackData.trade_status === 'TRADE_SUCCESS'
              ? PaymentStatus.SUCCESS
              : PaymentStatus.FAILED;
          break;
        case 'wechat':
          orderNo = callbackData.out_trade_no;
          paymentStatus =
            callbackData.result_code === 'SUCCESS' ? PaymentStatus.SUCCESS : PaymentStatus.FAILED;
          break;
        default:
          this.validationError(`不支持的支付提供商: ${provider}`);
      }

      // 更新支付状态
      const payment = await (this.tenantPrisma as any).payment.findUnique({
        where: { orderNo },
      });

      if (!payment) {
        this.validationError(`订单 ${orderNo} 不存在`);
      }

      // 只有在状态不同时才更新并触发事件
      if (payment.status !== paymentStatus) {
        // 更新支付状态
        const updatedPayment = await (this.tenantPrisma as any).payment.update({
          where: { orderNo },
          data: {
            status: paymentStatus,
            paymentTime: paymentStatus === PaymentStatus.SUCCESS ? new Date() : null,
            callbackData: callbackData,
          },
        });

        // 发送相应的事件
        if (paymentStatus === PaymentStatus.SUCCESS) {
          this.eventEmitter.emit('payment.success', {
            orderNo,
            businessType: payment.businessType,
            businessId: payment.businessId,
            userId: payment.userId,
            amount: payment.amount,
            metadata: payment.metadata,
            tenantId,
          });
        } else if (paymentStatus === PaymentStatus.FAILED) {
          this.eventEmitter.emit('payment.failed', {
            orderNo,
            businessType: payment.businessType,
            businessId: payment.businessId,
            userId: payment.userId,
            tenantId,
          });
        }
      }

      return { success: true };
    } catch (error) {
      this.logError('处理支付回调失败', error);
      throw error;
    }
  }

  /**
   * 申请退款
   * @param userId 用户ID
   * @param refundDto 退款申请DTO
   * @param tenantId 租户ID
   * @returns 退款申请结果
   */
  async requestRefund(userId: number, refundDto: RefundRequestDto, tenantId: string) {
    try {
      const { orderNo, reason, amount } = refundDto;

      // 查询原始支付订单
      const payment = await (this.tenantPrisma as any).payment.findUnique({
        where: { orderNo },
      });

      if (!payment) {
        this.validationError(`订单 ${orderNo} 不存在`);
      }

      if (payment.userId !== userId) {
        this.validationError('您没有权限申请此订单的退款');
      }

      if (payment.status !== PaymentStatus.SUCCESS) {
        this.validationError('只有支付成功的订单才能申请退款');
      }

      if (amount > payment.amount) {
        this.validationError('退款金额不能大于支付金额');
      }

      // 生成退款单号
      const refundNo = 'R' + this.generateOrderNo().substring(1);

      // 创建退款记录
      const refund = await (this.tenantPrisma as any).refund.create({
        data: {
          refundNo,
          paymentId: payment.id,
          amount,
          reason,
          status: 'processing',
          userId,
        },
      });

      return {
        refundNo: refund.refundNo,
        orderNo,
        amount: refund.amount,
        status: refund.status,
      };
    } catch (error) {
      this.logError('申请退款失败', error);
      throw error;
    }
  }

  /**
   * 处理退款申请
   * @param processDto 处理退款DTO
   * @param tenantId 租户ID
   * @returns 处理结果
   */
  async processRefund(processDto: ProcessRefundDto, tenantId: string) {
    try {
      const { refundNo, action, comment } = processDto;

      // 查询退款申请
      const refund = await (this.tenantPrisma as any).refund.findUnique({
        where: { refundNo },
        include: { payment: true },
      });

      if (!refund) {
        this.validationError(`退款单 ${refundNo} 不存在`);
      }

      if (refund.status !== 'processing') {
        this.validationError('只有处理中的退款申请才能进行处理');
      }

      // 更新退款状态
      const status = action === 'approve' ? 'approved' : 'rejected';

      await (this.tenantPrisma as any).refund.update({
        where: { refundNo },
        data: {
          status,
          comment,
          processTime: new Date(),
        },
      });

      // 如果批准退款，更新支付订单状态
      if (action === 'approve') {
        const updatedPayment = await (this.tenantPrisma as any).payment.update({
          where: { id: refund.paymentId },
          data: {
            status: PaymentStatus.REFUNDED,
          },
        });

        // 发送退款成功事件
        this.eventEmitter.emit('payment.refunded', {
          orderNo: refund.payment.orderNo,
          refundNo,
          businessType: refund.payment.businessType,
          businessId: refund.payment.businessId,
          userId: refund.payment.userId,
          amount: refund.amount,
          tenantId,
        });
      }

      return {
        refundNo,
        orderNo: refund.payment.orderNo,
        amount: refund.amount,
        status,
        comment,
      };
    } catch (error) {
      this.logError('处理退款申请失败', error);
      throw error;
    }
  }

  /**
   * 获取支付统计数据
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @param tenantId 租户ID
   * @returns 统计数据
   */
  async getPaymentStats(startTime: string, endTime: string, tenantId: string) {
    try {
      const start = startTime
        ? new Date(startTime)
        : new Date(new Date().setDate(new Date().getDate() - 30));
      const end = endTime ? new Date(endTime) : new Date();

      // 查询支付订单
      const payments = await (this.tenantPrisma as any).payment.findMany({
        where: {
          createTime: {
            gte: start,
            lte: end,
          },
        },
      });

      // 查询退款
      const refunds = await (this.tenantPrisma as any).refund.findMany({
        where: {
          status: 'approved',
          createTime: {
            gte: start,
            lte: end,
          },
        },
      });

      // 计算统计数据
      const successOrders = payments.filter(p => p.status === PaymentStatus.SUCCESS).length;
      const pendingOrders = payments.filter(p => p.status === PaymentStatus.PENDING).length;
      const failedOrders = payments.filter(p => p.status === PaymentStatus.FAILED).length;

      const totalRevenue = payments
        .filter(p => p.status === PaymentStatus.SUCCESS)
        .reduce((sum, p) => sum + p.amount, 0);

      const refundedAmount = refunds.reduce((sum, r) => sum + r.amount, 0);

      // 支付方式分布
      const paymentMethods = [...new Set(payments.map(p => p.paymentMethod))];
      const paymentMethodDistribution = paymentMethods.map(method => {
        const methodPayments = payments.filter(
          p => p.paymentMethod === method && p.status === PaymentStatus.SUCCESS,
        );
        const count = methodPayments.length;
        const amount = methodPayments.reduce((sum, p) => sum + p.amount, 0);
        const percentage = successOrders > 0 ? (count / successOrders) * 100 : 0;

        return {
          method,
          count,
          amount,
          percentage: parseFloat(percentage.toFixed(2)),
        };
      });

      // 业务类型分布
      const businessTypes = [...new Set(payments.map(p => p.businessType))];
      const businessTypeDistribution = businessTypes.map(type => {
        const typePayments = payments.filter(
          p => p.businessType === type && p.status === PaymentStatus.SUCCESS,
        );
        const count = typePayments.length;
        const amount = typePayments.reduce((sum, p) => sum + p.amount, 0);
        const percentage = successOrders > 0 ? (count / successOrders) * 100 : 0;

        return {
          type,
          count,
          amount,
          percentage: parseFloat(percentage.toFixed(2)),
        };
      });

      // 每日收入
      const dailyRevenue = [];
      const dateRange = this.getDateRange(start, end);

      for (const date of dateRange) {
        const dateStr = date.toISOString().split('T')[0];
        const dayPayments = payments.filter(p => {
          const paymentDate = p.createTime.toISOString().split('T')[0];
          return paymentDate === dateStr && p.status === PaymentStatus.SUCCESS;
        });

        const amount = dayPayments.reduce((sum, p) => sum + p.amount, 0);

        dailyRevenue.push({
          date: dateStr,
          amount,
        });
      }

      return {
        totalRevenue,
        successOrders,
        pendingOrders,
        failedOrders,
        refundedAmount,
        paymentMethodDistribution,
        businessTypeDistribution,
        dailyRevenue,
      };
    } catch (error) {
      this.logError('获取支付统计数据失败', error);
      throw error;
    }
  }

  /**
   * 手动更新支付状态（用于测试）
   * @param orderNo 订单号
   * @param status 支付状态
   * @param tenantId 租户ID
   * @returns 更新结果
   */
  async updatePaymentStatus(orderNo: string, status: PaymentStatus, tenantId: string) {
    try {
      const payment = await (this.tenantPrisma as any).payment.findUnique({
        where: { orderNo },
      });

      if (!payment) {
        this.validationError(`订单 ${orderNo} 不存在`);
      }

      // 只有在状态不同时才更新并触发事件
      if (payment.status !== status) {
        // 更新支付状态
        const updatedPayment = await (this.tenantPrisma as any).payment.update({
          where: { orderNo },
          data: {
            status,
            paymentTime: status === PaymentStatus.SUCCESS ? new Date() : null,
          },
        });

        // 发送相应的事件
        if (status === PaymentStatus.SUCCESS) {
          this.eventEmitter.emit('payment.success', {
            orderNo,
            businessType: payment.businessType,
            businessId: payment.businessId,
            userId: payment.userId,
            amount: payment.amount,
            metadata: payment.metadata,
            tenantId,
          });
        } else if (status === PaymentStatus.FAILED) {
          this.eventEmitter.emit('payment.failed', {
            orderNo,
            businessType: payment.businessType,
            businessId: payment.businessId,
            userId: payment.userId,
            tenantId,
          });
        } else if (status === PaymentStatus.REFUNDED) {
          this.eventEmitter.emit('payment.refunded', {
            orderNo,
            refundNo: null,
            businessType: payment.businessType,
            businessId: payment.businessId,
            userId: payment.userId,
            amount: payment.amount,
            tenantId,
          });
        }
      }

      return {
        orderNo,
        status,
        updated: payment.status !== status,
      };
    } catch (error) {
      this.logError(`手动更新支付状态失败，订单号: ${orderNo}`, error);
      throw error;
    }
  }

  /**
   * 生成订单号
   * @returns 订单号
   */
  private generateOrderNo(): string {
    const now = new Date();
    const year = now.getFullYear().toString().substring(2);
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const random = Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, '0');
    return `P${year}${month}${day}${random}`;
  }

  /**
   * 获取日期范围
   * @param start 开始日期
   * @param end 结束日期
   * @returns 日期范围数组
   */
  private getDateRange(start: Date, end: Date): Date[] {
    const dates = [];
    const currentDate = new Date(start);

    while (currentDate <= end) {
      dates.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return dates;
  }
}
