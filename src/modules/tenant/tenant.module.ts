import { Module } from '@nestjs/common';

import { TenantController } from './tenant.controller';
import { TenantService } from './tenant.service';

import { PrismaModule } from '@/core/database/prisma/prisma.module';

/**
 * 租户模块
 * 处理租户相关的功能
 */
@Module({
  imports: [PrismaModule],
  controllers: [TenantController],
  providers: [TenantService],
  exports: [TenantService],
})
export class TenantModule {}
