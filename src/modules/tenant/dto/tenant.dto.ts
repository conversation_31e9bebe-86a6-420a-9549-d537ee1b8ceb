import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsNumber,
  IsObject,
  IsNotEmpty,
  IsUrl,
  Min,
  Max,
  ValidateIf,
} from 'class-validator';

/**
 * 数据源DTO
 */
export class DatasourceDto {
  @ApiProperty({ description: '数据源ID' })
  id: number;

  @ApiProperty({ description: '数据源名称' })
  name: string;

  @ApiProperty({ description: '数据源URL' })
  url: string;

  @ApiProperty({ description: '数据源元数据', type: 'object' })
  metadata: any;
}

/**
 * 创建数据源DTO
 */
export class CreateDatasourceDto {
  @ApiProperty({ description: '数据源名称' })
  @IsString()
  @IsNotEmpty({ message: '数据源名称不能为空' })
  name: string;

  @ApiProperty({ description: '数据源URL' })
  @IsString()
  @IsNotEmpty({ message: '数据源URL不能为空' })
  url: string;
}

/**
 * 租户DTO
 */
export class TenantDto {
  @ApiProperty({ description: '租户ID' })
  id: number;

  @ApiProperty({ description: '租户代码' })
  code: string;

  @ApiProperty({ description: '租户名称' })
  name: string;

  @ApiProperty({ description: '租户网站' })
  website: string;

  @ApiProperty({ description: '租户自定义域名', required: false })
  domain?: string;

  @ApiProperty({ description: '租户状态：0-禁用，1-启用' })
  status: number;

  @ApiProperty({ description: '租户元数据', type: 'object' })
  metadata: any;

  @ApiProperty({ description: '创建时间' })
  createTime: string;

  @ApiProperty({ description: '更新时间' })
  updateTime: string;

  @ApiProperty({ description: '数据源信息', type: DatasourceDto })
  datasource: DatasourceDto;
}

/**
 * 租户列表DTO
 */
export class TenantListDto {
  @ApiProperty({ description: '租户列表', type: [TenantDto] })
  items: TenantDto[];

  @ApiProperty({ description: '总数' })
  total: number;

  @ApiProperty({ description: '当前页码' })
  page: number;

  @ApiProperty({ description: '每页数量' })
  pageSize: number;
}

/**
 * 创建租户DTO
 */
export class CreateTenantDto {
  @ApiProperty({ description: '租户代码' })
  @IsString()
  @IsNotEmpty({ message: '租户代码不能为空' })
  code: string;

  @ApiProperty({ description: '租户名称' })
  @IsString()
  @IsNotEmpty({ message: '租户名称不能为空' })
  name: string;

  @ApiProperty({ description: '租户网站' })
  @IsString()
  @IsOptional()
  @ValidateIf(o => o.website !== '' && o.website !== null && o.website !== undefined)
  @IsUrl(
    { require_protocol: true },
    {
      message: '租户网站必须是有效的URL（如 http://example.com）',
    },
  )
  website?: string;

  @ApiProperty({ description: '租户自定义域名（如 example.com）' })
  @IsString()
  @IsOptional()
  domain?: string;

  @ApiProperty({ description: '租户状态：0-禁用，1-启用', default: 1 })
  @IsNumber()
  @Min(0, { message: '状态值最小为0' })
  @Max(1, { message: '状态值最大为1' })
  @IsOptional()
  status?: number;

  @ApiProperty({ description: '租户元数据', type: 'object' })
  @IsObject()
  @IsOptional()
  metadata?: any;

  @ApiProperty({
    description: '数据源信息（可选，不提供则使用默认数据库）',
    type: CreateDatasourceDto,
    required: false,
  })
  @IsOptional()
  datasource?: CreateDatasourceDto;
}

/**
 * 更新租户DTO
 */
export class UpdateTenantDto {
  @ApiProperty({ description: '租户名称' })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ description: '租户网站' })
  @IsString()
  @IsOptional()
  @ValidateIf(o => o.website !== '' && o.website !== null && o.website !== undefined)
  @IsUrl(
    { require_protocol: true },
    {
      message: '租户网站必须是有效的URL（如 http://example.com）',
    },
  )
  website?: string;

  @ApiProperty({ description: '租户自定义域名（如 example.com）' })
  @IsString()
  @IsOptional()
  domain?: string;

  @ApiProperty({ description: '租户状态：0-禁用，1-启用' })
  @IsNumber()
  @Min(0, { message: '状态值最小为0' })
  @Max(1, { message: '状态值最大为1' })
  @IsOptional()
  status?: number;

  @ApiProperty({ description: '租户元数据', type: 'object' })
  @IsObject()
  @IsOptional()
  metadata?: any;
}

/**
 * 更新租户状态DTO
 */
export class UpdateTenantStatusDto {
  @ApiProperty({ description: '租户状态：0-禁用，1-启用' })
  @IsNumber()
  @Min(0, { message: '状态值最小为0' })
  @Max(1, { message: '状态值最大为1' })
  status: number;
}
