import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  UseGuards,
  Query,
  Patch,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';

import {
  CreateTenantDto,
  TenantDto,
  TenantListDto,
  UpdateTenantDto,
  UpdateTenantStatusDto,
} from './dto/tenant.dto';
import { TenantService } from './tenant.service';

import { JwtAuthGuard } from '@/core/auth/guards/jwt-auth.guard';
import { SystemUserGuard } from '@/core/auth/guards/system-user.guard';
import { BaseController } from '@/core/common/base/base.controller';

/**
 * 租户控制器
 * 处理租户相关的请求
 */
@ApiTags('租户管理')
@Controller('tenant')
@UseGuards(JwtAuthGuard, SystemUserGuard)
@ApiBearerAuth()
export class TenantController extends BaseController {
  constructor(private readonly tenantService: TenantService) {
    super();
  }

  /**
   * 获取租户列表
   * @param query 查询参数
   * @returns 租户列表
   */
  @Get('list')
  @ApiOperation({ summary: '获取租户列表' })
  @ApiResponse({ status: 200, description: '成功', type: TenantListDto })
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'pageSize', required: false, description: '每页数量' })
  @ApiQuery({ name: 'name', required: false, description: '租户名称' })
  @ApiQuery({ name: 'code', required: false, description: '租户代码' })
  @ApiQuery({ name: 'domain', required: false, description: '租户域名' })
  @ApiQuery({ name: 'status', required: false, description: '状态：0-禁用，1-启用' })
  @ApiQuery({ name: 'startTime', required: false, description: '开始时间' })
  @ApiQuery({ name: 'endTime', required: false, description: '结束时间' })
  async findAll(@Query() query: any): Promise<TenantListDto> {
    const data = await this.tenantService.findAll(query);
    return data;
  }

  /**
   * 获取租户详情
   * @param id 租户ID
   * @returns 租户详情
   */
  @Get(':id')
  @ApiOperation({ summary: '获取租户详情' })
  @ApiResponse({ status: 200, description: '成功', type: TenantDto })
  @ApiResponse({ status: 404, description: '租户不存在' })
  @ApiParam({ name: 'id', description: '租户ID' })
  async findOne(@Param('id') id: string): Promise<any> {
    const data = await this.tenantService.findOne(id);
    return this.success(data);
  }

  /**
   * 创建租户
   * @param createTenantDto 创建租户数据
   * @returns 创建的租户
   */
  @Post()
  @ApiOperation({ summary: '创建租户' })
  @ApiResponse({ status: 201, description: '创建成功', type: TenantDto })
  @ApiResponse({ status: 409, description: '租户代码已存在' })
  async create(@Body() createTenantDto: CreateTenantDto): Promise<any> {
    const data = await this.tenantService.create(createTenantDto);
    return this.success(data, '创建租户成功');
  }

  /**
   * 更新租户
   * @param id 租户ID
   * @param updateTenantDto 更新租户数据
   * @returns 更新后的租户
   */
  @Put(':id')
  @ApiOperation({ summary: '更新租户' })
  @ApiResponse({ status: 200, description: '更新成功', type: TenantDto })
  @ApiResponse({ status: 404, description: '租户不存在' })
  @ApiParam({ name: 'id', description: '租户ID' })
  async update(@Param('id') id: string, @Body() updateTenantDto: UpdateTenantDto): Promise<any> {
    const data = await this.tenantService.update(id, updateTenantDto);
    return this.success(data, '更新租户成功');
  }

  /**
   * 更新租户状态
   * @param id 租户ID
   * @param updateStatusDto 更新状态数据
   * @returns 更新结果
   */
  @Patch(':id/status')
  @ApiOperation({ summary: '更新租户状态' })
  @ApiResponse({ status: 200, description: '更新成功' })
  @ApiResponse({ status: 404, description: '租户不存在' })
  @ApiParam({ name: 'id', description: '租户ID' })
  async updateStatus(
    @Param('id') id: string,
    @Body() updateStatusDto: UpdateTenantStatusDto,
  ): Promise<any> {
    const data = await this.tenantService.updateStatus(id, updateStatusDto.status);
    return this.success(data, '更新租户状态成功');
  }

  /**
   * 删除租户
   * @param id 租户ID
   * @returns 删除结果
   */
  @Delete(':id')
  @ApiOperation({ summary: '删除租户' })
  @ApiResponse({ status: 200, description: '删除成功' })
  @ApiResponse({ status: 404, description: '租户不存在' })
  @ApiParam({ name: 'id', description: '租户ID' })
  async remove(@Param('id') id: string): Promise<any> {
    const data = await this.tenantService.remove(id);
    return this.success(data, '删除租户成功');
  }

  /**
   * 检查租户代码是否存在
   * @param code 租户代码
   * @param id 排除的租户ID（可选）
   * @returns 是否存在
   */
  @Get('check-code/:code')
  @ApiOperation({ summary: '检查租户代码是否存在' })
  @ApiResponse({ status: 200, description: '成功' })
  @ApiParam({ name: 'code', description: '租户代码' })
  @ApiQuery({ name: 'id', required: false, description: '排除的租户ID' })
  async checkCodeExists(@Param('code') code: string, @Query('id') id?: string): Promise<any> {
    const exists = await this.tenantService.checkCodeExists(code, id);
    return this.success({ exists });
  }

  /**
   * 检查租户域名是否存在
   * @param domain 租户域名
   * @param id 排除的租户ID（可选）
   * @returns 是否存在
   */
  @Get('check-domain/:domain')
  @ApiOperation({ summary: '检查租户域名是否存在' })
  @ApiResponse({ status: 200, description: '成功' })
  @ApiParam({ name: 'domain', description: '租户域名' })
  @ApiQuery({ name: 'id', required: false, description: '排除的租户ID' })
  async checkDomainExists(@Param('domain') domain: string, @Query('id') id?: string): Promise<any> {
    const exists = await this.tenantService.checkDomainExists(domain, id);
    return this.success({ exists });
  }
}
