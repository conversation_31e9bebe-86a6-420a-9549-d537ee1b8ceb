import { Module } from '@nestjs/common';

import { AuthModule } from './auth/auth.module';
import { CommonModule } from './common/common.module';
import { PrismaModule } from './database/prisma/prisma.module';
import { HealthModule } from './health/health.module';
import { LoggingModule } from './logging/logging.module';
import { MiddlewareModule } from './middleware/middleware.module';
import { MonitoringModule } from './monitoring/monitoring.module';

/**
 * 核心模块
 * 提供应用程序中使用的所有核心功能
 */
@Module({
  imports: [
    AuthModule,
    CommonModule,
    PrismaModule,
    MiddlewareModule,
    LoggingModule,
    HealthModule,
    MonitoringModule,
  ],
  exports: [
    AuthModule,
    CommonModule,
    PrismaModule,
    MiddlewareModule,
    LoggingModule,
    HealthModule,
    MonitoringModule,
  ],
})
export class CoreModule {}
