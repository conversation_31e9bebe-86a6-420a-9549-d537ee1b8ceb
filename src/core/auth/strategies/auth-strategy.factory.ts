import { Injectable, Logger, Scope } from '@nestjs/common';

import { IAuthStrategy } from './auth-strategy.interface';
import { SystemAuthStrategy } from './system-auth.strategy';
import { TenantAuthStrategy } from './tenant-auth.strategy';
import { LoginDto } from '../dto/login.dto';

/**
 * 认证策略工厂
 * 根据登录信息选择合适的认证策略
 */
@Injectable({ scope: Scope.REQUEST })
export class AuthStrategyFactory {
  private readonly logger = new Logger(AuthStrategyFactory.name);

  constructor(
    private readonly systemAuthStrategy: SystemAuthStrategy,
    private readonly tenantAuthStrategy: TenantAuthStrategy,
  ) {}

  /**
   * 创建认证策略
   * @param loginDto 登录信息
   * @param hasTenantInRequest 请求中是否有租户信息
   * @returns 认证策略实例
   */
  createStrategy(loginDto?: LoginDto, hasTenantInRequest?: boolean): IAuthStrategy {
    // 如果请求中有租户信息，直接使用租户认证策略
    if (hasTenantInRequest) {
      this.logger.debug('请求中有租户信息，使用租户认证策略');
      return this.tenantAuthStrategy;
    }

    // 如果提供了登录信息，根据是否有租户代码选择策略
    if (loginDto) {
      if (loginDto.tenantCode) {
        this.logger.debug(`登录DTO中有租户代码 ${loginDto.tenantCode}，使用租户认证策略`);
        return this.tenantAuthStrategy;
      } else {
        this.logger.debug('登录DTO中没有租户代码，使用系统认证策略');
        return this.systemAuthStrategy;
      }
    }

    // 默认返回系统认证策略
    this.logger.debug('没有租户信息，默认使用系统认证策略');
    return this.systemAuthStrategy;
  }

  /**
   * 根据用户类型和租户ID创建认证策略
   * @param userType 用户类型
   * @param tenantId 租户ID
   * @returns 认证策略实例
   */
  createStrategyByUserType(userType: string, tenantId?: string): IAuthStrategy {
    if (userType === 'TENANT') {
      this.logger.debug(`用户类型为 TENANT，租户ID: ${tenantId}，使用租户认证策略`);
      return this.tenantAuthStrategy;
    } else {
      this.logger.debug(`用户类型为 ${userType}，使用系统认证策略`);
      return this.systemAuthStrategy;
    }
  }
}
