import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';

import { IAuthStrategy } from './auth-strategy.interface';
import { ApiCode, ApiMessage } from '../../common/constants/api-code.constant';
import { PasswordUtil } from '../../common/utils/password.util';
import { PublicPrismaService } from '../../database/prisma/public-prisma.service';
import { LoginDto } from '../dto/login.dto';
import { UserInfoDto } from '../dto/user-info.dto';
import { JwtPayload } from '../strategies/jwt.strategy';

/**
 * 系统用户认证策略
 * 实现系统用户的认证相关操作
 */
@Injectable()
export class SystemAuthStrategy implements IAuthStrategy {
  private readonly logger = new Logger(SystemAuthStrategy.name);

  constructor(
    private readonly prisma: PublicPrismaService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 系统用户登录
   * @param loginDto 登录信息
   * @returns 登录结果，包含访问令牌
   */
  async login(loginDto: LoginDto): Promise<{ accessToken: string }> {
    const { username, password } = loginDto;

    // 查找系统用户
    const user = await this.prisma.systemUser.findUnique({
      where: { username },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    // 如果用户不存在或者状态不是启用状态，则抛出异常
    if (!user) {
      this.logger.warn(`登录失败: 用户 ${username} 不存在`);
      throw new UnauthorizedException(ApiMessage[ApiCode.USER_NOT_FOUND]);
    }

    if (user.status !== 1) {
      this.logger.warn(`登录失败: 用户 ${username} 已禁用`);
      throw new UnauthorizedException(ApiMessage[ApiCode.USER_ACCOUNT_DISABLED]);
    }

    // 验证密码
    const isPasswordValid = await PasswordUtil.verify(password, user.password);
    if (!isPasswordValid) {
      this.logger.warn(`登录失败: 用户 ${username} 密码错误`);
      throw new UnauthorizedException(ApiMessage[ApiCode.USER_PASSWORD_ERROR]);
    }

    // 检查密码是否需要升级（从SHA-1/SHA-256升级到bcrypt）
    if (PasswordUtil.needsUpgrade(user.password)) {
      this.logger.log(`升级用户 ${username} 的密码哈希到bcrypt格式`);
      // 生成bcrypt哈希
      const bcryptHash = await PasswordUtil.hash(password);
      // 更新用户密码
      await this.prisma.systemUser.update({
        where: { id: user.id },
        data: { password: bcryptHash },
      });
    }

    // 更新最后登录时间
    await this.prisma.systemUser.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() },
    });

    // 生成JWT载荷
    const payload: JwtPayload = {
      sub: user.id.toString(),
      username: user.username,
      userType: 'SYSTEM',
    };

    // 生成访问令牌
    const accessToken = this.jwtService.sign(payload);

    this.logger.log(`用户 ${username} 登录成功`);
    return { accessToken };
  }

  /**
   * 获取系统用户信息
   * @param userId 用户ID
   * @returns 用户信息
   */
  async getUserInfo(userId: string): Promise<UserInfoDto> {
    const user = await this.prisma.systemUser.findUnique({
      where: { id: parseInt(userId) },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    if (!user) {
      throw new UnauthorizedException(ApiMessage[ApiCode.USER_NOT_FOUND]);
    }

    // 获取用户角色列表
    const roles = user.userRoles.map(ur => ur.role.code);

    return {
      userId: user.id.toString(),
      username: user.username,
      realName: user.realName || user.username,
      avatar: user.avatar,
      desc: '',
      homePath: this.configService.get<string>('DEFAULT_HOME_PATH', '/dashboard'),
      roles,
    };
  }

  /**
   * 获取系统用户权限码
   * @param userId 用户ID
   * @returns 权限码列表
   */
  async getPermissionCodes(userId: string): Promise<string[]> {
    // 查询用户角色
    const userRoles = await this.prisma.systemUserRole.findMany({
      where: { userId: parseInt(userId) },
      include: {
        role: {
          include: {
            rolePermissions: {
              include: {
                permission: true,
              },
            },
          },
        },
      },
    });

    // 提取权限码
    const permissionCodes = new Set<string>();
    userRoles.forEach(ur => {
      ur.role.rolePermissions.forEach(rp => {
        if (rp.permission.status === 1) {
          permissionCodes.add(rp.permission.code);
        }
      });
    });

    return Array.from(permissionCodes);
  }

  /**
   * 获取系统用户角色编码
   * @param userId 用户ID
   * @returns 角色编码列表
   */
  async getRoleCodes(userId: string): Promise<string[]> {
    // 查询用户角色
    const userRoles = await this.prisma.systemUserRole.findMany({
      where: { userId: parseInt(userId) },
      include: {
        role: true,
      },
    });

    // 提取角色编码
    const roleCodes = userRoles.map(ur => ur.role.code);

    return roleCodes;
  }

  /**
   * 刷新系统用户令牌
   * @param userId 用户ID
   * @returns 新的访问令牌
   */
  async refreshToken(userId: string): Promise<{ accessToken: string }> {
    const user = await this.prisma.systemUser.findUnique({
      where: { id: parseInt(userId) },
    });

    if (!user) {
      throw new UnauthorizedException(ApiMessage[ApiCode.USER_NOT_FOUND]);
    }

    // 生成新的JWT载荷
    const payload: JwtPayload = {
      sub: user.id.toString(),
      username: user.username,
      userType: 'SYSTEM',
    };

    // 生成新的访问令牌
    const accessToken = this.jwtService.sign(payload);

    return { accessToken };
  }
}
