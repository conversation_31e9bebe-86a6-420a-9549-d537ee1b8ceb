import {
  Injectable,
  UnauthorizedException,
  Logger,
  BadRequestException,
  Inject,
  Scope,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';

import { IAuthStrategy } from './auth-strategy.interface';
import { ApiCode, ApiMessage } from '../../common/constants/api-code.constant';
import { PasswordUtil } from '../../common/utils/password.util';
import { PublicPrismaService } from '../../database/prisma/public-prisma.service';
import { TENANT_PRISMA_SERVICE } from '../../database/prisma/tenant-prisma.service';
import { LoginDto } from '../dto/login.dto';
import { UserInfoDto } from '../dto/user-info.dto';
import { JwtPayload } from '../strategies/jwt.strategy';

/**
 * 租户用户认证策略
 * 实现租户用户的认证相关操作
 */
@Injectable({ scope: Scope.REQUEST })
export class TenantAuthStrategy implements IAuthStrategy {
  private readonly logger = new Logger(TenantAuthStrategy.name);

  constructor(
    private readonly publicPrisma: PublicPrismaService,
    @Inject(TENANT_PRISMA_SERVICE) private readonly tenantPrisma: any,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 租户用户登录
   * @param loginDto 登录信息
   * @returns 登录结果，包含访问令牌
   */
  async login(loginDto: LoginDto): Promise<{ accessToken: string }> {
    const { username, password, tenantCode } = loginDto;

    if (!tenantCode) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    // 查找租户
    const tenant = await this.publicPrisma.tenant.findUnique({
      where: { code: tenantCode },
    });

    if (!tenant) {
      this.logger.warn(`登录失败: 租户 ${tenantCode} 不存在`);
      throw new UnauthorizedException(ApiMessage[ApiCode.TENANT_NOT_FOUND]);
    }

    if (tenant.status !== 1) {
      this.logger.warn(`登录失败: 租户 ${tenantCode} 已禁用`);
      throw new UnauthorizedException(ApiMessage[ApiCode.TENANT_DISABLED]);
    }

    // 查找租户用户
    // 检查 tenantPrisma 是否为 null
    if (!this.tenantPrisma) {
      this.logger.error(`登录失败: 租户 ${tenantCode} 的数据库连接不可用`);
      throw new UnauthorizedException(ApiMessage[ApiCode.SERVICE_UNAVAILABLE]);
    }

    try {
      const user = await this.tenantPrisma.user.findFirst({
        where: {
          username,
          tenantId: tenant.id, // 使用租户的数字ID
        },
        include: {
          userRoles: {
            include: {
              role: true,
            },
          },
        },
      });

      // 如果用户不存在或者状态不是启用状态，则抛出异常
      if (!user) {
        this.logger.warn(`登录失败: 用户 ${username} 不存在`);
        throw new UnauthorizedException(ApiMessage[ApiCode.USER_NOT_FOUND]);
      }

      if (user.status !== 1) {
        this.logger.warn(`登录失败: 用户 ${username} 已禁用`);
        throw new UnauthorizedException(ApiMessage[ApiCode.USER_ACCOUNT_DISABLED]);
      }

      // 验证密码
      const isPasswordValid = await PasswordUtil.verify(password, user.password);
      if (!isPasswordValid) {
        this.logger.warn(`登录失败: 用户 ${username} 密码错误`);
        throw new UnauthorizedException(ApiMessage[ApiCode.USER_PASSWORD_ERROR]);
      }

      // 检查密码是否需要升级（从SHA-1/SHA-256升级到bcrypt）
      if (PasswordUtil.needsUpgrade(user.password)) {
        this.logger.log(`升级租户 ${tenantCode} 用户 ${username} 的密码哈希到bcrypt格式`);
        // 生成bcrypt哈希
        const bcryptHash = await PasswordUtil.hash(password);
        // 更新用户密码
        await this.tenantPrisma.user.update({
          where: { id: user.id },
          data: { password: bcryptHash },
        });
      }

      // 更新最后登录时间
      await this.tenantPrisma.user.update({
        where: { id: user.id },
        data: { lastLoginAt: new Date() },
      });

      // 生成JWT载荷
      const payload: JwtPayload = {
        sub: user.id.toString(),
        username: user.username,
        userType: 'TENANT',
        tenantId: tenant.id.toString(), // 使用租户的数字ID
        tenantCode: tenantCode, // 保留租户代码
      };

      // 生成访问令牌
      const accessToken = this.jwtService.sign(payload);

      this.logger.log(`租户 ${tenantCode} 用户 ${username} 登录成功`);
      return { accessToken };
    } catch (error) {
      this.logger.error(`登录失败: ${error.message}`);
      throw new UnauthorizedException(ApiMessage[ApiCode.ERROR]);
    }
  }

  /**
   * 获取租户用户信息
   * @param userId 用户ID
   * @param tenantId 租户ID
   * @returns 用户信息
   */
  async getUserInfo(userId: string, tenantId: string): Promise<UserInfoDto> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    // 检查 tenantPrisma 是否为 null
    if (!this.tenantPrisma) {
      this.logger.error(`获取用户信息失败: 租户 ${tenantId} 的数据库连接不可用`);
      throw new UnauthorizedException(ApiMessage[ApiCode.SERVICE_UNAVAILABLE]);
    }

    const user = await this.tenantPrisma.user.findUnique({
      where: {
        id: parseInt(userId),
      },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    if (!user) {
      throw new UnauthorizedException(ApiMessage[ApiCode.USER_NOT_FOUND]);
    }

    // 将 tenantId 转换为数字进行比较，因为数据库中的 tenantId 是数字类型
    if (user.tenantId !== parseInt(tenantId)) {
      this.logger.warn(
        `用户 ${user.id} 的租户ID ${user.tenantId} 与请求的租户ID ${tenantId} 不匹配`,
      );
      throw new UnauthorizedException(ApiMessage[ApiCode.PERMISSION_DENIED]);
    }

    // 获取用户角色列表
    const roles = user.userRoles.map(ur => ur.role.code);

    return {
      userId: user.id.toString(),
      username: user.username,
      realName: user.realName || user.username,
      avatar: user.avatar,
      desc: '',
      homePath: user.homePath || this.configService.get<string>('DEFAULT_HOME_PATH', '/dashboard'),
      roles,
    };
  }

  /**
   * 获取租户用户权限码
   * @param userId 用户ID
   * @param tenantId 租户ID
   * @returns 权限码列表
   */
  async getPermissionCodes(userId: string, tenantId: string): Promise<string[]> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    // 检查 tenantPrisma 是否为 null
    if (!this.tenantPrisma) {
      this.logger.error(`获取权限码失败: 租户 ${tenantId} 的数据库连接不可用`);
      throw new UnauthorizedException(ApiMessage[ApiCode.SERVICE_UNAVAILABLE]);
    }

    // 查询用户角色
    const userRoles = await this.tenantPrisma.userRole.findMany({
      where: {
        userId: parseInt(userId),
        tenantId: parseInt(tenantId), // 将 tenantId 转换为数字
      },
      include: {
        role: {
          include: {
            rolePermissions: {
              include: {
                permission: true,
              },
            },
          },
        },
      },
    });

    // 提取权限码
    const permissionCodes = new Set<string>();
    userRoles.forEach(ur => {
      ur.role.rolePermissions.forEach(rp => {
        if (rp.permission.status === 1) {
          permissionCodes.add(rp.permission.code);
        }
      });
    });

    return Array.from(permissionCodes);
  }

  /**
   * 获取租户用户角色编码
   * @param userId 用户ID
   * @param tenantId 租户ID
   * @returns 角色编码列表
   */
  async getRoleCodes(userId: string, tenantId: string): Promise<string[]> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    // 检查 tenantPrisma 是否为 null
    if (!this.tenantPrisma) {
      this.logger.error(`获取角色编码失败: 租户 ${tenantId} 的数据库连接不可用`);
      throw new UnauthorizedException(ApiMessage[ApiCode.SERVICE_UNAVAILABLE]);
    }

    // 查询用户角色
    const userRoles = await this.tenantPrisma.userRole.findMany({
      where: {
        userId: parseInt(userId),
        tenantId: parseInt(tenantId), // 将 tenantId 转换为数字
      },
      include: {
        role: true,
      },
    });

    // 提取角色编码
    const roleCodes = userRoles.map(ur => ur.role.code);

    return roleCodes;
  }

  /**
   * 刷新租户用户令牌
   * @param userId 用户ID
   * @param tenantId 租户ID
   * @returns 新的访问令牌
   */
  async refreshToken(userId: string, tenantId: string): Promise<{ accessToken: string }> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    // 检查 tenantPrisma 是否为 null
    if (!this.tenantPrisma) {
      this.logger.error(`刷新令牌失败: 租户 ${tenantId} 的数据库连接不可用`);
      throw new UnauthorizedException(ApiMessage[ApiCode.SERVICE_UNAVAILABLE]);
    }

    const user = await this.tenantPrisma.user.findUnique({
      where: {
        id: parseInt(userId),
      },
    });

    if (!user) {
      throw new UnauthorizedException(ApiMessage[ApiCode.USER_NOT_FOUND]);
    }

    // 将 tenantId 转换为数字进行比较，因为数据库中的 tenantId 是数字类型
    if (user.tenantId !== parseInt(tenantId)) {
      this.logger.warn(
        `用户 ${user.id} 的租户ID ${user.tenantId} 与请求的租户ID ${tenantId} 不匹配`,
      );
      throw new UnauthorizedException(ApiMessage[ApiCode.PERMISSION_DENIED]);
    }

    // 生成新的JWT载荷
    const payload: JwtPayload = {
      sub: user.id.toString(),
      username: user.username,
      userType: 'TENANT',
      tenantId: user.tenantId.toString(), // 使用租户的数字ID
      tenantCode: tenantId, // 保留租户代码
    };

    // 生成新的访问令牌
    const accessToken = this.jwtService.sign(payload);

    return { accessToken };
  }
}
