import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

export interface Response<T> {
  data: T;
  code: number;
  message: string;
  success: boolean;
}

@Injectable()
export class ResponseTransformInterceptor<T>
  implements NestInterceptor<T, Response<T>>
{
  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<Response<T>> {
    return next.handle().pipe(
      map((data) => {
        // 如果数据已经是标准响应格式，则直接返回
        if (
          data &&
          typeof data === 'object' &&
          'code' in data &&
          'success' in data &&
          'data' in data
        ) {
          return data;
        }

        // 处理分页数据
        if (data && typeof data === 'object' && 'items' in data && 'meta' in data) {
          return {
            code: 200,
            success: true,
            message: 'Success',
            data,
          };
        }

        // 处理普通数据
        return {
          code: 200,
          success: true,
          message: 'Success',
          data,
        };
      }),
    );
  }
}
