import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  <PERSON><PERSON><PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

@Injectable()
export class RequestLoggerInterceptor implements NestInterceptor {
  private readonly logger = new Logger(RequestLoggerInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const { method, url, body, query, params } = request;
    const userAgent = request.get('user-agent') || '';
    const requestId = request.headers['x-request-id'] || '';

    // 请求开始时间
    const now = Date.now();

    // 记录请求信息
    this.logger.log(
      `[${requestId}] Request - ${method} ${url} - User Agent: ${userAgent}`,
    );

    // 记录请求参数（如果有）
    if (Object.keys(body).length > 0) {
      this.logger.debug(`Request Body: ${JSON.stringify(body)}`);
    }
    if (Object.keys(query).length > 0) {
      this.logger.debug(`Request Query: ${JSON.stringify(query)}`);
    }
    if (Object.keys(params).length > 0) {
      this.logger.debug(`Request Params: ${JSON.stringify(params)}`);
    }

    return next.handle().pipe(
      tap({
        next: (data) => {
          const responseTime = Date.now() - now;
          this.logger.log(
            `[${requestId}] Response - ${method} ${url} - ${responseTime}ms`,
          );
        },
        error: (err) => {
          const responseTime = Date.now() - now;
          this.logger.error(
            `[${requestId}] Response Error - ${method} ${url} - ${responseTime}ms - ${err.message}`,
          );
        },
      }),
    );
  }
}
