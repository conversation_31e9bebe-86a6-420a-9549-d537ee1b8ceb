import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { HttpLoggerMiddleware } from './http-logger.middleware';
import { RequestIdMiddleware } from './request-id.middleware';
import { RequestLoggerMiddleware } from './request-logger.middleware';
import { TenantDatasourceMiddleware } from './tenant-datasource.middleware';
import { PrismaModule } from '../database/prisma/prisma.module';
import { LoggingModule } from '../logging/logging.module';

/**
 * 中间件模块
 * 提供应用程序中使用的所有中间件
 */
@Module({
  imports: [PrismaModule, LoggingModule, ConfigModule],
  providers: [
    TenantDatasourceMiddleware,
    RequestIdMiddleware,
    RequestLoggerMiddleware,
    HttpLoggerMiddleware,
  ],
  exports: [
    TenantDatasourceMiddleware,
    RequestIdMiddleware,
    RequestLoggerMiddleware,
    HttpLoggerMiddleware,
  ],
})
export class MiddlewareModule {}
