import { Injectable, Logger, NestMiddleware } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';

/**
 * 请求日志中间件
 * 记录每个请求的方法、URL、状态码和响应时间
 */
@Injectable()
export class RequestLoggerMiddleware implements NestMiddleware {
  private readonly logger = new Logger(RequestLoggerMiddleware.name);

  use(request: Request, response: Response, next: NextFunction): void {
    const { method, originalUrl } = request;
    const start = Date.now();

    const requestId = request.headers['x-request-id'] as string;

    response.on('finish', () => {
      const { statusCode } = response;
      const duration = Date.now() - start;
      this.logger.log(`${method} ${originalUrl} ${statusCode} - ${duration}ms [${requestId}]`);
    });

    next();
  }
}
