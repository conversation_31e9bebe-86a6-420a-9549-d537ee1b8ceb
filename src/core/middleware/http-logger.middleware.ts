import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

import { LoggingService } from '../logging/logging.service';

/**
 * HTTP请求日志中间件
 * 记录所有HTTP请求的详细信息
 */
@Injectable()
export class HttpLoggerMiddleware implements NestMiddleware {
  constructor(private readonly loggingService: LoggingService) {}

  use(req: Request, res: Response, next: NextFunction): void {
    // 记录请求开始时间
    const startTime = Date.now();

    // 请求结束时记录日志
    res.on('finish', () => {
      const duration = Date.now() - startTime;
      this.loggingService.logHttpRequest(req, res, duration);
    });

    next();
  }
}
