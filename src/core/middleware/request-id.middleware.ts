import { randomUUID } from 'crypto';

import { Injectable, NestMiddleware } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';

/**
 * 请求ID中间件
 * 为每个请求添加唯一的请求ID
 */
@Injectable()
export class RequestIdMiddleware implements NestMiddleware {
  use(request: Request, response: Response, next: NextFunction) {
    if (!request.headers['x-request-id']) {
      const requestId = randomUUID();
      request.headers['x-request-id'] = requestId;
    }
    next();
  }
}
