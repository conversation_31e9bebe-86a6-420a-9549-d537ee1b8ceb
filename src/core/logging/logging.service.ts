import { join } from 'path';

import { Injectable, LoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as winston from 'winston';
import * as DailyRotateFile from 'winston-daily-rotate-file';

/**
 * 日志服务
 * 提供统一的日志记录功能
 */
@Injectable()
export class LoggingService implements LoggerService {
  private logger: winston.Logger;

  constructor(private configService: ConfigService) {
    const logDir = join(process.cwd(), 'logs');
    const logLevel = this.configService?.get<string>('LOG_LEVEL') || 'info';
    const env = this.configService?.get<string>('NODE_ENV') || 'development';

    // 定义日志格式
    const logFormat = winston.format.combine(
      winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
      winston.format.errors({ stack: true }),
      winston.format.splat(),
      winston.format.json(),
    );

    // 控制台输出格式
    const consoleFormat = winston.format.combine(
      winston.format.colorize(),
      winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
      winston.format.printf(({ timestamp, level, message, context, trace, ...meta }) => {
        return `${timestamp} [${level}] ${context ? `[${context}]` : ''}: ${message} ${
          Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ''
        } ${trace || ''}`;
      }),
    );

    // 定义传输方式
    const transports: winston.transport[] = [
      // 控制台输出
      new winston.transports.Console({
        format: consoleFormat,
        level: env === 'production' ? 'info' : 'debug',
      }),

      // 按日期轮转的错误日志文件
      new DailyRotateFile({
        dirname: join(logDir, 'error'),
        filename: 'error-%DATE%.log',
        datePattern: 'YYYY-MM-DD',
        maxSize: '20m',
        maxFiles: '14d',
        level: 'error',
        format: logFormat,
      }),

      // 按日期轮转的所有日志文件
      new DailyRotateFile({
        dirname: join(logDir, 'combined'),
        filename: 'combined-%DATE%.log',
        datePattern: 'YYYY-MM-DD',
        maxSize: '20m',
        maxFiles: '14d',
        level: logLevel,
        format: logFormat,
      }),
    ];

    // 创建 Winston 日志实例
    this.logger = winston.createLogger({
      level: logLevel,
      format: logFormat,
      transports,
      exitOnError: false,
    });

    this.logger.info('日志服务已初始化', { service: 'LoggingService' });
  }

  /**
   * 记录调试级别日志
   * @param message 日志消息
   * @param context 上下文
   * @param meta 元数据
   */
  debug(message: any, context?: string, ...meta: any[]): void {
    this.logger.debug(message, { context, ...this.getMeta(meta) });
  }

  /**
   * 记录信息级别日志
   * @param message 日志消息
   * @param context 上下文
   * @param meta 元数据
   */
  log(message: any, context?: string, ...meta: any[]): void {
    this.logger.info(message, { context, ...this.getMeta(meta) });
  }

  /**
   * 记录警告级别日志
   * @param message 日志消息
   * @param context 上下文
   * @param meta 元数据
   */
  warn(message: any, context?: string, ...meta: any[]): void {
    this.logger.warn(message, { context, ...this.getMeta(meta) });
  }

  /**
   * 记录错误级别日志
   * @param message 日志消息
   * @param trace 错误堆栈
   * @param context 上下文
   * @param meta 元数据
   */
  error(message: any, trace?: string, context?: string, ...meta: any[]): void {
    this.logger.error(message, { trace, context, ...this.getMeta(meta) });
  }

  /**
   * 记录严重错误级别日志
   * @param message 日志消息
   * @param context 上下文
   * @param meta 元数据
   */
  fatal(message: any, context?: string, ...meta: any[]): void {
    this.logger.error(`FATAL: ${message}`, { context, ...this.getMeta(meta) });
  }

  /**
   * 记录HTTP请求日志
   * @param req 请求对象
   * @param res 响应对象
   * @param duration 请求处理时间(ms)
   */
  logHttpRequest(req: any, res: any, duration: number): void {
    const { method, originalUrl, ip, headers, user } = req;
    const userAgent = headers['user-agent'];
    const statusCode = res.statusCode;

    this.logger.info('HTTP请求', {
      context: 'HTTP',
      method,
      url: originalUrl,
      statusCode,
      duration: `${duration}ms`,
      ip,
      userAgent,
      userId: user?.userId,
      tenantId: user?.tenantId,
    });
  }

  /**
   * 记录数据库查询日志
   * @param query 查询语句
   * @param params 查询参数
   * @param duration 查询时间(ms)
   */
  logDatabaseQuery(query: string, params: string, duration: number): void {
    this.logger.debug('数据库查询', {
      context: 'Database',
      query,
      params,
      duration: `${duration}ms`,
    });
  }

  /**
   * 处理元数据
   * @param meta 元数据数组
   * @returns 处理后的元数据对象
   */
  private getMeta(meta: any[]): object {
    if (meta.length === 0) return {};
    if (meta.length === 1 && typeof meta[0] === 'object') return meta[0];
    return { meta };
  }
}
