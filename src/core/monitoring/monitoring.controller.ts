import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';

import { MonitoringService } from './monitoring.service';
import { AdminOnly } from '../auth/decorators/admin-only.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

/**
 * 监控控制器
 * 提供系统监控数据的API接口
 */
@ApiTags('系统监控')
@Controller('monitoring')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class MonitoringController {
  constructor(private readonly monitoringService: MonitoringService) {}

  /**
   * 获取系统指标
   * 仅管理员可访问
   */
  @Get('metrics')
  @AdminOnly()
  @ApiOperation({ summary: '获取系统指标' })
  getMetrics() {
    return {
      code: 0,
      data: this.monitoringService.getSystemMetrics(),
      message: '获取成功',
    };
  }

  /**
   * 获取监控数据
   * 仅管理员可访问
   */
  @Get('dashboard')
  @AdminOnly()
  @ApiOperation({ summary: '获取监控数据' })
  getDashboard() {
    return {
      code: 0,
      data: this.monitoringService.getMonitoringData(),
      message: '获取成功',
    };
  }
}
