import * as os from 'os';
import * as v8 from 'v8';

import { Injectable, OnModuleInit } from '@nestjs/common';

import { LoggingService } from '../logging/logging.service';

/**
 * 监控服务
 * 收集和提供系统性能指标
 */
@Injectable()
export class MonitoringService implements OnModuleInit {
  private startTime: number;
  private metrics: Map<string, any> = new Map();
  private slowRequests: any[] = [];
  private errorCounts: Map<string, number> = new Map();

  constructor(private readonly logger: LoggingService) {
    this.startTime = Date.now();
  }

  onModuleInit() {
    // 每分钟记录一次系统指标
    setInterval(() => this.recordSystemMetrics(), 60000);

    this.logger.log('监控服务已初始化', 'MonitoringService');
  }

  /**
   * 记录系统指标
   */
  private recordSystemMetrics() {
    const metrics = this.getSystemMetrics();
    this.logger.log('系统指标', 'Metrics', metrics);
  }

  /**
   * 获取系统指标
   * @returns 系统指标对象
   */
  getSystemMetrics() {
    const uptime = Math.floor((Date.now() - this.startTime) / 1000);

    const metrics = {
      uptime: {
        seconds: uptime,
        formatted: this.formatUptime(uptime),
      },
      memory: {
        total: os.totalmem(),
        free: os.freemem(),
        usage: (1 - os.freemem() / os.totalmem()) * 100,
      },
      cpu: {
        loadAvg: os.loadavg(),
        cores: os.cpus().length,
      },
      node: {
        version: process.version,
        heapUsed: process.memoryUsage().heapUsed,
        heapTotal: process.memoryUsage().heapTotal,
        external: process.memoryUsage().external,
        rss: process.memoryUsage().rss,
      },
      v8: {
        heapSpaceStats: v8.getHeapSpaceStatistics(),
        heapStats: v8.getHeapStatistics(),
      },
    };

    return metrics;
  }

  /**
   * 记录慢请求
   * @param request 请求信息
   * @param duration 请求处理时间(ms)
   */
  recordSlowRequest(request: any, duration: number) {
    this.slowRequests.push({
      method: request.method,
      url: request.url,
      duration,
      timestamp: new Date().toISOString(),
    });

    // 只保留最近100条慢请求记录
    if (this.slowRequests.length > 100) {
      this.slowRequests.shift();
    }
  }

  /**
   * 记录错误
   * @param path 请求路径
   * @param error 错误信息
   */
  recordError(path: string, error: any) {
    const count = this.errorCounts.get(path) || 0;
    this.errorCounts.set(path, count + 1);

    this.logger.error(`API错误: ${path}`, error.stack, 'MonitoringService');
  }

  /**
   * 获取监控数据
   * @returns 监控数据对象
   */
  getMonitoringData() {
    return {
      system: this.getSystemMetrics(),
      slowRequests: this.slowRequests.slice(-10), // 最近10条慢请求
      errorCounts: Object.fromEntries(this.errorCounts),
    };
  }

  /**
   * 格式化运行时间
   * @param seconds 秒数
   * @returns 格式化后的运行时间字符串
   */
  private formatUptime(seconds: number): string {
    const days = Math.floor(seconds / (3600 * 24));
    const hours = Math.floor((seconds % (3600 * 24)) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    return `${days}d ${hours}h ${minutes}m ${secs}s`;
  }
}
