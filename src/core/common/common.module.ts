import { Module } from '@nestjs/common';

import { HttpExceptionFilter } from './filters/http-exception.filter';
import { PerformanceInterceptor } from './interceptors/performance.interceptor';
import { TransformInterceptor } from './interceptors/transform.interceptor';
import { ParseIntOrUndefinedPipe } from './pipes/parse-int-or-undefined.pipe';
import { ValidationPipe } from './pipes/validation.pipe';
import { LoggingModule } from '../logging/logging.module';
import { QueryBuilderService } from './services/query-builder.service';

/**
 * 通用模块
 * 提供应用程序中使用的所有通用功能
 */
@Module({
  imports: [LoggingModule],
  providers: [
    HttpExceptionFilter,
    TransformInterceptor,
    PerformanceInterceptor,
    ParseIntOrUndefinedPipe,
    ValidationPipe,
    QueryBuilderService,
  ],
  exports: [
    HttpExceptionFilter,
    TransformInterceptor,
    PerformanceInterceptor,
    ParseIntOrUndefinedPipe,
    ValidationPipe,
    QueryBuilderService,
  ],
})
export class CommonModule {}
