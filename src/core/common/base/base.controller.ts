import { ApiCode, ApiMessage } from '../constants/api-code.constant';

/**
 * 基础控制器类
 * 提供通用的控制器方法
 */
export abstract class BaseController {
  /**
   * 从请求中获取用户上下文
   * @param req 请求对象
   * @returns 用户类型和租户ID
   */
  protected getUserContext(req: any): { userType: string; tenantId?: string; userId?: number } {
    // 从JWT中获取用户类型和租户ID
    const userType = req.user?.userType || 'SYSTEM';
    const tenantId = req.user?.tenantId;
    const userId = req.user?.userId ? parseInt(req.user.userId, 10) : undefined;

    return { userType, tenantId, userId };
  }

  /**
   * 创建成功响应
   * @param data 响应数据
   * @param message 响应消息
   * @returns 格式化的响应对象
   */
  protected success<T>(
    data: T,
    message: string = ApiMessage[ApiCode.SUCCESS],
  ): { code: number; data: T; message: string } {
    // 返回标准格式的响应
    return {
      code: ApiCode.SUCCESS,
      data,
      message,
    };
  }

  /**
   * 创建错误响应
   * @param code 错误代码
   * @param data 响应数据
   * @returns 格式化的响应对象
   */
  protected error<T>(
    code: number = ApiCode.ERROR,
    data: T = null,
  ): { code: number; data: T; message: string } {
    // 错误响应需要保持原格式，因为它们通常由异常过滤器处理
    return {
      code,
      data,
      message: ApiMessage[code] || ApiMessage[ApiCode.ERROR],
    };
  }
}
