import { Logger } from '@nestjs/common';

import { ForbiddenException } from '../exceptions/forbidden.exception';
import { ResourceExistsException } from '../exceptions/resource-exists.exception';
import { ResourceNotFoundException } from '../exceptions/resource-not-found.exception';
import { UnauthorizedException } from '../exceptions/unauthorized.exception';
import { ValidationException } from '../exceptions/validation.exception';
import { DateFormatUtil } from '../utils/date-format.util';
import { PaginationUtil } from '../utils/pagination.util';
import { QueryBuilderUtil } from '../utils/query-builder.util';

import { PaginationOptions } from '@/core/pagination-options/pagination-options';

/**
 * 基础服务类
 * 提供通用的服务方法
 */
export abstract class BaseService {
  protected readonly logger: Logger;

  constructor(context: string) {
    this.logger = new Logger(context);
  }

  /**
   * 构建查询条件
   * @param params 查询参数
   * @param stringFields 需要进行模糊查询的字符串字段数组
   * @returns 构建的查询条件
   */
  protected buildQuery(
    params: Record<string, any>,
    stringFields: string[] = [],
  ): Record<string, any> {
    return QueryBuilderUtil.buildQuery(params, stringFields);
  }

  /**
   * 创建分页选项
   * @param params 查询参数
   * @param defaultOrderBy 默认排序方式
   * @returns 分页选项
   */
  protected createPaginationOptions(
    params: Record<string, any>,
    defaultOrderBy: Record<string, 'asc' | 'desc'> = { createdAt: 'desc' },
  ): PaginationOptions {
    return PaginationUtil.createPaginationOptions(params, defaultOrderBy);
  }

  /**
   * 格式化日期为年月日时分秒格式
   * @param date 日期对象
   * @returns 格式化后的日期字符串
   */
  protected formatDate(date: Date): string {
    return DateFormatUtil.formatToDateTime(date);
  }

  /**
   * 处理空字符串字段
   * @param value 字段值
   * @returns 处理后的值
   */
  protected handleEmptyString(value: string | null | undefined): string | null {
    return value === '' ? null : value;
  }

  /**
   * 安全解析整数
   * @param value 要解析的值
   * @param defaultValue 默认值
   * @returns 解析后的整数
   */
  protected safeParseInt(value: any, defaultValue: number = 0): number {
    if (value === undefined || value === null) {
      return defaultValue;
    }

    const parsed = typeof value === 'string' ? parseInt(value, 10) : value;
    return !isNaN(parsed) ? parsed : defaultValue;
  }

  /**
   * 记录错误日志
   * @param message 错误消息
   * @param error 错误对象
   */
  protected logError(message: string, error: any): void {
    // 安全处理错误对象
    if (!error) {
      this.logger.error(`${message}: 未知错误`);
      return;
    }

    const errorMessage = error.message || '未知错误';
    const errorStack = error.stack || '';

    this.logger.error(`${message}: ${errorMessage}`, errorStack);
  }

  /**
   * 抛出资源未找到异常
   * @param resourceName 资源名称
   * @param resourceId 资源ID
   */
  protected notFound(resourceName: string, resourceId?: string | number): never {
    throw new ResourceNotFoundException(resourceName, resourceId);
  }

  /**
   * 抛出资源已存在异常
   * @param resourceName 资源名称
   * @param field 字段名称
   * @param value 字段值
   */
  protected alreadyExists(resourceName: string, field: string, value: string | number): never {
    throw new ResourceExistsException(resourceName, field, value);
  }

  /**
   * 抛出验证异常
   * @param message 错误消息
   * @param errors 验证错误详情
   */
  protected validationError(message: string = '数据验证失败', errors?: any): never {
    throw new ValidationException(message, errors);
  }

  /**
   * 抛出禁止访问异常
   * @param message 错误消息
   */
  protected forbidden(message: string = '无权限访问'): never {
    throw new ForbiddenException(message);
  }

  /**
   * 抛出未授权异常
   * @param message 错误消息
   */
  protected unauthorized(message: string = '未授权访问'): never {
    throw new UnauthorizedException(message);
  }

  /**
   * 检查资源是否存在，不存在则抛出异常
   * @param exists 资源是否存在
   * @param resourceName 资源名称
   * @param resourceId 资源ID
   */
  protected checkExists(exists: boolean, resourceName: string, resourceId?: string | number): void {
    if (!exists) {
      this.notFound(resourceName, resourceId);
    }
  }

  /**
   * 检查资源是否已存在，已存在则抛出异常
   * @param exists 资源是否已存在
   * @param resourceName 资源名称
   * @param field 字段名称
   * @param value 字段值
   */
  protected checkNotExists(
    exists: boolean,
    resourceName: string,
    field: string,
    value: string | number,
  ): void {
    if (exists) {
      this.alreadyExists(resourceName, field, value);
    }
  }

  /**
   * 检查用户是否有权限，没有则抛出异常
   * @param hasPermission 是否有权限
   * @param message 错误消息
   */
  protected checkPermission(hasPermission: boolean, message: string = '无权限执行此操作'): void {
    if (!hasPermission) {
      this.forbidden(message);
    }
  }
}
