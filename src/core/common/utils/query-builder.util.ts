/**
 * 查询构建工具
 * 用于构建通用的查询条件
 */
export class QueryBuilderUtil {
  /**
   * 构建基础查询条件
   * @param params 查询参数
   * @returns 构建的查询条件
   */
  static buildBaseQuery(params: Record<string, any>): Record<string, any> {
    const query: Record<string, any> = {};

    // 处理ID精确查询
    if (params.id) {
      query.id = params.id;
    }

    // 处理状态精确查询
    if (params.status !== undefined && params.status !== null) {
      const status =
        typeof params.status === 'string' ? parseInt(params.status, 10) : params.status;

      if (!isNaN(status)) {
        query.status = status;
      }
    }

    return query;
  }

  /**
   * 构建字符串字段的模糊查询条件
   * @param query 查询条件对象
   * @param params 查询参数
   * @param fields 需要进行模糊查询的字段数组
   * @returns 更新后的查询条件
   */
  static buildStringQuery(
    query: Record<string, any>,
    params: Record<string, any>,
    fields: string[],
  ): Record<string, any> {
    for (const field of fields) {
      if (params[field]) {
        query[field] = { contains: params[field] };
      }
    }

    return query;
  }

  /**
   * 构建日期范围查询条件
   * @param query 查询条件对象
   * @param params 查询参数
   * @param dateField 日期字段名称，默认为 'createdAt'
   * @param startField 开始日期参数名称，默认为 'startTime'
   * @param endField 结束日期参数名称，默认为 'endTime'
   * @returns 更新后的查询条件
   */
  static buildDateRangeQuery(
    query: Record<string, any>,
    params: Record<string, any>,
    dateField: string = 'createdAt',
    startField: string = 'startTime',
    endField: string = 'endTime',
  ): Record<string, any> {
    if (params[startField] || params[endField]) {
      query[dateField] = {};

      if (params[startField]) {
        // 设置开始时间为当天的 00:00:00
        const startDate = new Date(params[startField]);
        startDate.setHours(0, 0, 0, 0);
        query[dateField].gte = startDate;
      }

      if (params[endField]) {
        // 设置结束时间为当天的 23:59:59
        const endDate = new Date(params[endField]);
        endDate.setHours(23, 59, 59, 999);
        query[dateField].lte = endDate;
      }
    }

    return query;
  }

  /**
   * 构建完整的查询条件
   * @param params 查询参数
   * @param stringFields 需要进行模糊查询的字符串字段数组
   * @param dateField 日期字段名称，默认为 'createdAt'
   * @returns 构建的查询条件
   */
  static buildQuery(
    params: Record<string, any>,
    stringFields: string[] = [],
    dateField: string = 'createdAt',
  ): Record<string, any> {
    let query = this.buildBaseQuery(params);

    // 构建字符串字段的模糊查询
    query = this.buildStringQuery(query, params, stringFields);

    // 构建日期范围查询
    query = this.buildDateRangeQuery(query, params, dateField);

    return query;
  }
}
