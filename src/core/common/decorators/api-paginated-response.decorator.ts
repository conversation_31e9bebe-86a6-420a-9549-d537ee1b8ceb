import { applyDecorators, Type } from '@nestjs/common';
import { ApiExtraModels, ApiOkResponse, getSchemaPath } from '@nestjs/swagger';

import { PaginationMeta } from '../interfaces/api-response.interface';

/**
 * 分页响应装饰器
 * 用于在Swagger文档中生成分页响应的模型
 *
 * 使用示例：
 * ```typescript
 * @Get()
 * @ApiPaginatedResponse(UserDto)
 * async findAll(): Promise<{ items: UserDto[], meta: PaginationMeta }> {
 *   // ...
 * }
 * ```
 *
 * @param model 分页项的类型
 * @returns 装饰器
 */
export const ApiPaginatedResponse = <TModel extends Type<any>>(model: TModel) => {
  return applyDecorators(
    ApiExtraModels(model),
    ApiOkResponse({
      schema: {
        properties: {
          items: {
            type: 'array',
            items: { $ref: getSchemaPath(model) },
          },
          meta: {
            type: 'object',
            properties: {
              page: {
                type: 'number',
                example: 1,
              },
              pageSize: {
                type: 'number',
                example: 10,
              },
              total: {
                type: 'number',
                example: 100,
              },
              totalPages: {
                type: 'number',
                example: 10,
              },
            },
          },
        },
      },
    }),
  );
};
