import { Injectable, Logger } from '@nestjs/common';

/**
 * 查询条件构建器服务
 * 用于构建Prisma查询条件
 */
@Injectable()
export class QueryBuilderService {
  private readonly logger = new Logger(QueryBuilderService.name);

  /**
   * 构建用户查询条件
   * @param params 查询参数
   * @param userType 用户类型
   * @returns 构建的查询条件
   */
  buildUserQueryCondition(params: Record<string, any>, userType: string): Record<string, any> {
    // 构建查询条件
    const where: Record<string, any> = {};

    // 添加基本查询条件
    if (params.username && params.username.trim() !== '') {
      where.username = { contains: params.username };
    }

    if (params.realName && params.realName.trim() !== '') {
      where.realName = { contains: params.realName };
    }

    if (params.email && params.email.trim() !== '') {
      // 系统用户和租户用户的邮箱字段名不同
      if (userType === 'SYSTEM') {
        where.email = { contains: params.email };
      } else {
        where.emailAddress = { contains: params.email };
      }
    }

    // 只有当status是有效的数字时才添加到查询条件中
    if (params.status !== undefined && params.status !== null && !isNaN(Number(params.status))) {
      where.status = Number(params.status);
    }

    // 添加新增字段的查询条件，确保值不为空
    if (params.phoneNumber && params.phoneNumber.trim() !== '') {
      where.phoneNumber = { contains: params.phoneNumber };
    }

    if (params.idCardNumber && params.idCardNumber.trim() !== '') {
      where.idCardNumber = { contains: params.idCardNumber };
    }

    if (params.openid && params.openid.trim() !== '') {
      where.openid = { contains: params.openid };
    }

    if (params.unionid && params.unionid.trim() !== '') {
      where.unionid = { contains: params.unionid };
    }

    // 添加时间范围查询条件，确保日期有效
    if (
      (params.startTime && params.startTime.trim() !== '') ||
      (params.endTime && params.endTime.trim() !== '')
    ) {
      where.createdAt = {};

      if (params.startTime && params.startTime.trim() !== '') {
        try {
          const startDate = new Date(params.startTime);
          startDate.setHours(0, 0, 0, 0); // 设置为当天的开始时间
          where.createdAt.gte = startDate;
        } catch (error) {
          this.logger.warn(`无效的开始时间格式: ${params.startTime}`);
        }
      }

      if (params.endTime && params.endTime.trim() !== '') {
        try {
          const endDate = new Date(params.endTime);
          endDate.setHours(23, 59, 59, 999); // 设置为当天的结束时间
          where.createdAt.lte = endDate;
        } catch (error) {
          this.logger.warn(`无效的结束时间格式: ${params.endTime}`);
        }
      }

      // 如果没有有效的时间条件，删除createdAt条件
      if (Object.keys(where.createdAt).length === 0) {
        delete where.createdAt;
      }
    }

    // 记录构建的查询条件，便于调试
    this.logger.debug(`构建的用户查询条件: ${JSON.stringify(where)}`);

    return where;
  }

  /**
   * 构建角色查询条件
   * @param params 查询参数
   * @returns 构建的查询条件
   */
  buildRoleQueryCondition(params: Record<string, any>): Record<string, any> {
    // 构建查询条件
    const where: Record<string, any> = {};

    // ID 精确查询
    if (params.id && params.id.trim() !== '') {
      where.id = params.id;
    }

    // 名称模糊查询
    if (params.name && params.name.trim() !== '') {
      where.name = { contains: params.name };
    }

    // 描述模糊查询
    if (params.remark && params.remark.trim() !== '') {
      where.remark = { contains: params.remark };
    }

    // 状态精确匹配
    if (params.status !== undefined && params.status !== null && !isNaN(Number(params.status))) {
      where.status = Number(params.status);
    }

    // 添加时间范围查询条件，确保日期有效
    if (
      (params.startTime && params.startTime.trim() !== '') ||
      (params.endTime && params.endTime.trim() !== '')
    ) {
      where.createdAt = {};

      if (params.startTime && params.startTime.trim() !== '') {
        try {
          const startDate = new Date(params.startTime);
          startDate.setHours(0, 0, 0, 0); // 设置为当天的开始时间
          where.createdAt.gte = startDate;
        } catch (error) {
          this.logger.warn(`无效的开始时间格式: ${params.startTime}`);
        }
      }

      if (params.endTime && params.endTime.trim() !== '') {
        try {
          const endDate = new Date(params.endTime);
          endDate.setHours(23, 59, 59, 999); // 设置为当天的结束时间
          where.createdAt.lte = endDate;
        } catch (error) {
          this.logger.warn(`无效的结束时间格式: ${params.endTime}`);
        }
      }

      // 如果没有有效的时间条件，删除createdAt条件
      if (Object.keys(where.createdAt).length === 0) {
        delete where.createdAt;
      }
    }

    // 记录构建的查询条件，便于调试
    this.logger.debug(`构建的角色查询条件: ${JSON.stringify(where)}`);

    return where;
  }

  /**
   * 构建部门查询条件
   * @param params 查询参数
   * @returns 构建的查询条件
   */
  buildDepartmentQueryCondition(params: Record<string, any>): Record<string, any> {
    // 构建查询条件
    const where: Record<string, any> = {};

    // ID 精确查询
    if (params.id && !isNaN(Number(params.id))) {
      where.id = Number(params.id);
    }

    // 名称模糊查询
    if (params.name && params.name.trim() !== '') {
      where.name = { contains: params.name };
    }

    // 父部门ID精确匹配
    if (params.pid !== undefined && params.pid !== null && !isNaN(Number(params.pid))) {
      where.pid = Number(params.pid);
    }

    // 状态精确匹配
    if (params.status !== undefined && params.status !== null && !isNaN(Number(params.status))) {
      where.status = Number(params.status);
    }

    // 添加时间范围查询条件，确保日期有效
    if (
      (params.startTime && params.startTime.trim() !== '') ||
      (params.endTime && params.endTime.trim() !== '')
    ) {
      where.createdAt = {};

      if (params.startTime && params.startTime.trim() !== '') {
        try {
          const startDate = new Date(params.startTime);
          startDate.setHours(0, 0, 0, 0); // 设置为当天的开始时间
          where.createdAt.gte = startDate;
        } catch (error) {
          this.logger.warn(`无效的开始时间格式: ${params.startTime}`);
        }
      }

      if (params.endTime && params.endTime.trim() !== '') {
        try {
          const endDate = new Date(params.endTime);
          endDate.setHours(23, 59, 59, 999); // 设置为当天的结束时间
          where.createdAt.lte = endDate;
        } catch (error) {
          this.logger.warn(`无效的结束时间格式: ${params.endTime}`);
        }
      }

      // 如果没有有效的时间条件，删除createdAt条件
      if (Object.keys(where.createdAt).length === 0) {
        delete where.createdAt;
      }
    }

    // 记录构建的查询条件，便于调试
    this.logger.debug(`构建的部门查询条件: ${JSON.stringify(where)}`);

    return where;
  }
}
