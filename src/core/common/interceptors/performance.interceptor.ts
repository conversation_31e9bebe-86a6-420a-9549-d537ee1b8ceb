import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

import { LoggingService } from '../../logging/logging.service';

/**
 * 性能监控拦截器
 * 记录请求处理时间，监控API性能
 */
@Injectable()
export class PerformanceInterceptor implements NestInterceptor {
  constructor(private readonly logger: LoggingService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const { method, url } = request;
    const className = context.getClass().name;
    const handlerName = context.getHandler().name;

    const now = Date.now();

    return next.handle().pipe(
      tap(() => {
        const duration = Date.now() - now;

        // 记录性能数据
        this.logger.log(`${method} ${url} ${duration}ms`, 'Performance', {
          controller: className,
          handler: handlerName,
          duration,
          method,
          url,
        });

        // 如果请求处理时间超过阈值，记录警告
        if (duration > 1000) {
          this.logger.warn(`慢请求: ${method} ${url} ${duration}ms`, 'Performance', {
            controller: className,
            handler: handlerName,
            duration,
            method,
            url,
          });
        }
      }),
    );
  }
}
