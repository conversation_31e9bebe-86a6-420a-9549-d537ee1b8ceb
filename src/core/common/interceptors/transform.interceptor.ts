import { Injectable, NestInterceptor, ExecutionContext, CallHandler, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { ApiCode, ApiMessage } from '../constants/api-code.constant';

/**
 * 统一响应格式接口
 */
export interface Response<T> {
  code: number;
  data: T;
  message: string;
}

/**
 * 响应转换拦截器
 * 将所有响应转换为统一的格式 { code, data, message }
 */
@Injectable()
export class TransformInterceptor<T> implements NestInterceptor<T, Response<T>> {
  private readonly logger = new Logger(TransformInterceptor.name);
  intercept(context: ExecutionContext, next: CallHandler): Observable<Response<T>> {
    const req = context.switchToHttp().getRequest();
    const url = req.url;

    return next.handle().pipe(
      map(data => {
        try {
          // 记录原始响应数据类型
          this.logger.debug(`响应数据类型: ${typeof data}, URL: ${url}`);

          // 检查数据是否已经是标准格式
          const isStandardFormat =
            data &&
            typeof data === 'object' &&
            'code' in data &&
            typeof data.code === 'number' &&
            'data' in data &&
            'message' in data;

          // 如果数据已经是标准格式，则直接返回
          if (isStandardFormat) {
            this.logger.debug(`检测到标准响应格式，直接返回, URL: ${url}`);
            return data;
          }

          // 否则转换为标准格式
          this.logger.debug(`转换为标准响应格式, URL: ${url}`);
          return {
            code: ApiCode.SUCCESS,
            data,
            message: ApiMessage[ApiCode.SUCCESS],
          };
        } catch (error) {
          this.logger.error(`转换响应格式失败: ${error.message}, URL: ${url}`);
          // 出错时返回原始数据
          return {
            code: ApiCode.SUCCESS,
            data,
            message: ApiMessage[ApiCode.SUCCESS],
          };
        }
      }),
    );
  }
}
