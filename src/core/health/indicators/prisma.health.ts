import { Injectable } from '@nestjs/common';
import { HealthIndicator, HealthIndicatorResult, HealthCheckError } from '@nestjs/terminus';

import { PublicPrismaService } from '../../database/prisma/public-prisma.service';

/**
 * Prisma 健康检查指示器
 * 检查数据库连接状态
 */
@Injectable()
export class PrismaHealthIndicator extends HealthIndicator {
  constructor(private readonly prisma: PublicPrismaService) {
    super();
  }

  /**
   * 检查 Prisma 数据库连接健康状态
   * @param key 健康检查键名
   * @returns 健康检查结果
   */
  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    try {
      // 执行简单查询测试数据库连接
      await this.prisma.$queryRaw`SELECT 1`;

      return this.getStatus(key, true);
    } catch (error) {
      throw new HealthCheckError(
        '数据库健康检查失败',
        this.getStatus(key, false, { message: error.message }),
      );
    }
  }
}
