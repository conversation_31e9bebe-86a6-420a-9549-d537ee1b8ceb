import { Controller, Get } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import {
  HealthCheck,
  HealthCheckService,
  DiskHealthIndicator,
  MemoryHealthIndicator,
  HttpHealthIndicator,
} from '@nestjs/terminus';

import { PrismaHealthIndicator } from './indicators/prisma.health';
import { Public } from '../auth/decorators/public.decorator';

/**
 * 健康检查控制器
 * 提供应用程序各组件的健康状态检查
 */
@ApiTags('健康检查')
@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private http: HttpHealthIndicator,
    private disk: DiskHealthIndicator,
    private memory: MemoryHealthIndicator,
    private prismaHealth: PrismaHealthIndicator,
    private configService: ConfigService,
  ) {}

  /**
   * 全面健康检查
   * 检查所有关键组件的健康状态
   */
  @Get()
  @Public()
  @HealthCheck()
  @ApiOperation({ summary: '系统健康检查' })
  check() {
    const port = this.configService.get<number>('PORT') || 3000;

    return this.health.check([
      // 数据库健康检查
      () => this.prismaHealth.isHealthy('database'),

      // 磁盘空间检查 - 确保至少有250MB可用空间
      () => this.disk.checkStorage('storage', { path: '/', thresholdPercent: 0.75 }),

      // 内存使用检查 - 确保堆内存使用不超过150MB
      () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024),

      // 内存使用检查 - 确保RSS内存使用不超过300MB
      () => this.memory.checkRSS('memory_rss', 300 * 1024 * 1024),

      // 自检 - 确保API服务器可以访问自己
      () => this.http.pingCheck('api', `http://localhost:${port}/api/health/ping`),
    ]);
  }

  /**
   * 简单的 ping 检查
   * 用于基本的可用性检查
   */
  @Get('ping')
  @Public()
  @ApiOperation({ summary: 'Ping检查' })
  ping() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
    };
  }
}
