import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import { Prisma, PrismaClient } from '@prisma/client';

@Injectable()
export class TenantPrismaService extends PrismaClient implements OnModuleInit {
  private readonly logger = new Logger(TenantPrismaService.name);

  constructor(datasourceUrl: string) {
    super({
      datasourceUrl,
      log: [
        {
          emit: 'event',
          level: 'query',
        },
        {
          emit: 'event',
          level: 'error',
        },
        {
          emit: 'event',
          level: 'info',
        },
        {
          emit: 'event',
          level: 'warn',
        },
      ],
    });
  }

  withQueryExtensions(tenantId: number) {
    return this.$extends({
      query: {
        $allOperations({ model, operation, args, query }) {
          // 只对查询操作添加租户ID过滤
          if (
            operation === 'findMany' ||
            operation === 'findFirst' ||
            operation === 'findUnique' ||
            operation === 'count'
          ) {
            // 添加租户ID过滤
            return query({
              ...args,
              where: {
                ...args.where,
                tenantId: tenantId, // 使用租户的数字ID
              },
            });
          }

          // 对于更新和删除操作，不修改查询
          return query(args);
        },
      },
    });
  }

  async onModuleInit() {
    this.$on('query' as never, (e: Prisma.QueryEvent) => {
      this.logger.debug(`Query: ${e.query}`);
      this.logger.debug(`Params: ${e.params}`);
      this.logger.debug(`Duration: ${e.duration}ms`);
    });

    await this.$connect();
  }
}

export const TENANT_PRISMA_SERVICE = TenantPrismaService.name;
