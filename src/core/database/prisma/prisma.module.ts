import * as process from 'process';

import {
  BadRequestException,
  Global,
  Module,
  NotFoundException,
  Scope,
  Logger,
} from '@nestjs/common';
import { REQUEST } from '@nestjs/core';

import { PublicPrismaService } from './public-prisma.service';
import { TENANT_PRISMA_SERVICE, TenantPrismaService } from './tenant-prisma.service';

import { isSystemPath } from '@/core/common/constants/system-paths.constant';
import { IRequestWithProps } from '@/core/common/interfaces/request-with-props.interface';

@Global()
@Module({
  exports: [PublicPrismaService, TENANT_PRISMA_SERVICE],
  providers: [
    PublicPrismaService,
    {
      provide: TENANT_PRISMA_SERVICE,
      scope: Scope.REQUEST,
      inject: [REQUEST],
      useFactory: (request: IRequestWithProps) => {
        const logger = new Logger('TenantPrismaFactory');

        // 检查request是否存在
        if (!request) {
          logger.log('TENANT_PRISMA_SERVICE 工厂函数被调用，但没有请求上下文');
          return null;
        }

        logger.log(`TENANT_PRISMA_SERVICE 工厂函数被调用，URL: ${request.url || 'undefined'}`);

        const tenant = request.tenant;
        const url = request.url || '';
        const method = request.method || '';

        logger.debug(`处理请求: ${method} ${url}`);
        if (tenant) {
          logger.debug(
            `请求中的租户信息: tenantCode=${tenant.tenantCode}, datasourceUrl=${tenant.datasourceUrl}`,
          );
        } else {
          logger.debug('请求中没有租户信息');
        }

        // 检查是否是系统用户请求

        // 1. 检查请求路径是否是系统路径
        const pathIsSystem = isSystemPath(url);
        logger.debug(`是否是系统路径: ${pathIsSystem}`);

        // 2. 检查用户类型
        const userType = request.user?.userType;
        const isSystemUser = userType === 'SYSTEM';
        logger.debug(`用户类型: ${userType}, 是否是系统用户: ${isSystemUser}`);

        // 3. 检查请求体中是否有租户代码
        let hasNoTenantCode = false;
        const hasTenantInRequest = !!tenant; // 检查请求中是否已经有租户信息（通过域名识别）

        logger.debug(`请求中是否有租户信息: ${hasTenantInRequest}`);

        if (method === 'POST' && (url.includes('/auth/login') || url.includes('/api/auth/login'))) {
          try {
            const body = request.body || {};
            logger.debug(`登录请求体: ${JSON.stringify(body)}`);

            // 只有在请求中没有租户信息的情况下，才检查请求体中是否有租户代码
            if (!hasTenantInRequest) {
              hasNoTenantCode = !body.tenantCode;
              logger.debug(`请求体中是否没有租户代码: ${hasNoTenantCode}`);
            }
          } catch (error) {
            logger.error(`解析请求体失败: ${error.message}`);
            // 只有在请求中没有租户信息的情况下，才设置hasNoTenantCode为true
            if (!hasTenantInRequest) {
              hasNoTenantCode = true;
              logger.debug('解析失败，设置hasNoTenantCode为true');
            }
          }
        }

        // 检查用户类型是否为租户用户
        const isTenantUser = userType === 'TENANT';

        // 检查是否是登录请求
        const isLoginRequest =
          method === 'POST' && (url.includes('/auth/login') || url.includes('/api/auth/login'));

        // 如果是登录请求且请求中有租户信息，则需要租户数据库
        const isLoginWithTenant = isLoginRequest && hasTenantInRequest;

        // 如果请求中有租户信息且是租户用户，则需要租户数据库
        const isTenantUserWithTenant = isTenantUser && hasTenantInRequest;

        // 如果是租户用户或请求中有租户信息，总是需要租户数据库
        if (isTenantUser || hasTenantInRequest) {
          logger.log(
            `租户用户访问或请求中有租户信息，需要租户数据库: userType=${userType}, url=${url}, hasTenantInRequest=${hasTenantInRequest}`,
          );
          // 继续执行，创建租户数据库连接
        }
        // 如果是系统用户，或者（没有租户代码且请求中没有租户信息），则不需要租户数据库
        else if (isSystemUser || (hasNoTenantCode && !hasTenantInRequest)) {
          logger.debug(
            `不需要租户数据库: isSystemUser=${isSystemUser}, hasNoTenantCode=${hasNoTenantCode}, hasTenantInRequest=${hasTenantInRequest}, isLoginWithTenant=${isLoginWithTenant}, isTenantUser=${isTenantUser}, isTenantUserWithTenant=${isTenantUserWithTenant}`,
          );
          return null;
        }

        logger.debug(
          `需要租户数据库: pathIsSystem=${pathIsSystem}, isSystemUser=${isSystemUser}, hasNoTenantCode=${hasNoTenantCode}, hasTenantInRequest=${hasTenantInRequest}, isLoginWithTenant=${isLoginWithTenant}, isTenantUser=${isTenantUser}, isTenantUserWithTenant=${isTenantUserWithTenant}`,
        );

        // 对于其他需要租户信息的请求
        if (!tenant) {
          logger.error('需要租户数据库但请求中没有租户信息');
          throw new BadRequestException('Invalid tenant code.');
        }

        const { tenantCode, datasourceUrl, tenantId } = tenant;
        logger.debug(`租户信息: tenantCode=${tenantCode}, datasourceUrl=${datasourceUrl}`);

        if (!datasourceUrl) {
          logger.warn(`租户 ${tenantCode} 没有数据源URL，将使用默认数据源URL`);
          // 使用默认数据源URL
          const defaultDatasourceUrl = process.env.DATABASE_URL;
          logger.debug(`使用默认数据源URL: ${defaultDatasourceUrl}`);

          // 如果默认数据源URL也不存在，则抛出异常
          if (!defaultDatasourceUrl) {
            logger.error(`租户 ${tenantCode} 没有数据源URL，且默认数据源URL也不存在`);
            throw new NotFoundException('This tenant has no datasource.');
          }

          // 使用默认数据源URL
          tenant.datasourceUrl = defaultDatasourceUrl;
        }

        logger.debug(`为租户 ${tenantCode} 创建Prisma服务，数据源URL: ${tenant.datasourceUrl}`);
        try {
          // 如果tenantId不存在，尝试将tenantCode转换为数字
          const tenantIdNumber = tenantId ? parseInt(tenantId.toString()) : parseInt(tenantCode);
          const prismaService = new TenantPrismaService(tenant.datasourceUrl).withQueryExtensions(
            tenantIdNumber,
          );
          logger.debug(`成功创建租户 ${tenantCode} (ID: ${tenantIdNumber}) 的Prisma服务`);
          return prismaService;
        } catch (error) {
          logger.error(`创建租户 ${tenantCode} 的Prisma服务失败: ${error.message}`);
          throw error;
        }
      },
    },
  ],
})
export class PrismaModule {}
