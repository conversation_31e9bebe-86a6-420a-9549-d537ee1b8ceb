import { ValidationPipe, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as bodyParser from 'body-parser';
import helmet from 'helmet';
import * as metadata from 'package.json';

import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  const configService = app.get(ConfigService);

  const swaggerConfig = new DocumentBuilder()
    .setTitle(metadata.name)
    .setVersion(metadata.version)
    .setDescription(metadata.description)
    .addBearerAuth() // 添加Bearer认证支持
    .build();
  const document = SwaggerModule.createDocument(app, swaggerConfig);
  SwaggerModule.setup('api/docs', app, document);

  app.use(helmet());

  // 使用 body-parser 中间件解析请求体
  app.use(bodyParser.json());
  app.use(bodyParser.urlencoded({ extended: true }));

  app.enableCors({
    origin: configService.get('CORS_ORIGINS'),
  });

  // 设置全局路由前缀
  app.setGlobalPrefix('api');

  // 全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true, // 自动删除非DTO中定义的属性
      transform: true, // 自动转换类型
      forbidNonWhitelisted: false, // 允许非白名单属性
      transformOptions: {
        enableImplicitConversion: true, // 启用隐式类型转换
      },
      // 显示详细的验证错误信息
      exceptionFactory: errors => {
        const messages = errors
          .map(error => {
            const constraints = error.constraints || {};
            return Object.values(constraints).join(', ');
          })
          .join('; ');
        return new BadRequestException(messages || 'Validation failed');
      },
    }),
  );

  // 从配置服务获取端口
  const port = configService.get('PORT');

  await app.listen(port);

  // 只在开发环境打印详细信息
  const nodeEnv = configService.get('NODE_ENV');
  if (nodeEnv === 'development') {
    console.log(`应用程序正在运行 (${nodeEnv} 环境)`);
    console.log(`本地访问: http://localhost:${port}`);
    console.log(`Swagger 文档: http://localhost:${port}/api/docs`);
  }
}
bootstrap();
