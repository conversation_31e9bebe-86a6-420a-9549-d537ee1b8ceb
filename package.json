{"name": "multi-tenant-nestjs", "version": "0.1.0", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"dev": "nest start --watch", "start": "nest start", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main", "build": "nest build && node copy-cache-dashboard.js", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\" \"test/**/*.ts\"", "format:all": "prettier --write \"**/*.{ts,js,json,md}\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "db:push:tenant": "prisma db push", "db:generate:tenant": "prisma generate", "db:push:public": "prisma db push --schema prisma/public-schema.prisma", "db:generate:public": "prisma generate --schema prisma/public-schema.prisma", "db:seed": "node prisma/seed.js", "db:reset": "pnpm db:reset:all && pnpm db:seed", "db:reset:tenant": "prisma db push --force-reset", "db:reset:public": "prisma db push --schema prisma/public-schema.prisma --force-reset", "db:reset:all": "pnpm db:reset:tenant && pnpm db:reset:public", "db:update-passwords": "node scripts/update-password-hashes.js", "db:export-feature-codes": "node scripts/export-feature-codes.js", "db:import-feature-codes": "node scripts/import-feature-codes.js", "postinstall": "pnpm db:generate:tenant && pnpm db:generate:public", "prepare": "husky install", "lint-staged": "lint-staged"}, "dependencies": {"@nestjs/axios": "^4.0.0", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^2.1.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^7.4.0", "@nestjs/terminus": "^10.1.1", "@prisma/client": "6.7.0", "@types/body-parser": "^1.19.5", "@types/passport-jwt": "^4.0.1", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "cache-manager": "^6.4.3", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "crypto": "^1.0.1", "dotenv": "^16.5.0", "helmet": "^7.1.0", "ioredis": "^5.6.1", "joi": "^17.13.3", "nestjs-pino": "^4.4.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pino-pretty": "^13.0.0", "redis": "^5.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.3", "@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcryptjs": "^3.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^8.0.3", "jest": "^29.5.0", "lint-staged": "^15.5.2", "prettier": "^3.0.0", "prisma": "^6.7.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^@core/(.*)$": "<rootDir>/core/$1", "^@modules/(.*)$": "<rootDir>/modules/$1", "^@controllers/(.*)$": "<rootDir>/modules/controllers/$1"}}, "prisma": {"seed": "node prisma/seed.js"}, "lint-staged": {"*.{ts,js}": ["eslint --fix", "prettier --write"], "*.{json,md,yml}": ["prettier --write"]}}