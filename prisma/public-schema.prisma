generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/@prisma-public/prisma/client"
}

datasource db {
  provider = "postgresql"
  url      = env("PUBLIC_DATABASE_URL")
}

// 租户相关模型
model Tenant {
  id           Int         @id @default(autoincrement())
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @default(now())
  code         String      @unique
  name         String
  website      String?
  domain       String?     @unique // 租户自定义域名，用于通过域名识别租户
  status       Int         @default(1) // 0-禁用，1-启用
  metadata     Json?
  datasource   Datasource? @relation(fields: [datasourceId], references: [id])
  datasourceId Int?

  // 关联字段
  features     TenantFeature[]
  featureUsages TenantFeatureUsage[]
  configs      TenantConfig[]

  @@index([name])
  @@index([status])
  @@index([datasourceId])
  @@index([domain])
}

model Datasource {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())
  name      String
  url       String
  metadata  Json?
  tenants   Tenant[]
  // Multiple tenants can share the same database.
  // Their data will be segragated using "tenantId" column in each table.
  // The "tenantId" column stores the tenant's numeric ID, not the tenant code.
  // This is handled using an extension query on the Prisma client.
}

// 系统用户相关模型
model SystemUser {
  id           Int               @id @default(autoincrement())
  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @updatedAt
  username     String            @unique
  password     String
  realName     String?
  email        String?           @unique
  avatar       String?
  status       Int               @default(1) // 0-禁用，1-启用
  lastLoginAt  DateTime?

  // 新增基本预留字段
  phoneNumber  String?           // 手机号
  idCardNumber String?           // 身份证号
  openid       String?           // 微信openid
  unionid      String?           // 微信unionid
  adminRemark  String?           // 管理员备注

  // 关联字段
  userRoles    SystemUserRole[]
  extension    SystemUserExtension?
  remarks      SystemUserRemark[] @relation("SystemUserRemarks")
  remarkedBy   SystemUserRemark[] @relation("SystemUserRemarkTargets")
  userTags     SystemUserTagRelation[]

  @@index([status])
  @@index([realName])
  @@index([phoneNumber])
  @@index([idCardNumber])
  @@index([openid])
}

model SystemRole {
  id           String                @id @default(uuid())
  createdAt    DateTime              @default(now())
  updatedAt    DateTime              @updatedAt
  name         String
  code         String
  remark       String?
  status       Int                   @default(1) // 0-禁用，1-启用
  userRoles    SystemUserRole[]
  roleMenus    SystemRoleMenu[]
  rolePermissions SystemRolePermission[]

  @@index([name])
  @@index([code])
  @@index([status])
}

model SystemPermission {
  id           Int                   @id @default(autoincrement())
  createdAt    DateTime              @default(now())
  updatedAt    DateTime              @updatedAt
  name         String
  code         String                @unique // 权限码，如 system:user:create
  description  String?
  status       Int                   @default(1) // 0-禁用，1-启用
  rolePermissions SystemRolePermission[]
}

model SystemMenu {
  id           Int               @id @default(autoincrement())
  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @updatedAt
  name         String
  path         String            @unique
  component    String?
  redirect     String?
  pid          Int?              // 父菜单ID
  type         String            // menu-菜单，button-按钮
  icon         String?
  permission   String?           // 权限标识
  orderNo      Int               @default(0)
  status       Int               @default(1) // 0-禁用，1-启用
  meta         Json?             // 元数据
  roleMenus    SystemRoleMenu[]
  featureMenus FeatureMenu[]     // 关联功能菜单
  parent       SystemMenu?       @relation("MenuToMenu", fields: [pid], references: [id])
  children     SystemMenu[]      @relation("MenuToMenu")

  @@index([name])
  @@index([pid])
  @@index([type])
  @@index([status])
  @@index([orderNo])
}

// 关联表
model SystemUserRole {
  id           Int         @id @default(autoincrement())
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  userId       Int
  roleId       String
  user         SystemUser  @relation(fields: [userId], references: [id], onDelete: Cascade)
  role         SystemRole  @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
}

model SystemRoleMenu {
  id           Int         @id @default(autoincrement())
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  roleId       String
  menuId       Int
  role         SystemRole  @relation(fields: [roleId], references: [id], onDelete: Cascade)
  menu         SystemMenu  @relation(fields: [menuId], references: [id], onDelete: Cascade)

  @@unique([roleId, menuId])
}

model SystemRolePermission {
  id           Int               @id @default(autoincrement())
  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @updatedAt
  roleId       String
  permissionId Int
  role         SystemRole        @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission   SystemPermission  @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
}

// 系统用户备注关系表
model SystemUserRemark {
  id           Int         @id @default(autoincrement())
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  userId       Int         // 备注创建者ID
  targetUserId Int         // 被备注的用户ID
  remark       String      // 备注内容
  user         SystemUser  @relation("SystemUserRemarks", fields: [userId], references: [id], onDelete: Cascade)
  targetUser   SystemUser  @relation("SystemUserRemarkTargets", fields: [targetUserId], references: [id], onDelete: Cascade)

  @@unique([userId, targetUserId])
  @@index([userId])
  @@index([targetUserId])
}

// 系统用户扩展信息表
model SystemUserExtension {
  id           Int         @id @default(autoincrement())
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  userId       Int         @unique
  extendedInfo Json        // 扩展信息，可以存储各种额外属性
  user         SystemUser  @relation(fields: [userId], references: [id], onDelete: Cascade)
}

// 系统用户标签表
model SystemUserTag {
  id           Int         @id @default(autoincrement())
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  name         String      @unique // 标签名称
  color        String?     // 标签颜色
  userTags     SystemUserTagRelation[]
}

// 系统用户-标签关系表
model SystemUserTagRelation {
  id           Int         @id @default(autoincrement())
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  userId       Int
  tagId        Int
  user         SystemUser  @relation(fields: [userId], references: [id], onDelete: Cascade)
  tag          SystemUserTag @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@unique([userId, tagId])
  @@index([userId])
  @@index([tagId])
}

// 系统部门模型
model SystemDepartment {
  id           Int                @id @default(autoincrement())
  createdAt    DateTime           @default(now())
  updatedAt    DateTime           @updatedAt
  name         String             // 部门名称
  pid          Int?                // 父部门ID，null表示顶级部门
  status       Int                @default(1) // 状态：0-禁用，1-启用
  orderNo      Int                @default(0) // 排序号
  remark       String?            // 备注
  parent       SystemDepartment?  @relation("SystemDepartmentToSystemDepartment", fields: [pid], references: [id])
  children     SystemDepartment[] @relation("SystemDepartmentToSystemDepartment")

  @@unique([name, pid])
  @@index([pid])
  @@index([status])
  @@index([orderNo])
}

// 租户功能权限表
model TenantFeature {
  id           Int       @id @default(autoincrement())
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  tenantId     Int       // 租户ID
  featureCode  String    // 功能代码，如 "ai.ppt", "payment"
  enabled      Boolean   @default(false) // 是否启用
  expiresAt    DateTime? // 过期时间
  quota        Int?      // 使用配额（null表示无限制）
  usedQuota    Int       @default(0) // 已使用配额
  config       Json?     // 功能配置（非敏感信息）

  tenant       Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  feature      FeatureCode? @relation(fields: [featureCode], references: [code])

  @@unique([tenantId, featureCode])
  @@index([featureCode])
  @@index([enabled])
  @@index([expiresAt])
}

// 租户功能使用记录表
model TenantFeatureUsage {
  id           Int       @id @default(autoincrement())
  createdAt    DateTime  @default(now())
  tenantId     Int       // 租户ID
  featureCode  String    // 功能代码
  userId       Int?      // 使用用户ID
  metadata     Json?     // 使用相关数据

  tenant       Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  feature      FeatureCode? @relation(fields: [featureCode], references: [code])

  @@index([tenantId, featureCode])
  @@index([createdAt])
  @@index([userId])
}

// 功能配置模板表
model FeatureTemplate {
  id           Int       @id @default(autoincrement())
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  code         String    @unique // 模板代码，如 "basic", "pro"
  name         String    // 模板名称
  features     Json      // 包含的功能配置
  isActive     Boolean   @default(true)
}

// 租户配置表
model TenantConfig {
  id           Int       @id @default(autoincrement())
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  tenantId     Int
  category     String    // 配置类别，如 "email", "sms", "oss", "payment"
  key          String    // 配置键
  value        String    // 配置值（敏感信息加密存储）
  encrypted    Boolean   @default(false)

  tenant       Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId, category, key])
  @@index([tenantId, category])
}

// 功能代码表
model FeatureCode {
  id           Int       @id @default(autoincrement())
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  code         String    @unique // 功能代码，如 "ai.ppt", "payment"
  name         String    // 功能名称
  description  String    // 功能描述
  module       String    // 所属模块
  isActive     Boolean   @default(true) // 是否激活
  sortOrder    Int       @default(0) // 排序顺序
  metadata     Json?     // 额外元数据

  // 关联租户功能
  tenantFeatures TenantFeature[]
  usageRecords   TenantFeatureUsage[]
  featureMenus   FeatureMenu[]

  @@index([module])
  @@index([isActive])
}

// 功能菜单关联表
model FeatureMenu {
  id           Int       @id @default(autoincrement())
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  featureCode  String    // 功能代码
  menuId       Int       // 系统菜单ID

  feature      FeatureCode @relation(fields: [featureCode], references: [code], onDelete: Cascade)
  menu         SystemMenu  @relation(fields: [menuId], references: [id], onDelete: Cascade)

  @@unique([featureCode, menuId])
  @@index([featureCode])
  @@index([menuId])
}

// 订阅计划模型 - 系统级别的订阅计划
model SubscriptionPlan {
  id              Int                  @id @default(autoincrement())
  createdAt       DateTime             @default(now()) @map("create_time")
  updatedAt       DateTime             @updatedAt @map("update_time")
  code            String               // 计划代码，唯一标识
  name            String               // 计划名称
  description     String?              // 计划描述
  price           Decimal              @db.Decimal(10, 2) // 价格
  originalPrice   Decimal?             @db.Decimal(10, 2) @map("original_price") // 原价
  billingCycle    String               @map("billing_cycle") // 计费周期: monthly, quarterly, yearly
  features        Json                 // 功能配置
  isActive        Boolean              @default(true) @map("is_active") // 是否激活
  status          String               @default("active") // 状态: active, inactive, archived
  sortOrder       Int                  @default(0) @map("sort_order") // 排序顺序
  metadata        Json?                // 元数据，可存储附加信息
  subscriptions   TenantSubscription[] // 关联的租户订阅

  @@unique([code])
  @@index([status])
  @@index([isActive])
  @@index([sortOrder])
  @@map("subscription_plans")
}

// 租户订阅模型 - 记录租户的订阅情况
model TenantSubscription {
  id              Int               @id @default(autoincrement())
  createdAt       DateTime          @default(now()) @map("create_time")
  updatedAt       DateTime          @updatedAt @map("update_time")
  tenantId        Int               @map("tenant_id") // 租户ID（数字）
  planId          Int               @map("plan_id") // 订阅计划ID
  duration        Int               // 订阅时长（月数）
  billingCycle    String            @map("billing_cycle") // 计费周期: monthly, quarterly, yearly
  startDate       DateTime          @map("start_date") // 开始日期
  endDate         DateTime          @map("end_date") // 结束日期
  status          String            @default("active") // 状态: active, expired, suspended, cancelled
  autoRenew       Boolean           @default(false) @map("auto_renew") // 是否自动续费
  metadata        Json?             // 元数据，可存储附加信息
  plan            SubscriptionPlan  @relation(fields: [planId], references: [id])

  @@index([tenantId])
  @@index([planId])
  @@index([status])
  @@index([startDate])
  @@index([endDate])
  @@map("tenant_subscriptions")
}