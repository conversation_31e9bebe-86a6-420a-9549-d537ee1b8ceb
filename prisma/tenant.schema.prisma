generator client {
  provider = "prisma-client-js"
  output   = "./generated/tenant"
}

datasource db {
  provider = "postgresql"
  url      = env("TENANT_DATABASE_URL")
}

// 租户用户
model User {
  id             Int             @id @default(autoincrement())
  username       String          // 用户名
  password       String?         // 密码（加密存储）
  email          String?         // 邮箱
  phoneNumber    String?         // 手机号
  realName       String?         // 真实姓名
  nickname       String?         // 昵称
  avatar         String?         // 头像
  gender         Int?            // 性别：0-未知，1-男，2-女
  status         Int             @default(1) // 状态：0-禁用，1-启用
  userType       String          @default("NORMAL") // 用户类型：ADMIN-管理员，NORMAL-普通用户
  lastLoginAt    DateTime?       // 上次登录时间
  tenantId       Int             // 租户ID
  departmentId   Int?            // 部门ID
  metadata       Json?           // 元数据，存储额外信息
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  roles          UserRole[]      // 用户角色关联
  departments    UserDepartment[] // 用户部门关联

  // 会员关系
  memberships    UserMembership[] // 用户的会员记录
  wallets        UserWallet[]     // 用户的虚拟币钱包

  @@unique([username, tenantId])
  @@index([status])
  @@index([tenantId])
  @@index([departmentId])
  @@index([userType])
  @@index([email])
  @@index([phoneNumber])
}

// 其他租户模型...

// 用户-角色关联表
model UserRole {
  id        Int      @id @default(autoincrement())
  userId    Int
  roleId    String
  tenantId  Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
  @@index([tenantId])
}

// 用户-部门关联表
model UserDepartment {
  id           Int      @id @default(autoincrement())
  userId       Int
  departmentId Int
  tenantId     Int
  isMain       Boolean  @default(false) // 是否主部门
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, departmentId])
  @@index([tenantId])
  @@index([isMain])
}

// 会员计划表 - 租户创建的会员计划
model MembershipPlan {
  id              Int              @id @default(autoincrement())
  code            String           // 计划代码
  name            String           // 计划名称
  description     String?          // 计划描述
  price           Decimal          @db.Decimal(10, 2) // 价格
  originalPrice   Decimal?         @db.Decimal(10, 2) // 原价
  billingCycle    String           // 计费周期: monthly, quarterly, yearly
  features        Json             // 功能配置
  isActive        Boolean          @default(true) // 是否激活
  status          String           @default("active") // 状态: active, inactive
  sortOrder       Int              @default(0) // 排序顺序
  metadata        Json?            // 元数据
  tenantId        Int              // 租户ID
  createTime      DateTime         @default(now())
  updateTime      DateTime         @updatedAt

  // 关系
  memberships     UserMembership[] // 用户会员记录
  benefits        PlanBenefit[]    // 会员权益

  @@unique([code, tenantId])
  @@index([tenantId])
  @@index([status])
  @@index([isActive])
  @@index([sortOrder])
}

// 用户会员表 - 记录用户的会员状态
model UserMembership {
  id              Int             @id @default(autoincrement())
  userId          Int             // 用户ID
  planId          Int             // 会员计划ID
  startDate       DateTime        // 开始日期
  endDate         DateTime        // 结束日期
  status          String          @default("active") // 状态: active, expired, cancelled
  autoRenew       Boolean         @default(false) // 是否自动续费
  metadata        Json?           // 元数据
  tenantId        Int             // 租户ID
  createTime      DateTime        @default(now())
  updateTime      DateTime        @updatedAt

  // 关系
  user            User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  plan            MembershipPlan  @relation(fields: [planId], references: [id])

  @@index([userId])
  @@index([planId])
  @@index([tenantId])
  @@index([status])
  @@index([startDate])
  @@index([endDate])
}

// 会员权益表 - 定义会员权益项目
model MembershipBenefit {
  id              Int             @id @default(autoincrement())
  code            String          // 权益代码
  name            String          // 权益名称
  description     String?         // 权益描述
  category        String          // 权益分类
  icon            String?         // 权益图标
  details         String?         // 权益详情
  isActive        Boolean         @default(true) // 是否激活
  sortOrder       Int             @default(0) // 排序顺序
  metadata        Json?           // 元数据
  tenantId        int             // 租户ID
  createTime      DateTime        @default(now())
  updateTime      DateTime        @updatedAt

  // 关系
  planBenefits    PlanBenefit[]   // 计划权益关联

  @@unique([code, tenantId])
  @@index([tenantId])
  @@index([category])
  @@index([isActive])
  @@index([sortOrder])
}

// 计划权益关联表 - 会员计划和权益的关联
model PlanBenefit {
  id              Int               @id @default(autoincrement())
  planId          Int               // 会员计划ID
  benefitId       Int               // 权益ID
  quota           Int?              // 配额限制
  config          Json?             // 配置信息
  tenantId        Int               // 租户ID
  createTime      DateTime          @default(now())
  updateTime      DateTime          @updatedAt

  // 关系
  plan            MembershipPlan    @relation(fields: [planId], references: [id], onDelete: Cascade)
  benefit         MembershipBenefit @relation(fields: [benefitId], references: [id], onDelete: Cascade)

  @@unique([planId, benefitId])
  @@index([tenantId])
}

// 虚拟币类型表
model VirtualCurrencyType {
  id              Int               @id @default(autoincrement())
  code            String            // 虚拟币代码
  name            String            // 虚拟币名称
  description     String?           // 虚拟币描述
  icon            String?           // 虚拟币图标
  exchangeRate    Decimal           @db.Decimal(10, 2) // 兑换比率
  isActive        Boolean           @default(true) // 是否激活
  metadata        Json?             // 元数据
  tenantId        Int               // 租户ID
  createTime      DateTime          @default(now())
  updateTime      DateTime          @updatedAt

  // 关系
  wallets         UserWallet[]      // 用户钱包
  transactions    CurrencyTransaction[] // 交易记录

  @@unique([code, tenantId])
  @@index([tenantId])
  @@index([isActive])
}

// 用户虚拟币钱包
model UserWallet {
  id              Int                 @id @default(autoincrement())
  userId          Int                 // 用户ID
  currencyTypeId  Int                 // 虚拟币类型ID
  balance         Decimal             @db.Decimal(10, 2) // 余额
  tenantId        Int                 // 租户ID
  createTime      DateTime            @default(now())
  updateTime      DateTime            @updatedAt

  // 关系
  user            User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  currencyType    VirtualCurrencyType @relation(fields: [currencyTypeId], references: [id])
  transactions    CurrencyTransaction[] // 交易记录

  @@unique([userId, currencyTypeId])
  @@index([tenantId])
}

// 虚拟币交易记录
model CurrencyTransaction {
  id              Int                 @id @default(autoincrement())
  walletId        Int                 // 钱包ID
  currencyTypeId  Int                 // 虚拟币类型ID
  amount          Decimal             @db.Decimal(10, 2) // 交易金额
  balance         Decimal             @db.Decimal(10, 2) // 交易后余额
  type            String              // 交易类型: purchase, consume, gift, refund
  description     String?             // 交易描述
  metadata        Json?               // 元数据
  tenantId        Int                 // 租户ID
  createTime      DateTime            @default(now())

  // 关系
  wallet          UserWallet          @relation(fields: [walletId], references: [id], onDelete: Cascade)
  currencyType    VirtualCurrencyType @relation(fields: [currencyTypeId], references: [id])

  @@index([walletId])
  @@index([currencyTypeId])
  @@index([tenantId])
  @@index([type])
  @@index([createTime])
}
