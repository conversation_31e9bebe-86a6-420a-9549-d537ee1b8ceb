generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 用户相关模型
model User {
  id           Int       @id @default(autoincrement())
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  username     String    // 用户名
  password     String    // 密码（加密存储）
  emailAddress String?   // 邮箱
  firstName    String?   // 名
  lastName     String?   // 姓
  realName     String?   // 真实姓名/显示名
  avatar       String?   // 头像URL
  status       Int       @default(1) // 0-禁用，1-启用
  lastLoginAt  DateTime? // 最后登录时间
  homePath     String?   // 用户首页路径
  metadata     Json?     // 元数据
  tenantId     Int       // 租户ID（数字）

  // 新增基本预留字段
  phoneNumber  String?   // 手机号
  idCardNumber String?   // 身份证号
  openid       String?   // 微信openid
  unionid      String?   // 微信unionid
  adminRemark  String?   // 管理员备注

  // 关联字段
  userRoles    UserRole[]
  remarks      UserRemark[] @relation("UserRemarks")
  remarkedBy   UserRemark[] @relation("UserRemarkTargets")
  extension    UserExtension?
  userTags     UserTagRelation[]

  // 会员关系
  memberships    UserMembership[] // 用户的会员记录
  wallets        UserWallet[]     // 用户的虚拟币钱包

  @@unique([username, tenantId])
  @@index([tenantId])
  @@index([username])
  @@index([status])
  @@index([emailAddress])
  @@index([phoneNumber])
  @@index([idCardNumber])
  @@index([openid])
}

// 角色相关模型
model Role {
  id           String            @id @default(uuid())
  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @updatedAt
  name         String            // 角色名称
  code         String            // 角色编码
  remark       String?           // 角色描述
  status       Int               @default(1) // 0-禁用，1-启用
  tenantId     Int               // 租户ID（数字）
  userRoles    UserRole[]
  roleMenus    RoleMenu[]
  rolePermissions RolePermission[]

  @@index([tenantId])
  @@index([name])
  @@index([code])
  @@index([status])
}

// 权限相关模型
model Permission {
  id           Int               @id @default(autoincrement())
  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @updatedAt
  name         String            // 权限名称
  code         String            // 权限码，如 system:user:create
  description  String?           // 权限描述
  status       Int               @default(1) // 0-禁用，1-启用
  tenantId     Int               // 租户ID（数字）
  rolePermissions RolePermission[]

  @@unique([code, tenantId])
  @@index([tenantId])
}

// 菜单相关模型
model Menu {
  id           Int               @id @default(autoincrement())
  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @updatedAt
  name         String            // 菜单名称
  path         String            // 路由路径
  component    String?           // 组件路径
  redirect     String?           // 重定向路径
  pid          Int?              // 父菜单ID
  type         String            // menu-菜单，button-按钮
  icon         String?           // 图标
  permission   String?           // 权限标识
  orderNo      Int               @default(0) // 排序号
  status       Int               @default(1) // 0-禁用，1-启用
  meta         Json?             // 元数据
  tenantId     Int               // 租户ID（数字）
  sourceMenuId Int?              // 源系统菜单ID
  featureCode  String?           // 关联的功能代码
  roleMenus    RoleMenu[]
  parent       Menu?             @relation("MenuToMenu", fields: [pid], references: [id])
  children     Menu[]            @relation("MenuToMenu")

  @@unique([path, tenantId])
  @@index([tenantId])
  @@index([name])
  @@index([pid])
  @@index([type])
  @@index([status])
  @@index([orderNo])
  @@index([sourceMenuId])
  @@index([featureCode])
}

// 关联表
model UserRole {
  id           Int       @id @default(autoincrement())
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  userId       Int
  roleId       String
  tenantId     Int       // 租户ID（数字）
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  role         Role      @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
  @@index([tenantId])
}

model RoleMenu {
  id           Int       @id @default(autoincrement())
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  roleId       String
  menuId       Int
  tenantId     Int       // 租户ID（数字）
  role         Role      @relation(fields: [roleId], references: [id], onDelete: Cascade)
  menu         Menu      @relation(fields: [menuId], references: [id], onDelete: Cascade)

  @@unique([roleId, menuId])
  @@index([tenantId])
}

model RolePermission {
  id           Int         @id @default(autoincrement())
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  roleId       String
  permissionId Int
  tenantId     Int         // 租户ID（数字）
  role         Role        @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission   Permission  @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
  @@index([tenantId])
}

// 用户备注关系表
model UserRemark {
  id           Int       @id @default(autoincrement())
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  userId       Int       // 备注创建者ID
  targetUserId Int       // 被备注的用户ID
  remark       String    // 备注内容
  tenantId     Int       // 租户ID（数字）
  user         User      @relation("UserRemarks", fields: [userId], references: [id], onDelete: Cascade)
  targetUser   User      @relation("UserRemarkTargets", fields: [targetUserId], references: [id], onDelete: Cascade)

  @@unique([userId, targetUserId, tenantId])
  @@index([tenantId])
  @@index([userId])
  @@index([targetUserId])
}

// 用户扩展信息表
model UserExtension {
  id           Int       @id @default(autoincrement())
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  userId       Int       @unique
  tenantId     Int       // 租户ID（数字）
  extendedInfo Json      // 扩展信息，可以存储各种额外属性
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([tenantId])
}

// 用户标签表
model UserTag {
  id           Int       @id @default(autoincrement())
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  name         String    // 标签名称
  color        String?   // 标签颜色
  tenantId     Int       // 租户ID（数字）
  userTags     UserTagRelation[]

  @@unique([name, tenantId])
  @@index([tenantId])
}

// 用户-标签关系表
model UserTagRelation {
  id           Int       @id @default(autoincrement())
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  userId       Int
  tagId        Int
  tenantId     Int       // 租户ID（数字）
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  tag          UserTag   @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@unique([userId, tagId])
  @@index([tenantId])
  @@index([userId])
  @@index([tagId])
}

// 租户部门模型
model Department {
  id           Int          @id @default(autoincrement())
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt
  name         String       // 部门名称
  pid          Int?         // 父部门ID，null表示顶级部门
  status       Int          @default(1) // 状态：0-禁用，1-启用
  orderNo      Int          @default(0) // 排序号
  remark       String?      // 备注
  tenantId     Int          // 租户ID（数字）
  parent       Department?  @relation("DepartmentToDepartment", fields: [pid], references: [id])
  children     Department[] @relation("DepartmentToDepartment")

  @@unique([name, pid, tenantId])
  @@index([tenantId])
  @@index([pid])
  @@index([status])
  @@index([orderNo])
}

// 会员计划表 - 租户创建的会员计划
model MembershipPlan {
  id              Int              @id @default(autoincrement())
  code            String           // 计划代码
  name            String           // 计划名称
  description     String?          // 计划描述
  price           Decimal          @db.Decimal(10, 2) // 价格
  originalPrice   Decimal?         @db.Decimal(10, 2) // 原价
  billingCycle    String           // 计费周期: monthly, quarterly, yearly
  features        Json             // 功能配置
  isActive        Boolean          @default(true) // 是否激活
  status          String           @default("active") // 状态: active, inactive
  sortOrder       Int              @default(0) // 排序顺序
  metadata        Json?            // 元数据
  tenantId        Int              // 租户ID
  createTime      DateTime         @default(now())
  updateTime      DateTime         @updatedAt

  // 关系
  memberships     UserMembership[] // 用户会员记录
  benefits        PlanBenefit[]    // 会员权益

  @@unique([code, tenantId])
  @@index([tenantId])
  @@index([status])
  @@index([isActive])
  @@index([sortOrder])
}

// 用户会员表 - 记录用户的会员状态
model UserMembership {
  id              Int             @id @default(autoincrement())
  userId          Int             // 用户ID
  name            Int             // 用户名称
  planId          Int             // 会员计划ID
  startDate       DateTime        // 开始日期
  endDate         DateTime        // 结束日期
  status          String          @default("active") // 状态: active, expired, cancelled
  autoRenew       Boolean         @default(false) // 是否自动续费
  metadata        Json?           // 元数据
  tenantId        Int             // 租户ID
  createTime      DateTime        @default(now())
  updateTime      DateTime        @updatedAt

  // 关系
  user            User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  plan            MembershipPlan  @relation(fields: [planId], references: [id])

  @@index([userId])
  @@index([planId])
  @@index([tenantId])
  @@index([status])
  @@index([startDate])
  @@index([endDate])
}

// 会员权益表 - 定义会员权益项目
model MembershipBenefit {
  id              Int             @id @default(autoincrement())
  code            String          // 权益代码
  name            String          // 权益名称
  description     String?         // 权益描述
  category        String          // 权益分类
  icon            String?         // 权益图标
  details         String?         // 权益详情
  isActive        Boolean         @default(true) // 是否激活
  sortOrder       Int             @default(0) // 排序顺序
  metadata        Json?           // 元数据
  tenantId        Int             // 租户ID
  createTime      DateTime        @default(now())
  updateTime      DateTime        @updatedAt

  // 关系
  planBenefits    PlanBenefit[]   // 计划权益关联

  @@unique([code, tenantId])
  @@index([tenantId])
  @@index([category])
  @@index([isActive])
  @@index([sortOrder])
}

// 计划权益关联表 - 会员计划和权益的关联
model PlanBenefit {
  id              Int               @id @default(autoincrement())
  planId          Int               // 会员计划ID
  benefitId       Int               // 权益ID
  quota           Int?              // 配额限制
  config          Json?             // 配置信息
  tenantId        Int               // 租户ID
  createTime      DateTime          @default(now())
  updateTime      DateTime          @updatedAt

  // 关系
  plan            MembershipPlan    @relation(fields: [planId], references: [id], onDelete: Cascade)
  benefit         MembershipBenefit @relation(fields: [benefitId], references: [id], onDelete: Cascade)

  @@unique([planId, benefitId])
  @@index([tenantId])
}

// 虚拟币类型表
model VirtualCurrencyType {
  id              Int               @id @default(autoincrement())
  code            String            // 虚拟币代码
  name            String            // 虚拟币名称
  description     String?           // 虚拟币描述
  icon            String?           // 虚拟币图标
  exchangeRate    Decimal           @db.Decimal(10, 2) // 兑换比率
  isActive        Boolean           @default(true) // 是否激活
  metadata        Json?             // 元数据
  tenantId        Int               // 租户ID
  createTime      DateTime          @default(now())
  updateTime      DateTime          @updatedAt

  // 关系
  wallets         UserWallet[]      // 用户钱包
  transactions    CurrencyTransaction[] // 交易记录

  @@unique([code, tenantId])
  @@index([tenantId])
  @@index([isActive])
}

// 用户虚拟币钱包
model UserWallet {
  id              Int                 @id @default(autoincrement())
  userId          Int                 // 用户ID
  currencyTypeId  Int                 // 虚拟币类型ID
  balance         Decimal             @db.Decimal(10, 2) // 余额
  tenantId        Int                 // 租户ID
  createTime      DateTime            @default(now())
  updateTime      DateTime            @updatedAt

  // 关系
  user            User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  currencyType    VirtualCurrencyType @relation(fields: [currencyTypeId], references: [id])
  transactions    CurrencyTransaction[] // 交易记录

  @@unique([userId, currencyTypeId])
  @@index([tenantId])
}

// 虚拟币交易记录
model CurrencyTransaction {
  id              Int                 @id @default(autoincrement())
  walletId        Int                 // 钱包ID
  currencyTypeId  Int                 // 虚拟币类型ID
  amount          Decimal             @db.Decimal(10, 2) // 交易金额
  balance         Decimal             @db.Decimal(10, 2) // 交易后余额
  type            String              // 交易类型: purchase, consume, gift, refund
  description     String?             // 交易描述
  metadata        Json?               // 元数据
  tenantId        Int                 // 租户ID
  createTime      DateTime            @default(now())

  // 关系
  wallet          UserWallet          @relation(fields: [walletId], references: [id], onDelete: Cascade)
  currencyType    VirtualCurrencyType @relation(fields: [currencyTypeId], references: [id])

  @@index([walletId])
  @@index([currencyTypeId])
  @@index([tenantId])
  @@index([type])
  @@index([createTime])
}