// 支付模块
model Payment {
  id           Int       @id @default(autoincrement())
  orderNo      String    @unique // 订单号
  userId       Int       // 用户ID
  amount       Float     // 支付金额
  status       String    // pending, success, failed, refunded
  paymentMethod String    // alipay, wechat, creditcard
  businessType String    // 业务类型，如membership, virtual-currency, feature-purchase
  businessId   String    // 业务ID
  subject      String    // 支付主题
  description  String?   // 支付描述
  metadata     Json?     // 元数据
  callbackData Json?     // 回调数据
  createTime   DateTime  @default(now())
  updateTime   DateTime  @updatedAt
  paymentTime  DateTime? // 支付时间

  // 关联
  user         User      @relation(fields: [userId], references: [id])
  refunds      Refund[]

  @@map("payments")
}

// 退款模块
model Refund {
  id           Int       @id @default(autoincrement())
  refundNo     String    @unique // 退款单号
  paymentId    Int       // 支付订单ID
  userId       Int       // 用户ID
  amount       Float     // 退款金额
  reason       String    // 退款原因
  status       String    // processing, approved, rejected
  comment      String?   // 处理备注
  createTime   DateTime  @default(now())
  updateTime   DateTime  @updatedAt
  processTime  DateTime? // 处理时间

  // 关联
  payment      Payment   @relation(fields: [paymentId], references: [id])
  user         User      @relation(fields: [userId], references: [id])

  @@map("refunds")
}
