import { PrismaClient } from '@prisma/client';
import { seedFeatureTemplates } from './seeds/feature-templates';

const prisma = new PrismaClient();

async function main() {
  console.log('开始执行种子脚本...');
  
  // 创建功能模板
  await seedFeatureTemplates(prisma);
  
  console.log('种子脚本执行完成');
}

main()
  .catch((e) => {
    console.error('种子脚本执行失败:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
