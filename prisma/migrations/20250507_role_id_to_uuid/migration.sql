-- 修改系统角色表的 ID 字段类型
ALTER TABLE "public"."SystemRole" ALTER COLUMN "id" DROP DEFAULT;
ALTER TABLE "public"."SystemRole" ALTER COLUMN "id" TYPE TEXT USING id::TEXT;
ALTER TABLE "public"."SystemRole" ALTER COLUMN "id" SET DEFAULT gen_random_uuid();

-- 修改系统用户角色关联表的 roleId 字段类型
ALTER TABLE "public"."SystemUserRole" ALTER COLUMN "roleId" TYPE TEXT USING "roleId"::TEXT;

-- 修改系统角色菜单关联表的 roleId 字段类型
ALTER TABLE "public"."SystemRoleMenu" ALTER COLUMN "roleId" TYPE TEXT USING "roleId"::TEXT;

-- 修改系统角色权限关联表的 roleId 字段类型
ALTER TABLE "public"."SystemRolePermission" ALTER COLUMN "roleId" TYPE TEXT USING "roleId"::TEXT;

-- 修改租户角色表的 ID 字段类型
ALTER TABLE "tenant"."Role" ALTER COLUMN "id" DROP DEFAULT;
ALTER TABLE "tenant"."Role" ALTER COLUMN "id" TYPE TEXT USING id::TEXT;
ALTER TABLE "tenant"."Role" ALTER COLUMN "id" SET DEFAULT gen_random_uuid();

-- 修改租户用户角色关联表的 roleId 字段类型
ALTER TABLE "tenant"."UserRole" ALTER COLUMN "roleId" TYPE TEXT USING "roleId"::TEXT;

-- 修改租户角色菜单关联表的 roleId 字段类型
ALTER TABLE "tenant"."RoleMenu" ALTER COLUMN "roleId" TYPE TEXT USING "roleId"::TEXT;

-- 修改租户角色权限关联表的 roleId 字段类型
ALTER TABLE "tenant"."RolePermission" ALTER COLUMN "roleId" TYPE TEXT USING "roleId"::TEXT;
