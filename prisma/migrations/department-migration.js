/**
 * 部门模块数据库迁移脚本
 * 
 * 本脚本用于将系统部门模型从租户数据库迁移到系统数据库
 * 
 * 使用方法：
 * 1. 首先运行 prisma migrate dev 生成迁移文件
 * 2. 然后运行 node prisma/migrations/department-migration.js 执行数据迁移
 */

const { PrismaClient: PublicPrismaClient } = require('@prisma-public/prisma/client');
const { PrismaClient } = require('@prisma/client');

async function main() {
  console.log('开始部门数据迁移...');

  // 创建Prisma客户端
  const publicPrisma = new PublicPrismaClient();
  const tenantPrisma = new PrismaClient();

  try {
    // 连接数据库
    await publicPrisma.$connect();
    await tenantPrisma.$connect();
    console.log('数据库连接成功');

    // 检查租户数据库中是否存在SystemDepartment表
    let hasTenantSystemDepartments = false;
    try {
      const count = await tenantPrisma.$queryRaw`SELECT COUNT(*) FROM "SystemDepartment"`;
      hasTenantSystemDepartments = count[0].count > 0;
    } catch (error) {
      console.log('租户数据库中不存在SystemDepartment表，无需迁移数据');
    }

    if (hasTenantSystemDepartments) {
      // 从租户数据库中获取所有系统部门
      const systemDepartments = await tenantPrisma.systemDepartment.findMany();
      console.log(`找到 ${systemDepartments.length} 个系统部门需要迁移`);

      // 将系统部门迁移到系统数据库
      for (const dept of systemDepartments) {
        await publicPrisma.systemDepartment.create({
          data: {
            id: dept.id,
            name: dept.name,
            pid: dept.pid,
            status: dept.status,
            orderNo: dept.orderNo,
            remark: dept.remark,
            createdAt: dept.createdAt,
            updatedAt: dept.updatedAt,
          },
        });
      }
      console.log('系统部门数据迁移完成');

      // 删除租户数据库中的SystemDepartment表
      await tenantPrisma.$executeRaw`DROP TABLE IF EXISTS "SystemDepartment" CASCADE`;
      console.log('租户数据库中的SystemDepartment表已删除');
    }

    console.log('部门数据迁移成功完成');
  } catch (error) {
    console.error('部门数据迁移失败:', error);
  } finally {
    // 断开数据库连接
    await publicPrisma.$disconnect();
    await tenantPrisma.$disconnect();
  }
}

main()
  .then(() => {
    console.log('迁移脚本执行完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('迁移脚本执行失败:', error);
    process.exit(1);
  });
