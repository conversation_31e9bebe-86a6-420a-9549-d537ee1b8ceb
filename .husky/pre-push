#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# Get current branch name
BR<PERSON>CH_NAME=$(git symbolic-ref --short HEAD)

# Branch naming convention
PATTERN="^(feature|bugfix|hotfix|release|chore)\/[a-z0-9-]+$"

# Check if branch name follows the pattern
if ! [[ $BRANCH_NAME =~ $PATTERN ]]; then
  echo "ERROR: Branch name does not follow the convention: $PATTERN"
  echo "Current branch name: $BRANCH_NAME"
  echo "Allowed prefixes: feature/, bugfix/, hotfix/, release/, chore/"
  exit 1
fi

# Run tests before pushing
npm test
