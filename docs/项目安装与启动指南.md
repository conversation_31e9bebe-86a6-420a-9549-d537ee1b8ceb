# 项目安装与启动指南

本文档提供了项目的安装、初始化和启动步骤。

## 环境要求

- Node.js v22+
- PostgreSQL 数据库
- pnpm 包管理器

## 安装步骤

### 1. 安装 pnpm（如果尚未安装）

```bash
npm install -g pnpm
```

### 2. 安装项目依赖

在项目根目录下运行：

```bash
pnpm install
```

这个命令会安装所有必要的依赖，并自动生成 Prisma 客户端。

### 3. 配置环境变量

复制 `.env.example` 文件为 `.env`，并根据您的环境修改配置：

```bash
cp .env.example .env
```

然后编辑 `.env` 文件，修改数据库连接信息等配置。

### 4. 初始化数据库

#### 4.1 创建数据库表结构

```bash
# 创建公共数据库表结构
pnpm db:push:public

# 创建租户数据库表结构
pnpm db:push:tenant
```

#### 4.2 添加初始数据

```bash
# 使用 Prisma seed 添加初始数据
npx prisma db seed
```

### 5. 启动项目

#### 开发环境

```bash
pnpm dev
```

#### 生产环境

```bash
pnpm build
pnpm start:prod
```

## 初始账号信息

初始化完成后，您可以使用以下账号登录系统：

### 系统管理员

- 用户名：admin
- 密码：admin123

### 租户管理员

- 用户名：tenant_admin
- 密码：admin123
- 租户代码：tenant1

## 常见问题

### 1. 依赖安装失败

如果依赖安装失败，可以尝试清除缓存后重新安装：

```bash
pnpm store prune
pnpm install
```

### 2. 数据库连接失败

检查 `.env` 文件中的数据库连接信息是否正确，确保数据库服务已启动。

### 3. Prisma 相关错误

如果遇到 Prisma 相关错误，可以尝试重新生成 Prisma 客户端：

```bash
pnpm db:generate:tenant
pnpm db:generate:public
```

## 项目结构

```
├── docs/                # 文档
├── prisma/              # Prisma 模型和种子脚本
│   ├── schema.prisma    # 租户数据库模型
│   ├── public-schema.prisma # 公共数据库模型
│   └── seed.js          # 数据库种子脚本
├── src/                 # 源代码
│   ├── filters/         # 全局过滤器
│   ├── interceptors/    # 全局拦截器
│   ├── middlewares/     # 中间件
│   ├── modules/         # 业务模块
│   └── types/           # 类型定义
├── .env                 # 环境变量
├── .env.example         # 环境变量示例
├── package.json         # 项目配置
└── README.md            # 项目说明
```
