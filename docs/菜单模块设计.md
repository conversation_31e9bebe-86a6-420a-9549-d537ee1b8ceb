# 菜单模块设计文档

## 概述

菜单模块负责管理系统菜单和租户菜单，提供菜单的增删改查功能，以及菜单树的构建。菜单模块采用策略模式，根据用户类型（系统用户或租户用户）选择不同的菜单服务实现。

## 目录结构

```
src/modules/routes/menu/
├── dto/
│   └── menu.dto.ts         # 菜单相关DTO定义
├── services/
│   ├── menu.service.ts     # 菜单服务（策略选择器）
│   ├── system-menu.service.ts  # 系统菜单服务实现
│   └── tenant-menu.service.ts  # 租户菜单服务实现
├── menu.controller.ts      # 菜单控制器
└── menu.module.ts          # 菜单模块定义
```

## 数据模型

### 菜单类型枚举

```typescript
export enum MenuType {
  MENU = 'menu',         // 菜单
  BUTTON = 'button',     // 按钮
  DIRECTORY = 'directory', // 目录
  CATALOG = 'catalog',   // 目录
  EMBEDDED = 'embedded', // 嵌入式
  LINK = 'link',         // 链接
}
```

### 菜单元数据

```typescript
export class MenuMetaDto {
  title: string;           // 菜单标题
  icon?: string;           // 菜单图标
  orderNo?: number;        // 排序号
  hideMenu?: boolean;      // 是否隐藏菜单
  ignoreAuth?: boolean;    // 是否忽略权限
  hideBreadcrumb?: boolean; // 是否隐藏面包屑
  hideChildrenInMenu?: boolean; // 是否隐藏子菜单
  currentActiveMenu?: string; // 当前激活的菜单
}
```

### 菜单DTO

```typescript
export class CreateMenuDto {
  name: string;           // 菜单名称
  path: string;           // 路由路径
  pid?: number;           // 父级ID
  redirect?: string;      // 重定向路径
  type: MenuType;         // 菜单类型
  icon?: string;          // 图标
  component?: string;     // 组件路径
  permission?: string;    // 权限标识
  orderNo: number;        // 排序号
  status: number;         // 状态：0-禁用，1-启用
  meta?: MenuMetaDto;     // 元数据
}
```

## API接口

### 获取菜单树（前端导航用）

- 路径: `GET /menu/all`
- 描述: 获取当前用户可访问的所有菜单，构建成树形结构
- 返回: `MenuTreeDto[]`

### 获取菜单列表（管理用）

- 路径: `GET /system/menu/list`
- 描述: 获取所有菜单列表，用于后台管理
- 返回: `MenuDto[]`

### 创建菜单

- 路径: `POST /system/menu`
- 描述: 创建新菜单
- 参数: `CreateMenuDto`
- 返回: `MenuDto`

### 更新菜单

- 路径: `PUT /system/menu/:id`
- 描述: 更新指定ID的菜单
- 参数: `UpdateMenuDto`
- 返回: `MenuDto`

### 删除菜单

- 路径: `DELETE /system/menu/:id`
- 描述: 删除指定ID的菜单
- 返回: `{ success: boolean }`

### 检查菜单名称是否存在

- 路径: `GET /system/menu/name-exists`
- 描述: 检查菜单名称是否已存在
- 参数: `name`, `id`(可选)
- 返回: `boolean`

### 检查菜单路径是否存在

- 路径: `GET /system/menu/path-exists`
- 描述: 检查菜单路径是否已存在
- 参数: `path`, `id`(可选)
- 返回: `boolean`

## 实现细节

### 策略模式

菜单模块采用策略模式，根据用户类型选择不同的菜单服务实现：

- 系统用户: 使用 `SystemMenuService`
- 租户用户: 使用 `TenantMenuService`

`MenuService` 作为策略选择器，根据请求中的用户信息选择合适的服务实现。

### Meta字段处理

菜单的 `meta` 字段在数据库中存储为 JSON 字符串，在返回给前端时需要解析为 JavaScript 对象。为此，我们实现了以下辅助方法：

- `parseMetaField`: 解析 meta 字段，将 JSON 字符串转换为对象
- `transformMenuData`: 转换菜单数据，确保 meta 字段是对象

### 菜单树构建

菜单树的构建采用递归方式，根据菜单的父子关系构建树形结构：

- `buildMenuTree`: 构建菜单树，用于后台管理
- `buildFrontendMenuTree`: 构建前端菜单树，用于前端导航

## 注意事项

1. 删除菜单时，需要先检查是否有子菜单，如果有则不允许删除
2. 更新菜单时，需要检查路径是否已被其他菜单使用
3. 菜单的 `meta` 字段在数据库中存储为 JSON 字符串，需要正确处理
4. 菜单类型必须是 `MenuType` 枚举中定义的值之一
