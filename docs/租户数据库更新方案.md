# 租户数据库表结构更新方案

在多租户系统中，当每个租户使用独立的数据库或 schema 时，更新租户表结构是一个重要的运维挑战。本文档提供了几种可能的解决方案。

## 1. 批量更新脚本

### 实现方式

创建一个脚本，从公共数据库中获取所有租户的数据源信息，然后对每个数据源执行 Prisma 迁移：

```typescript
// scripts/update-tenant-schemas.ts
import { PrismaClient as PublicPrismaClient } from '@prisma-public/prisma/client';
import { execSync } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';

async function updateTenantSchemas() {
  // 连接到公共数据库
  const publicPrisma = new PublicPrismaClient();
  
  try {
    // 获取所有租户及其数据源
    const tenants = await publicPrisma.tenant.findMany({
      include: { datasource: true },
    });
    
    console.log(`Found ${tenants.length} tenants to update.`);
    
    // 遍历每个租户
    for (const tenant of tenants) {
      if (!tenant.datasource) {
        console.warn(`Tenant ${tenant.code} has no datasource. Skipping.`);
        continue;
      }
      
      console.log(`Updating schema for tenant: ${tenant.code}`);
      
      // 创建临时的 .env 文件，包含租户特定的数据库 URL
      const envContent = `DATABASE_URL="${tenant.datasource.url}"`;
      const tempEnvPath = path.join(process.cwd(), '.env.temp');
      fs.writeFileSync(tempEnvPath, envContent);
      
      try {
        // 使用临时 .env 文件执行 Prisma 迁移
        execSync(`dotenv -e .env.temp -- npx prisma db push --schema=./prisma/schema.prisma`, {
          stdio: 'inherit',
        });
        console.log(`✅ Successfully updated schema for tenant: ${tenant.code}`);
      } catch (error) {
        console.error(`❌ Failed to update schema for tenant: ${tenant.code}`, error);
      } finally {
        // 清理临时文件
        fs.unlinkSync(tempEnvPath);
      }
    }
  } finally {
    await publicPrisma.$disconnect();
  }
}

updateTenantSchemas().catch(console.error);
```

### 使用方法

1. 将脚本保存到 `scripts/update-tenant-schemas.ts`
2. 安装所需依赖：`pnpm add -D dotenv-cli`
3. 在 `package.json` 中添加命令：

```json
{
  "scripts": {
    "update:tenant-schemas": "ts-node scripts/update-tenant-schemas.ts"
  }
}
```

4. 运行命令更新所有租户的表结构：`pnpm update:tenant-schemas`

### 优点

- 可以一次性更新所有租户的数据库结构
- 自动化程度高，减少手动操作
- 可以记录每个租户的更新结果

### 缺点

- 对于大量租户，可能需要较长时间
- 需要有权限访问所有租户的数据库
- 如果某个租户更新失败，需要手动处理

## 2. 租户数据库迁移服务

### 实现方式

创建一个专门的服务，负责管理租户数据库的迁移：

```typescript
// src/modules/database-migration/database-migration.service.ts
import { Injectable, Logger } from '@nestjs/common';
import { PublicPrismaService } from '@/modules/prisma/public-prisma.service';
import { exec } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';

const execAsync = promisify(exec);

@Injectable()
export class DatabaseMigrationService {
  private readonly logger = new Logger(DatabaseMigrationService.name);

  constructor(private readonly publicPrisma: PublicPrismaService) {}

  async migrateAllTenants() {
    const tenants = await this.publicPrisma.tenant.findMany({
      include: { datasource: true },
    });

    this.logger.log(`Found ${tenants.length} tenants to migrate.`);

    const results = {
      success: 0,
      failed: 0,
      skipped: 0,
      details: [],
    };

    for (const tenant of tenants) {
      if (!tenant.datasource) {
        this.logger.warn(`Tenant ${tenant.code} has no datasource. Skipping.`);
        results.skipped++;
        results.details.push({
          tenant: tenant.code,
          status: 'skipped',
          reason: 'No datasource',
        });
        continue;
      }

      try {
        await this.migrateTenant(tenant.code, tenant.datasource.url);
        results.success++;
        results.details.push({
          tenant: tenant.code,
          status: 'success',
        });
      } catch (error) {
        this.logger.error(
          `Failed to migrate tenant ${tenant.code}: ${error.message}`,
        );
        results.failed++;
        results.details.push({
          tenant: tenant.code,
          status: 'failed',
          error: error.message,
        });
      }
    }

    return results;
  }

  async migrateTenant(tenantCode: string, datasourceUrl: string) {
    this.logger.log(`Migrating tenant: ${tenantCode}`);

    // 创建临时的 .env 文件
    const tempEnvPath = path.join(process.cwd(), `.env.${tenantCode}`);
    fs.writeFileSync(tempEnvPath, `DATABASE_URL="${datasourceUrl}"`);

    try {
      // 执行 Prisma 迁移
      const { stdout, stderr } = await execAsync(
        `dotenv -e ${tempEnvPath} -- npx prisma db push --schema=./prisma/schema.prisma`,
      );

      if (stderr) {
        this.logger.warn(`Migration warnings for tenant ${tenantCode}: ${stderr}`);
      }

      this.logger.log(`Migration completed for tenant ${tenantCode}: ${stdout}`);
      return { success: true, output: stdout };
    } finally {
      // 清理临时文件
      fs.unlinkSync(tempEnvPath);
    }
  }
}
```

### 使用方法

1. 创建一个 API 端点来触发迁移：

```typescript
// src/modules/database-migration/database-migration.controller.ts
import { Controller, Post, UseGuards } from '@nestjs/common';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { SuperAdminGuard } from '@/guards/super-admin.guard';
import { DatabaseMigrationService } from './database-migration.service';

@ApiTags('database-migration')
@Controller('database-migration')
@UseGuards(SuperAdminGuard)
@ApiBearerAuth()
export class DatabaseMigrationController {
  constructor(
    private readonly databaseMigrationService: DatabaseMigrationService,
  ) {}

  @Post('migrate-all-tenants')
  async migrateAllTenants() {
    return this.databaseMigrationService.migrateAllTenants();
  }
}
```

2. 将模块添加到 `app.module.ts`：

```typescript
import { DatabaseMigrationModule } from './modules/database-migration/database-migration.module';

@Module({
  imports: [
    // 其他模块...
    DatabaseMigrationModule,
  ],
})
export class AppModule {}
```

### 优点

- 提供了 API 接口，可以通过 API 触发迁移
- 可以集成到管理界面中
- 提供详细的迁移结果和日志
- 可以实现更复杂的迁移策略，如分批迁移、定时迁移等

### 缺点

- 需要实现额外的服务和 API
- 需要确保只有超级管理员可以访问迁移 API

## 3. 租户创建/更新时自动迁移

### 实现方式

在创建新租户或更新租户数据源时，自动执行数据库迁移：

```typescript
// src/modules/tenants/tenants.service.ts
import { Injectable } from '@nestjs/common';
import { PublicPrismaService } from '@/modules/prisma/public-prisma.service';
import { DatabaseMigrationService } from '@/modules/database-migration/database-migration.service';

@Injectable()
export class TenantsService {
  constructor(
    private readonly publicPrisma: PublicPrismaService,
    private readonly databaseMigrationService: DatabaseMigrationService,
  ) {}

  async createTenant(createTenantDto) {
    // 创建租户和数据源
    const tenant = await this.publicPrisma.tenant.create({
      data: {
        code: createTenantDto.code,
        name: createTenantDto.name,
        // 其他字段...
        datasource: {
          create: {
            name: createTenantDto.datasourceName,
            url: createTenantDto.datasourceUrl,
          },
        },
      },
      include: { datasource: true },
    });

    // 自动迁移新租户的数据库
    if (tenant.datasource) {
      await this.databaseMigrationService.migrateTenant(
        tenant.code,
        tenant.datasource.url,
      );
    }

    return tenant;
  }

  async updateTenantDatasource(tenantId: number, datasourceUrl: string) {
    // 更新租户的数据源
    const tenant = await this.publicPrisma.tenant.update({
      where: { id: tenantId },
      data: {
        datasource: {
          update: {
            url: datasourceUrl,
          },
        },
      },
      include: { datasource: true },
    });

    // 自动迁移更新后的数据库
    if (tenant.datasource) {
      await this.databaseMigrationService.migrateTenant(
        tenant.code,
        tenant.datasource.url,
      );
    }

    return tenant;
  }
}
```

### 优点

- 自动化程度高，无需手动触发迁移
- 确保新租户始终使用最新的数据库结构
- 当租户数据源变更时，自动更新表结构

### 缺点

- 可能导致租户创建/更新操作变慢
- 如果迁移失败，需要处理回滚逻辑
- 不适合大规模的架构变更

## 4. 使用 Prisma Migrate 而非 Prisma DB Push

### 实现方式

对于更复杂的数据库变更，可以使用 Prisma Migrate 而不是 DB Push：

```typescript
async migrateTenant(tenantCode: string, datasourceUrl: string) {
  this.logger.log(`Migrating tenant: ${tenantCode}`);

  // 创建临时的 .env 文件
  const tempEnvPath = path.join(process.cwd(), `.env.${tenantCode}`);
  fs.writeFileSync(tempEnvPath, `DATABASE_URL="${datasourceUrl}"`);

  try {
    // 创建迁移
    await execAsync(
      `dotenv -e ${tempEnvPath} -- npx prisma migrate dev --name tenant_update --schema=./prisma/schema.prisma`,
    );

    this.logger.log(`Migration completed for tenant ${tenantCode}`);
    return { success: true };
  } finally {
    // 清理临时文件
    fs.unlinkSync(tempEnvPath);
  }
}
```

### 优点

- 支持更复杂的数据库变更，如重命名字段、添加外键等
- 生成迁移历史，便于追踪变更
- 支持回滚操作

### 缺点

- 需要为每个租户维护独立的迁移历史
- 操作更复杂，可能需要手动解决冲突
- 不适合完全自动化的场景

## 5. 使用数据库复制模板

### 实现方式

维护一个最新的租户数据库模板，创建新租户时复制该模板：

```typescript
async createTenantDatabase(tenantCode: string, templateDatabaseUrl: string, newDatabaseUrl: string) {
  // 使用 pg_dump 和 psql 复制数据库结构
  await execAsync(
    `pg_dump --schema-only ${templateDatabaseUrl} | psql ${newDatabaseUrl}`,
  );
  
  return { success: true };
}
```

### 优点

- 快速创建新租户数据库
- 避免运行迁移脚本的开销
- 适合大量创建新租户的场景

### 缺点

- 只适用于创建新租户，不适用于更新现有租户
- 依赖特定数据库的工具（如 PostgreSQL 的 pg_dump）
- 需要维护模板数据库

## 最佳实践建议

1. **开发环境**：使用 `prisma db push` 快速迭代
2. **测试环境**：使用批量更新脚本或迁移服务
3. **生产环境**：
   - 小规模变更：使用迁移服务，分批更新租户
   - 大规模变更：计划维护窗口，使用 Prisma Migrate
   - 新租户：考虑使用数据库模板复制

4. **监控和回滚**：
   - 记录每次迁移的结果
   - 实现回滚机制
   - 设置迁移超时和重试策略

5. **安全考虑**：
   - 确保迁移脚本只能由授权用户执行
   - 使用最小权限原则配置数据库连接
   - 在迁移前备份数据库
