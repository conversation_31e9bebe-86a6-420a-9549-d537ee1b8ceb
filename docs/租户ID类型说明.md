# 租户ID类型说明

## 当前情况

在我们的多租户系统中，租户ID的类型已统一为数字类型：

1. 在 `prisma/public-schema.prisma` 中，`Tenant` 模型的 `id` 字段是 `Int` 类型（数字）
2. 在 `prisma/schema.prisma` 中，所有表的 `tenantId` 字段也是 `Int` 类型（数字）
3. 在代码中，我们直接使用租户的数字ID（`tenant.id`）作为 `tenantId` 的值，无需类型转换

这种统一的类型处理方式避免了之前可能存在的类型混淆问题。

## 解决方案

有两种可能的解决方案：

### 方案1：保持数据库模型不变，但在代码中明确区分（推荐）

我们可以保持数据库模型中的 `tenantId` 字段为字符串类型，但在代码中明确区分：
- 在代码中，我们将租户ID转换为字符串后再存储到数据库
- 在从数据库读取时，我们将字符串转换回数字（如果需要）

这种方案的优点是不需要修改数据库模型，避免了数据迁移的复杂性。缺点是代码中需要进行类型转换。

**实施步骤**：

1. 在所有使用 `tenantId` 的地方，确保将租户ID转换为字符串：
   ```typescript
   const tenantIdStr = tenant.id.toString();
   ```

2. 在需要使用租户ID进行比较或计算的地方，将字符串转换回数字：
   ```typescript
   const tenantIdNum = parseInt(tenantIdStr);
   ```

3. 在代码注释中明确说明 `tenantId` 是租户的数字ID的字符串表示：
   ```typescript
   /**
    * 租户ID（数字ID的字符串表示）
    */
   tenantId: string;
   ```

### 方案2：修改数据库模型，将 `tenantId` 字段改为数字类型

我们可以修改数据库模型，将 `tenantId` 字段改为 `Int` 类型。这需要进行数据库迁移，可能会比较复杂。

**实施步骤**：

1. 修改 `prisma/schema.prisma` 中的所有 `tenantId` 字段：
   ```prisma
   tenantId     Int               // 租户ID（数字）
   ```

2. 生成迁移脚本：
   ```bash
   npx prisma migrate dev --name change_tenant_id_to_int
   ```

3. 修改代码，确保不再将租户ID转换为字符串。

## 建议

考虑到我们已经修改了代码，使用租户的数字ID（转换为字符串）作为 `tenantId` 的值，**方案1**可能是更安全的选择。这样可以避免数据库迁移的复杂性，同时保持代码的一致性。

如果将来有机会进行大规模重构，可以考虑实施方案2，将 `tenantId` 字段改为数字类型，以提高查询性能和数据一致性。

## 注意事项

1. **类型一致性**：所有与租户ID相关的字段都应使用 `Int` 类型，确保类型一致。

2. **查询性能**：数字类型的外键通常比字符串类型有更好的查询性能，特别是在大规模数据集上。

3. **代码一致性**：在代码中应保持一致的命名和类型处理，避免混淆。

4. **文档**：在代码注释和文档中明确说明 `tenantId` 的类型为数字类型，以便其他开发人员理解。
