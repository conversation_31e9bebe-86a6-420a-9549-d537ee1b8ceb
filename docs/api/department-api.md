# 部门管理接口文档

本文档描述了系统中部门管理相关的 API 接口，供前端开发人员使用。

## 通用说明

### 基础 URL

所有 API 请求的基础 URL 为：`/api`

### 认证方式

所有接口都需要在请求头中携带 JWT 令牌：

```
Authorization: Bearer {token}
```

### 响应格式

所有接口的响应格式统一为：

```json
{
  "code": 0, // 状态码，0 表示成功，非 0 表示失败
  "data": {}, // 响应数据
  "message": "操作成功" // 响应消息
}
```

## 部门管理接口

### 1. 获取部门树形列表

- **URL**: `/departments/tree`
- **方法**: `GET`
- **描述**: 获取部门的树形结构列表
- **请求参数**:
  - `name`: 部门名称，模糊查询（可选）
  - `status`: 部门状态，0-禁用，1-启用（可选）
- **响应示例**:

```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "name": "总公司",
      "pid": 0,
      "status": 1,
      "orderNo": 1,
      "createTime": "2023-05-01T00:00:00.000Z",
      "remark": "总部",
      "children": [
        {
          "id": 2,
          "name": "研发部",
          "pid": 1,
          "status": 1,
          "orderNo": 1,
          "createTime": "2023-05-01T00:00:00.000Z",
          "remark": "负责产品研发",
          "children": []
        },
        {
          "id": 3,
          "name": "市场部",
          "pid": 1,
          "status": 1,
          "orderNo": 2,
          "createTime": "2023-05-01T00:00:00.000Z",
          "remark": "负责市场营销",
          "children": []
        }
      ]
    }
  ],
  "message": "获取成功"
}
```

### 2. 获取部门列表（扁平结构）

- **URL**: `/departments/list`
- **方法**: `GET`
- **描述**: 获取部门的扁平结构列表，用于下拉选择等场景
- **请求参数**:
  - `status`: 部门状态，0-禁用，1-启用（可选）
- **响应示例**:

```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "name": "总公司",
      "pid": 0,
      "status": 1
    },
    {
      "id": 2,
      "name": "研发部",
      "pid": 1,
      "status": 1
    },
    {
      "id": 3,
      "name": "市场部",
      "pid": 1,
      "status": 1
    }
  ],
  "message": "获取成功"
}
```

### 3. 获取部门详情

- **URL**: `/departments/:id`
- **方法**: `GET`
- **描述**: 获取指定ID的部门详情
- **路径参数**:
  - `id`: 部门ID
- **响应示例**:

```json
{
  "code": 0,
  "data": {
    "id": 2,
    "name": "研发部",
    "pid": 1,
    "status": 1,
    "orderNo": 1,
    "createTime": "2023-05-01T00:00:00.000Z",
    "updateTime": "2023-05-01T00:00:00.000Z",
    "remark": "负责产品研发"
  },
  "message": "获取成功"
}
```

### 4. 创建部门

- **URL**: `/departments`
- **方法**: `POST`
- **描述**: 创建新部门
- **请求参数**:

```json
{
  "name": "人力资源部",
  "pid": 1,
  "status": 1,
  "orderNo": 3,
  "remark": "负责人力资源管理"
}
```

- **响应示例**:

```json
{
  "code": 0,
  "data": {
    "id": 4,
    "name": "人力资源部",
    "pid": 1,
    "status": 1,
    "orderNo": 3,
    "createTime": "2023-05-02T00:00:00.000Z",
    "updateTime": "2023-05-02T00:00:00.000Z",
    "remark": "负责人力资源管理"
  },
  "message": "创建成功"
}
```

### 5. 更新部门

- **URL**: `/departments/:id`
- **方法**: `PUT`
- **描述**: 更新指定ID的部门
- **路径参数**:
  - `id`: 部门ID
- **请求参数**:

```json
{
  "name": "人力资源部",
  "pid": 1,
  "status": 1,
  "orderNo": 4,
  "remark": "负责人力资源管理和招聘"
}
```

- **响应示例**:

```json
{
  "code": 0,
  "data": {
    "id": 4,
    "name": "人力资源部",
    "pid": 1,
    "status": 1,
    "orderNo": 4,
    "createTime": "2023-05-02T00:00:00.000Z",
    "updateTime": "2023-05-02T01:00:00.000Z",
    "remark": "负责人力资源管理和招聘"
  },
  "message": "更新成功"
}
```

### 6. 删除部门

- **URL**: `/departments/:id`
- **方法**: `DELETE`
- **描述**: 删除指定ID的部门
- **路径参数**:
  - `id`: 部门ID
- **响应示例**:

```json
{
  "code": 0,
  "data": {
    "success": true
  },
  "message": "删除成功"
}
```

### 7. 更新部门状态

- **URL**: `/departments/:id/status`
- **方法**: `PATCH`
- **描述**: 更新指定ID的部门状态
- **路径参数**:
  - `id`: 部门ID
- **请求参数**:

```json
{
  "status": 0 // 0-禁用，1-启用
}
```

- **响应示例**:

```json
{
  "code": 0,
  "data": {
    "id": 4,
    "status": 0
  },
  "message": "更新成功"
}
```

### 8. 检查部门名称是否存在

- **URL**: `/departments/name-exists`
- **方法**: `GET`
- **描述**: 检查部门名称是否已存在
- **请求参数**:
  - `name`: 部门名称
  - `pid`: 父部门ID
  - `id`: 排除的部门ID（可选，用于编辑时检查）
- **响应示例**:

```json
{
  "code": 0,
  "data": false, // false表示不存在，true表示已存在
  "message": "检查成功"
}
```

## 数据结构

### 部门对象

| 字段名     | 类型   | 描述                     |
| ---------- | ------ | ------------------------ |
| id         | number | 部门ID                   |
| name       | string | 部门名称                 |
| pid   | number | 父部门ID，0表示顶级      |
| status     | number | 状态：0-禁用，1-启用     |
| orderNo    | number | 排序号                   |
| createTime | string | 创建时间                 |
| updateTime | string | 更新时间                 |
| remark     | string | 备注                     |
| children   | array  | 子部门列表（树形结构时） |

## 错误码说明

| 错误码 | 描述                     |
| ------ | ------------------------ |
| 0      | 成功                     |
| 400    | 请求参数错误             |
| 401    | 未授权或授权已过期       |
| 403    | 权限不足                 |
| 404    | 资源不存在               |
| 409    | 资源冲突（如名称已存在） |
| 500    | 服务器内部错误           |

## 注意事项

1. 删除部门时，如果该部门下有子部门，将会返回错误，需要先删除子部门。
2. 部门状态为禁用时，该部门下的所有子部门也会被视为禁用状态。
3. 创建或更新部门时，`pid` 必须是已存在的部门ID或0（表示顶级部门）。
4. `orderNo` 用于同级部门的排序，值越小排序越靠前。
