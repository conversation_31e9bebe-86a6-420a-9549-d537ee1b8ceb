# 功能代码管理 API 文档

## 概述

功能代码管理 API 提供了对系统功能代码的管理功能，包括查询、创建、更新和删除功能代码。功能代码用于控制租户可以使用的功能，是租户功能权限管理的基础。

## 基础信息

- **基础路径**: `/api/system/feature-codes`
- **认证方式**: JWT Bearer Token
- **权限要求**: 
  - 查询接口: 普通用户可访问
  - 修改接口: 仅系统管理员可访问

## API 列表

### 1. 获取功能代码列表

获取系统中所有可用的功能代码列表。

**请求**

```
GET /api/system/feature-codes/list
```

**查询参数**

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| includeInactive | Boolean | 否 | 是否包含未激活的功能代码，默认为 false |
| forceRefresh | Boolean | 否 | 是否强制刷新缓存，默认为 false |

**响应**

```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "code": "ai.ppt",
      "name": "AI PPT生成",
      "description": "AI PPT生成功能",
      "module": "AI模块",
      "isActive": true,
      "sortOrder": 0,
      "metadata": {},
      "createdAt": "2025-05-15T12:00:00.000Z",
      "updatedAt": "2025-05-15T12:00:00.000Z"
    },
    {
      "id": 2,
      "code": "ai.document",
      "name": "AI文档生成",
      "description": "AI文档生成功能",
      "module": "AI模块",
      "isActive": true,
      "sortOrder": 1,
      "metadata": {},
      "createdAt": "2025-05-15T12:00:00.000Z",
      "updatedAt": "2025-05-15T12:00:00.000Z"
    }
  ],
  "message": "获取成功"
}
```

### 2. 获取功能代码详情

获取指定功能代码的详细信息。

**请求**

```
GET /api/system/feature-codes/:code
```

**路径参数**

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| code | String | 是 | 功能代码，如 "ai.ppt" |

**响应**

```json
{
  "code": 0,
  "data": {
    "id": 1,
    "code": "ai.ppt",
    "name": "AI PPT生成",
    "description": "AI PPT生成功能",
    "module": "AI模块",
    "isActive": true,
    "sortOrder": 0,
    "metadata": {},
    "createdAt": "2025-05-15T12:00:00.000Z",
    "updatedAt": "2025-05-15T12:00:00.000Z"
  },
  "message": "获取成功"
}
```

### 3. 创建或更新功能代码

创建新的功能代码或更新已有功能代码。

**请求**

```
POST /api/system/feature-codes/upsert
```

**请求头**

```
Content-Type: application/json
```

**请求体**

```json
{
  "code": "ai.ppt",
  "name": "AI PPT生成",
  "description": "AI PPT生成功能",
  "module": "AI模块",
  "isActive": true,
  "sortOrder": 0,
  "metadata": {
    "icon": "file-ppt",
    "color": "#FF4D4F",
    "requiredPermissions": ["ai.use"]
  }
}
```

**请求参数说明**

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| code | String | 是 | 功能代码，唯一标识 |
| name | String | 是 | 功能名称 |
| description | String | 是 | 功能描述 |
| module | String | 是 | 所属模块 |
| isActive | Boolean | 否 | 是否激活，默认为 true |
| sortOrder | Number | 否 | 排序顺序，默认为 0 |
| metadata | Object | 否 | 额外元数据 |

**响应**

```json
{
  "code": 0,
  "data": {
    "id": 1,
    "code": "ai.ppt",
    "name": "AI PPT生成",
    "description": "AI PPT生成功能",
    "module": "AI模块",
    "isActive": true,
    "sortOrder": 0,
    "metadata": {
      "icon": "file-ppt",
      "color": "#FF4D4F",
      "requiredPermissions": ["ai.use"]
    },
    "createdAt": "2025-05-15T12:00:00.000Z",
    "updatedAt": "2025-05-15T12:00:00.000Z"
  },
  "message": "保存成功"
}
```

### 4. 删除功能代码

删除指定的功能代码。

**请求**

```
DELETE /api/system/feature-codes/:code
```

**路径参数**

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| code | String | 是 | 功能代码，如 "ai.ppt" |

**响应**

```json
{
  "code": 0,
  "data": null,
  "message": "删除成功"
}
```

## 错误码

| 错误码 | 描述 |
|-------|------|
| 0 | 成功 |
| 10001 | 参数错误 |
| 10002 | 资源不存在 |
| 10003 | 权限不足 |
| 10004 | 操作失败 |
| 10005 | 系统错误 |

## 数据模型

### FeatureCode

| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | Number | 主键ID |
| code | String | 功能代码，唯一标识 |
| name | String | 功能名称 |
| description | String | 功能描述 |
| module | String | 所属模块 |
| isActive | Boolean | 是否激活 |
| sortOrder | Number | 排序顺序 |
| metadata | Object | 额外元数据 |
| createdAt | DateTime | 创建时间 |
| updatedAt | DateTime | 更新时间 |

## 使用示例

### 获取功能代码列表

**请求**

```bash
curl -X GET "http://localhost:4000/api/system/feature-codes/list" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**响应**

```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "code": "ai.ppt",
      "name": "AI PPT生成",
      "description": "AI PPT生成功能",
      "module": "AI模块",
      "isActive": true,
      "sortOrder": 0,
      "metadata": {},
      "createdAt": "2025-05-15T12:00:00.000Z",
      "updatedAt": "2025-05-15T12:00:00.000Z"
    }
  ],
  "message": "获取成功"
}
```

### 创建新功能代码

**请求**

```bash
curl -X POST "http://localhost:4000/api/system/feature-codes/upsert" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "ai.image",
    "name": "AI图像生成",
    "description": "AI图像生成功能",
    "module": "AI模块",
    "isActive": true,
    "sortOrder": 2,
    "metadata": {
      "icon": "file-image",
      "color": "#1890FF"
    }
  }'
```

**响应**

```json
{
  "code": 0,
  "data": {
    "id": 3,
    "code": "ai.image",
    "name": "AI图像生成",
    "description": "AI图像生成功能",
    "module": "AI模块",
    "isActive": true,
    "sortOrder": 2,
    "metadata": {
      "icon": "file-image",
      "color": "#1890FF"
    },
    "createdAt": "2025-05-15T12:30:00.000Z",
    "updatedAt": "2025-05-15T12:30:00.000Z"
  },
  "message": "保存成功"
}
```

## 注意事项

1. 功能代码一旦创建并被租户使用，不建议删除，可以通过设置 `isActive` 为 `false` 来禁用。
2. 功能代码的 `code` 字段是唯一标识，建议使用 `[模块].[子功能]` 的格式，如 `ai.ppt`。
3. 功能代码的 `metadata` 字段可以存储任意额外信息，如图标、颜色、所需权限等。
4. 系统启动时会自动将代码常量中定义的功能代码同步到数据库，但不会覆盖已有记录的内容。

## 相关接口

- [租户功能管理 API](/api/system/tenant-features)
- [功能模板管理 API](/api/system/feature-templates)
- [租户配置管理 API](/api/system/tenant-configs)
