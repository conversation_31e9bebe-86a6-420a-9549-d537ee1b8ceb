# Vue Vben Admin 前后端集成指南

本文档提供了将Vue Vben Admin前端与后端API进行集成的详细指南。

## 1. 接口适配

如果后端API与Vue Vben Admin预期的接口不完全一致，需要在前端进行适配。以下是主要需要适配的文件：

### 1.1 认证相关接口

文件路径：`src/api/core/auth.ts`

```typescript
// 登录接口
export async function loginApi(data: AuthApi.LoginParams) {
  return requestClient.post<AuthApi.LoginResult>('/auth/login', data);
}

// 刷新Token接口
export async function refreshTokenApi() {
  return baseRequestClient.post<AuthApi.RefreshTokenResult>('/auth/refresh');
}

// 退出登录接口
export async function logoutApi() {
  return baseRequestClient.post('/auth/logout');
}

// 获取权限码接口
export async function getAccessCodesApi() {
  return requestClient.get<string[]>('/auth/codes');
}
```

### 1.2 用户信息接口

文件路径：`src/api/core/user.ts`

```typescript
// 获取用户信息接口
export async function getUserInfoApi() {
  return requestClient.get<UserInfo>('/user/info');
}
```

### 1.3 菜单相关接口

文件路径：`src/api/system/menu.ts`和`src/api/menu.ts`

```typescript
// 获取所有菜单（用于生成导航菜单）
export async function getAllMenusApi() {
  return requestClient.get<MenuRecordRaw[]>('/menu/all');
}

// 获取菜单列表（用于菜单管理）
export async function getMenuList() {
  return requestClient.get<Array<SystemMenuApi.SystemMenu>>('/system/menu/list');
}
```

### 1.4 角色相关接口

文件路径：`src/api/system/role.ts`

```typescript
// 获取角色列表
async function getRoleList(params: Recordable<any>) {
  return requestClient.get<Array<SystemRoleApi.SystemRole>>('/system/role/list', { params });
}

// 创建角色
async function createRole(data: Omit<SystemRoleApi.SystemRole, 'id'>) {
  return requestClient.post('/system/role', data);
}
```

## 2. 数据格式适配

### 2.1 用户信息格式

Vue Vben Admin期望的用户信息格式：

```typescript
interface UserInfo {
  userId: string;       // 用户ID
  username: string;     // 用户名
  realName: string;     // 用户真实姓名
  avatar: string;       // 头像URL
  desc?: string;        // 用户描述
  homePath?: string;    // 用户首页路径
  roles: string[];      // 用户角色列表
}
```

如果后端返回的用户信息格式不一致，需要在`getUserInfoApi`中进行转换：

```typescript
export async function getUserInfoApi() {
  const response = await requestClient.get('/your-backend-api/user/profile');
  
  // 转换为Vue Vben Admin期望的格式
  return {
    userId: response.id.toString(),
    username: response.email,
    realName: `${response.firstName} ${response.lastName}`,
    avatar: response.avatarUrl || '',
    roles: response.roles || [],
    homePath: '/dashboard'
  };
}
```

### 2.2 菜单数据格式

Vue Vben Admin期望的菜单格式：

```typescript
interface MenuRecordRaw {
  id: string;
  name: string;
  path: string;
  component?: string;
  redirect?: string;
  meta: {
    title: string;
    icon?: string;
    orderNo?: number;
    // 其他元数据...
  };
  children?: MenuRecordRaw[];
}
```

如果后端返回的菜单格式不一致，需要在`getAllMenusApi`中进行转换。

### 2.3 角色数据格式

Vue Vben Admin期望的角色格式：

```typescript
interface SystemRole {
  id: string;
  name: string;
  permissions: string[];
  remark?: string;
  status: 0 | 1;
}
```

## 3. 请求配置

文件路径：`src/api/request.ts`

### 3.1 基础URL配置

```typescript
const baseURL = import.meta.env.VITE_API_URL || '';

const requestClient = new RequestClient({
  baseURL,
  // 其他配置...
});
```

确保在`.env.development`和`.env.production`中正确设置`VITE_API_URL`。

### 3.2 请求拦截器

```typescript
requestClient.interceptors.request.use((config) => {
  // 添加Token
  const accessToken = accessStore.accessToken;
  if (accessToken) {
    config.headers.Authorization = `Bearer ${accessToken}`;
  }
  return config;
});
```

### 3.3 响应拦截器

```typescript
requestClient.interceptors.response.use(
  (response) => {
    // 处理成功响应
    const { data } = response;
    // 如果后端返回的数据格式是 { code, data, message }
    if (data.code !== undefined && data.data !== undefined) {
      if (data.code === 200) {
        return data.data;
      } else {
        // 处理业务错误
        message.error(data.message || '请求失败');
        return Promise.reject(new Error(data.message || '请求失败'));
      }
    }
    // 直接返回数据
    return data;
  },
  (error) => {
    // 处理错误响应
    if (error.response) {
      const { status } = error.response;
      
      // 处理401未授权错误
      if (status === 401) {
        // Token过期处理
        doReAuthenticate();
      }
      
      // 处理其他错误
      message.error(error.message || '请求失败');
    }
    return Promise.reject(error);
  }
);
```

## 4. 权限控制配置

文件路径：`src/preferences.ts`

```typescript
// 权限控制模式：frontend-前端控制，backend-后端控制
export const overridesPreferences = {
  app: {
    accessMode: 'backend',
    // 其他配置...
  },
};
```

## 5. 常见问题与解决方案

### 5.1 登录后无法获取用户信息

- 检查Token是否正确传递
- 检查用户信息接口返回格式是否符合预期
- 检查网络请求是否有跨域问题

### 5.2 菜单无法正确显示

- 检查菜单接口返回格式是否符合预期
- 检查路由配置是否正确
- 检查权限控制模式是否配置正确

### 5.3 权限控制不生效

- 检查权限码接口返回是否正确
- 检查组件上的权限指令是否正确
- 检查权限控制模式是否配置正确

## 6. 测试与调试

1. 使用浏览器开发者工具检查网络请求
2. 在请求拦截器和响应拦截器中添加日志
3. 使用Mock数据进行前端测试
4. 使用Postman等工具测试后端API
