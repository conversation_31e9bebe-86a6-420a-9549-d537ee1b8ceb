# 多租户系统 API 文档

本文档描述了多租户系统的 API 接口，供前端开发人员使用。

## 通用说明

### 基础 URL

所有 API 请求的基础 URL 为：`/api`

### 认证方式

除了登录接口外，所有接口都需要在请求头中携带 JWT 令牌：

```
Authorization: Bearer {token}
```

### 租户标识

对于租户相关的请求，需要在请求头中携带租户代码：

```
x-tenant-code: {tenantCode}
```

### 响应格式

所有接口的响应格式统一为：

```json
{
  "code": 0,         // 状态码，0 表示成功，非 0 表示失败
  "data": {},        // 响应数据
  "message": "操作成功" // 响应消息
}
```

## 认证相关接口

### 登录

- **URL**: `/auth/login`
- **方法**: `POST`
- **描述**: 用户登录，获取 JWT 令牌
- **请求参数**:

```json
{
  "username": "admin",     // 用户名
  "password": "123456",    // 密码
  "tenantCode": "tenant1"  // 租户代码（系统用户登录时可不传）
}
```

- **响应示例**:

```json
{
  "code": 0,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires": 3600
  },
  "message": "登录成功"
}
```

### 获取用户信息

- **URL**: `/user/info`
- **方法**: `GET`
- **描述**: 获取当前登录用户的信息
- **响应示例**:

```json
{
  "code": 0,
  "data": {
    "userId": 1,
    "username": "admin",
    "realName": "管理员",
    "avatar": "https://example.com/avatar.png",
    "userType": "SYSTEM",  // SYSTEM: 系统用户, TENANT: 租户用户
    "tenantId": "tenant1", // 租户用户才有此字段
    "roles": ["admin"]
  },
  "message": "获取成功"
}
```

### 刷新令牌

- **URL**: `/auth/refresh`
- **方法**: `POST`
- **描述**: 使用刷新令牌获取新的访问令牌
- **请求参数**:

```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

- **响应示例**:

```json
{
  "code": 0,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires": 3600
  },
  "message": "刷新成功"
}
```

### 退出登录

- **URL**: `/auth/logout`
- **方法**: `POST`
- **描述**: 用户退出登录
- **响应示例**:

```json
{
  "code": 0,
  "data": null,
  "message": "退出成功"
}
```

## 菜单相关接口

### 获取菜单树

- **URL**: `/menu/all`
- **方法**: `GET`
- **描述**: 获取当前用户可访问的所有菜单，构建成树形结构
- **响应示例**:

```json
{
  "code": 0,
  "data": [
    {
      "id": "1",
      "name": "系统管理",
      "path": "/system",
      "component": "LAYOUT",
      "redirect": "/system/user",
      "meta": {
        "title": "系统管理",
        "icon": "setting",
        "orderNo": 1
      },
      "children": [
        {
          "id": "2",
          "name": "用户管理",
          "path": "/system/user",
          "component": "system/user/index",
          "meta": {
            "title": "用户管理",
            "icon": "user",
            "orderNo": 1
          }
        }
      ]
    }
  ],
  "message": "获取成功"
}
```

### 获取菜单列表（管理用）

- **URL**: `/system/menu/list`
- **方法**: `GET`
- **描述**: 获取所有菜单列表，用于后台管理
- **响应示例**:

```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "name": "系统管理",
      "path": "/system",
      "pid": null,
      "redirect": "/system/user",
      "type": "menu",
      "icon": "setting",
      "component": "LAYOUT",
      "permission": null,
      "orderNo": 1,
      "status": 1,
      "meta": {
        "title": "系统管理",
        "icon": "setting",
        "orderNo": 1
      },
      "createdAt": "2023-05-01T00:00:00.000Z",
      "updatedAt": "2023-05-01T00:00:00.000Z",
      "children": [
        {
          "id": 2,
          "name": "用户管理",
          "path": "/system/user",
          "pid": 1,
          "redirect": null,
          "type": "menu",
          "icon": "user",
          "component": "system/user/index",
          "permission": "system:user:list",
          "orderNo": 1,
          "status": 1,
          "meta": {
            "title": "用户管理",
            "icon": "user",
            "orderNo": 1
          },
          "createdAt": "2023-05-01T00:00:00.000Z",
          "updatedAt": "2023-05-01T00:00:00.000Z"
        }
      ]
    }
  ],
  "message": "获取成功"
}
```

### 创建菜单

- **URL**: `/system/menu`
- **方法**: `POST`
- **描述**: 创建新菜单
- **请求参数**:

```json
{
  "name": "角色管理",
  "path": "/system/role",
  "pid": 1,
  "type": "menu",
  "icon": "role",
  "component": "system/role/index",
  "permission": "system:role:list",
  "orderNo": 2,
  "status": 1,
  "meta": {
    "title": "角色管理",
    "icon": "role",
    "orderNo": 2
  }
}
```

- **响应示例**:

```json
{
  "code": 0,
  "data": {
    "id": 3,
    "name": "角色管理",
    "path": "/system/role",
    "pid": 1,
    "redirect": null,
    "type": "menu",
    "icon": "role",
    "component": "system/role/index",
    "permission": "system:role:list",
    "orderNo": 2,
    "status": 1,
    "meta": {
      "title": "角色管理",
      "icon": "role",
      "orderNo": 2
    },
    "createdAt": "2023-05-01T00:00:00.000Z",
    "updatedAt": "2023-05-01T00:00:00.000Z"
  },
  "message": "创建成功"
}
```

### 更新菜单

- **URL**: `/system/menu/:id`
- **方法**: `PUT`
- **描述**: 更新指定ID的菜单
- **请求参数**:

```json
{
  "name": "角色管理",
  "path": "/system/role",
  "pid": 1,
  "type": "menu",
  "icon": "peoples",
  "component": "system/role/index",
  "permission": "system:role:list",
  "orderNo": 2,
  "status": 1,
  "meta": {
    "title": "角色管理",
    "icon": "peoples",
    "orderNo": 2
  }
}
```

- **响应示例**:

```json
{
  "code": 0,
  "data": {
    "id": 3,
    "name": "角色管理",
    "path": "/system/role",
    "pid": 1,
    "redirect": null,
    "type": "menu",
    "icon": "peoples",
    "component": "system/role/index",
    "permission": "system:role:list",
    "orderNo": 2,
    "status": 1,
    "meta": {
      "title": "角色管理",
      "icon": "peoples",
      "orderNo": 2
    },
    "createdAt": "2023-05-01T00:00:00.000Z",
    "updatedAt": "2023-05-01T00:00:00.000Z"
  },
  "message": "更新成功"
}
```

### 删除菜单

- **URL**: `/system/menu/:id`
- **方法**: `DELETE`
- **描述**: 删除指定ID的菜单
- **响应示例**:

```json
{
  "code": 0,
  "data": {
    "success": true
  },
  "message": "删除成功"
}
```

### 检查菜单名称是否存在

- **URL**: `/system/menu/name-exists`
- **方法**: `GET`
- **描述**: 检查菜单名称是否已存在
- **请求参数**:
  - `name`: 菜单名称
  - `id`: 排除的菜单ID（可选）
- **响应示例**:

```json
{
  "code": 0,
  "data": false,
  "message": "检查成功"
}
```

### 检查菜单路径是否存在

- **URL**: `/system/menu/path-exists`
- **方法**: `GET`
- **描述**: 检查菜单路径是否已存在
- **请求参数**:
  - `path`: 菜单路径
  - `id`: 排除的菜单ID（可选）
- **响应示例**:

```json
{
  "code": 0,
  "data": false,
  "message": "检查成功"
}
```

## 菜单类型说明

菜单类型（`type`）支持以下值：

- `menu`: 菜单
- `button`: 按钮
- `directory`: 目录
- `catalog`: 目录
- `embedded`: 嵌入式
- `link`: 链接

## 错误码说明

| 错误码 | 说明 |
| ------ | ---- |
| 0      | 成功 |
| 400    | 请求参数错误 |
| 401    | 未授权 |
| 403    | 禁止访问 |
| 404    | 资源不存在 |
| 500    | 服务器内部错误 |
