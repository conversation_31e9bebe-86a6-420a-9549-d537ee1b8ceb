# 认证与授权接口文档

本文档描述了vue-vben-admin系统中与认证和授权相关的API接口规范。

## 1. 登录接口

用于用户登录系统，获取访问令牌。

**请求**：
- 方法：`POST`
- 路径：`/auth/login`
- 内容类型：`application/json`

**请求参数**：
```json
{
  "username": "string",  // 用户名
  "password": "string"   // 密码
}
```

**响应**：
```json
{
  "code": 200,           // 状态码
  "data": {
    "accessToken": "string"  // JWT访问令牌
  },
  "message": "string"    // 消息
}
```

**错误响应**：
- `400 Bad Request`：参数错误
- `401 Unauthorized`：用户名或密码错误
- `500 Internal Server Error`：服务器错误

## 2. 获取用户信息接口

获取当前登录用户的详细信息。

**请求**：
- 方法：`GET`
- 路径：`/user/info`
- 头部：需要包含`Authorization: Bearer {accessToken}`

**响应**：
```json
{
  "code": 200,           // 状态码
  "data": {
    "userId": "string",      // 用户ID
    "username": "string",    // 用户名
    "realName": "string",    // 用户真实姓名（显示名）
    "avatar": "string",      // 头像URL
    "desc": "string",        // 用户描述（可选）
    "homePath": "string",    // 用户首页路径（可选）
    "roles": ["string"]      // 用户角色列表，如['admin', 'editor']
  },
  "message": "string"    // 消息
}
```

**错误响应**：
- `401 Unauthorized`：未授权或Token无效
- `500 Internal Server Error`：服务器错误

## 3. 获取角色编码接口

获取当前用户的角色编码列表。

**请求**：
- 方法：`GET`
- 路径：`/auth/codes`
- 头部：需要包含`Authorization: Bearer {accessToken}`

**响应**：
```json
{
  "code": 200,           // 状态码
  "data": ["string"],    // 角色编码列表，如["ADMIN_100001", "USER_100001"]
  "message": "string"    // 消息
}
```

**错误响应**：
- `401 Unauthorized`：未授权或Token无效
- `500 Internal Server Error`：服务器错误

## 4. 退出登录接口

用户退出登录。

**请求**：
- 方法：`POST`
- 路径：`/auth/logout`
- 头部：需要包含`Authorization: Bearer {accessToken}`

**响应**：
```json
{
  "code": 200,           // 状态码
  "data": {
    "success": true      // 是否成功
  },
  "message": "string"    // 消息
}
```

**错误响应**：
- `401 Unauthorized`：未授权或Token无效
- `500 Internal Server Error`：服务器错误

## 5. 刷新Token接口

刷新访问令牌。

**请求**：
- 方法：`POST`
- 路径：`/auth/refresh`
- 头部：需要包含`Authorization: Bearer {accessToken}`或刷新令牌

**响应**：
```json
{
  "code": 200,           // 状态码
  "data": {
    "data": "string",    // 新的accessToken
    "status": 200        // 状态码
  },
  "message": "string"    // 消息
}
```

**错误响应**：
- `401 Unauthorized`：未授权或Token无效
- `500 Internal Server Error`：服务器错误

## 实现注意事项

1. **Token处理**：
   - 设置合理的Token过期时间（建议1-2小时）
   - 实现Token刷新机制
   - 在响应拦截器中处理Token过期情况

2. **安全性**：
   - 使用HTTPS传输
   - 密码应该在前端进行加密或哈希处理
   - 实现防暴力破解机制（如登录失败次数限制）

3. **错误处理**：
   - 提供明确的错误消息
   - 不要在错误响应中泄露敏感信息
