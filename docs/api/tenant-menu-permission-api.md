# 基于功能模板的租户菜单权限控制 API 接口文档

本文档描述了通过功能模板控制租户可用菜单范围的 API 接口。通过扩展现有的功能模板功能，系统管理员可以为每个租户分配可访问的菜单，如果没有分配菜单范围，则租户默认不可访问任何菜单。

## 基本设计

在现有功能模板的基础上，我们扩展以下内容：

1. 在功能模板中添加关联菜单的能力
2. 为每个功能代码关联对应的系统菜单
3. 当租户启用某个功能时，自动授予相关菜单的访问权限
4. 当租户禁用某个功能时，自动撤销相关菜单的访问权限

## 接口列表

### 1. 获取功能代码关联的菜单

获取指定功能代码关联的系统菜单。

**请求**:
- **方法**: GET
- **路径**: `/api/system/feature-codes/{code}/menus`
- **路径参数**:
  - `code`: 功能代码，字符串

**响应**:
```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "menuId": 5,
      "featureCode": "ai.ppt",
      "menuName": "AI PPT",
      "menuPath": "/ai/ppt",
      "createdAt": "2025-05-18T10:00:00.000Z",
      "updatedAt": "2025-05-18T10:00:00.000Z"
    },
    // 更多菜单项...
  ],
  "message": "操作成功"
}
```

### 2. 设置功能代码关联的菜单

设置指定功能代码关联的系统菜单。

**请求**:
- **方法**: POST
- **路径**: `/api/system/feature-codes/{code}/menus`
- **路径参数**:
  - `code`: 功能代码，字符串
- **请求体**:
```json
{
  "menuIds": [5, 6, 7]  // 关联的系统菜单ID列表
}
```

> 注意：之前的API实现要求请求体中包含`featureCode`字段，现已更新为仅需要`menuIds`字段，功能代码直接从URL路径参数中获取。

**响应**:
```json
{
  "code": 0,
  "data": true,
  "message": "功能菜单关联设置成功"
}
```

### 3. 获取功能模板关联的菜单

获取指定功能模板关联的所有菜单。

**请求**:
- **方法**: GET
- **路径**: `/api/system/feature-templates/{code}/menus`
- **路径参数**:
  - `code`: 功能模板代码，字符串

**响应**:
```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "name": "Dashboard",
      "path": "/dashboard",
      "component": "BasicLayout",
      "meta": {
        "title": "仪表盘",
        "icon": "lucide:layout-dashboard"
      },
      "featureCodes": ["base", "dashboard"],
      "children": [
        // 子菜单...
      ]
    },
    // 更多菜单...
  ],
  "message": "操作成功"
}
```

### 4. 获取租户可用菜单

获取指定租户根据已启用功能可访问的菜单。

**请求**:
- **方法**: GET
- **路径**: `/api/system/tenant-features/{tenantId}/menus`
- **路径参数**:
  - `tenantId`: 租户ID，整数

**响应**:
```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "menuId": 5,
      "featureCode": "ai.ppt",
      "menuName": "AI PPT",
      "menuPath": "/ai/ppt",
      "enabled": true
    },
    // 更多菜单项...
  ],
  "message": "操作成功"
}
```

### 5. 同步租户菜单

根据租户已启用的功能，同步更新租户的菜单数据。

**请求**:
- **方法**: POST
- **路径**: `/api/system/tenant-features/{tenantId}/sync-menus`
- **路径参数**:
  - `tenantId`: 租户ID，整数

**响应**:
```json
{
  "code": 0,
  "data": true,
  "message": "租户菜单同步成功"
}
```

## 数据模型

### FeatureMenu

功能代码与系统菜单的关联表。

| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | Integer | 主键ID |
| featureCode | String | 功能代码 |
| menuId | Integer | 系统菜单ID |
| createdAt | DateTime | 创建时间 |
| updatedAt | DateTime | 更新时间 |

### TenantMenu (扩展)

在现有的TenantMenu表中添加字段：

| 字段名 | 类型 | 描述 |
|-------|------|------|
| sourceMenuId | Integer | 源系统菜单ID |
| featureCode | String | 关联的功能代码 |

## 菜单获取流程

所有用户（系统用户和租户用户）都使用统一的菜单获取接口：

**请求**:
- **方法**: GET
- **路径**: `/api/menu/all`

后端会根据用户类型和权限返回不同的菜单数据：

1. **系统用户**：返回系统菜单，根据用户角色过滤
2. **租户用户**：返回租户菜单，同时考虑以下因素：
   - 租户启用的功能
   - 用户的角色权限
   - 菜单的状态（启用/禁用）

## 注意事项

1. **权限控制**：
   - 只有系统管理员可以管理功能与菜单的关联
   - 租户管理员只能在分配的功能范围内管理租户用户的菜单权限

2. **菜单依赖关系**：
   - 需要处理菜单的父子关系，确保启用子菜单时父菜单也被启用
   - 禁用父菜单时，可能需要同时禁用所有子菜单

3. **缓存处理**：
   - 功能启用/禁用后，需要清除相关的菜单缓存
   - 考虑使用事件机制，在功能状态变更时触发菜单同步

4. **默认行为**：
   - 如果租户未启用任何功能，将无法访问任何菜单
   - 如果功能未关联任何菜单，不会授予任何菜单权限

## 错误码

| 错误码 | 描述 |
|-------|------|
| 0 | 成功 |
| 400001 | 参数错误 |
| 400002 | 租户不存在 |
| 400003 | 菜单不存在 |
| 500001 | 服务器内部错误 |
| 500002 | 数据库操作失败 |
| 500003 | 租户菜单同步失败 |

## 前端实现要求

前端需要实现以下功能模块来支持基于功能模板的租户菜单权限控制：

### 1. 系统管理员功能

#### 1.1 功能代码菜单管理

- 实现功能代码列表页面，展示所有可用的功能代码
- 为每个功能代码提供"管理关联菜单"的操作入口
- 在关联菜单页面中，展示系统所有菜单的树形结构，并允许多选
- 提供保存按钮，调用"设置功能代码关联的菜单"API保存选择结果
- 提供预览功能，显示当前功能代码关联的所有菜单

#### 1.2 功能模板菜单预览

- 在功能模板详情页面中，添加"查看关联菜单"的功能
- 调用"获取功能模板关联的菜单"API，以树形结构展示该模板包含的所有菜单
- 为每个菜单项标注关联的功能代码，便于管理员了解菜单与功能的对应关系

#### 1.3 租户菜单管理

- 在租户管理页面中，添加"查看可用菜单"的功能
- 调用"获取租户可用菜单"API，展示租户当前可访问的所有菜单
- 提供"同步菜单"按钮，调用"同步租户菜单"API更新租户菜单数据
- 展示同步结果，包括新增、更新和禁用的菜单数量

### 2. 菜单渲染功能

#### 2.1 统一菜单获取

- 修改现有的菜单获取逻辑，统一使用`/api/menu/all`接口
- 根据返回的菜单数据构建路由和导航菜单
- 处理菜单的层级关系，确保正确显示父子菜单

#### 2.2 动态路由生成

- 基于获取的菜单数据，动态生成前端路由配置
- 处理菜单中的`component`字段，映射到对应的前端组件
- 支持路由懒加载，提高应用性能

#### 2.3 权限指示

- 在系统管理员的菜单管理界面中，清晰标识哪些菜单已分配给哪些功能
- 使用视觉提示（如标签、颜色编码）显示菜单的启用/禁用状态
- 对于租户用户，隐藏未授权的菜单项

### 3. 用户体验优化

#### 3.1 菜单选择器

- 实现树形菜单选择器组件，支持多选、搜索和展开/折叠
- 支持按菜单类型（目录、菜单、按钮）进行筛选
- 提供"全选"和"取消全选"功能

#### 3.2 批量操作

- 支持批量为多个功能代码分配相同的菜单
- 支持批量为多个租户同步菜单数据

#### 3.3 变更预览

- 在保存菜单关联前，提供变更预览功能
- 清晰显示将要添加和移除的菜单项
- 提供确认对话框，避免误操作

## 总结

通过将菜单权限控制整合到现有的功能模板功能中，我们实现了以下目标：

1. 系统管理员可以为每个功能代码关联相应的菜单
2. 租户启用功能时自动获得相关菜单的访问权限
3. 租户禁用功能时自动失去相关菜单的访问权限
4. 如果租户未启用任何功能，则默认无法访问任何菜单
5. 所有用户使用统一的`/api/menu/all`接口获取菜单，保持前端代码一致性

这种设计不仅复用了现有的功能模板架构，还使系统的权限控制更加一致和易于管理。前端实现需要关注用户体验，确保管理界面直观易用，同时保证菜单渲染的性能和正确性。
