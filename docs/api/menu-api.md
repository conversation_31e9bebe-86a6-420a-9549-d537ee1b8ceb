# 菜单与权限接口文档

本文档描述了vue-vben-admin系统中与菜单和权限相关的API接口规范。

## 1. 获取所有菜单接口

获取当前用户可访问的所有菜单。

**请求**：
- 方法：`GET`
- 路径：`/menu/all`
- 头部：需要包含`Authorization: Bearer {accessToken}`

**响应**：
```json
{
  "code": 200,           // 状态码
  "data": [
    {
      "id": "string",                // 菜单ID
      "name": "string",              // 菜单名称
      "path": "string",              // 路由路径
      "component": "string",         // 组件路径
      "redirect": "string",          // 重定向路径
      "meta": {
        "title": "string",           // 菜单标题
        "icon": "string",            // 菜单图标
        "orderNo": 0,                // 排序号
        "hideMenu": false,           // 是否隐藏菜单
        "ignoreAuth": false,         // 是否忽略权限
        "hideBreadcrumb": false,     // 是否隐藏面包屑
        "hideChildrenInMenu": false, // 是否隐藏子菜单
        "currentActiveMenu": "string" // 当前激活的菜单
      },
      "children": []                 // 子菜单，结构与父级相同
    }
  ],
  "message": "string"    // 消息
}
```

**错误响应**：
- `401 Unauthorized`：未授权或Token无效
- `500 Internal Server Error`：服务器错误

## 2. 获取菜单列表接口

获取系统中所有菜单列表，用于菜单管理和权限分配。

**请求**：
- 方法：`GET`
- 路径：`/system/menu/list`
- 头部：需要包含`Authorization: Bearer {accessToken}`

**响应**：
```json
{
  "code": 200,           // 状态码
  "data": [
    {
      "id": "string",          // 菜单ID
      "name": "string",        // 菜单名称
      "path": "string",        // 路由路径
      "pid": "string",         // 父级ID
      "redirect": "string",    // 重定向
      "type": "string",        // 菜单类型：menu-菜单，button-按钮
      "icon": "string",        // 图标
      "component": "string",   // 组件
      "permission": "string",  // 权限标识
      "orderNo": 0,            // 排序号
      "status": 1,             // 状态：0-禁用，1-启用
      "children": []           // 子菜单，结构与父级相同
    }
  ],
  "message": "string"    // 消息
}
```

**错误响应**：
- `401 Unauthorized`：未授权或Token无效
- `403 Forbidden`：权限不足
- `500 Internal Server Error`：服务器错误

## 3. 创建菜单接口

创建新的菜单项。

**请求**：
- 方法：`POST`
- 路径：`/system/menu`
- 头部：需要包含`Authorization: Bearer {accessToken}`
- 内容类型：`application/json`

**请求参数**：
```json
{
  "name": "string",        // 菜单名称
  "path": "string",        // 路由路径
  "pid": "string",         // 父级ID
  "redirect": "string",    // 重定向
  "type": "string",        // 菜单类型：menu-菜单，button-按钮
  "icon": "string",        // 图标
  "component": "string",   // 组件
  "permission": "string",  // 权限标识
  "orderNo": 0,            // 排序号
  "status": 1              // 状态：0-禁用，1-启用
}
```

**响应**：
```json
{
  "code": 200,           // 状态码
  "data": {
    "id": "string",      // 新创建的菜单ID
    "name": "string",    // 菜单名称
    // 其他字段与请求参数相同
  },
  "message": "string"    // 消息
}
```

**错误响应**：
- `400 Bad Request`：参数错误
- `401 Unauthorized`：未授权或Token无效
- `403 Forbidden`：权限不足
- `500 Internal Server Error`：服务器错误

## 4. 更新菜单接口

更新现有菜单项。

**请求**：
- 方法：`PUT`
- 路径：`/system/menu/{id}`
- 头部：需要包含`Authorization: Bearer {accessToken}`
- 内容类型：`application/json`

**请求参数**：
```json
{
  "name": "string",        // 菜单名称
  "path": "string",        // 路由路径
  "pid": "string",         // 父级ID
  "redirect": "string",    // 重定向
  "type": "string",        // 菜单类型：menu-菜单，button-按钮
  "icon": "string",        // 图标
  "component": "string",   // 组件
  "permission": "string",  // 权限标识
  "orderNo": 0,            // 排序号
  "status": 1              // 状态：0-禁用，1-启用
}
```

**响应**：
```json
{
  "code": 200,           // 状态码
  "data": {
    "id": "string",      // 菜单ID
    "name": "string",    // 菜单名称
    // 其他字段与请求参数相同
  },
  "message": "string"    // 消息
}
```

**错误响应**：
- `400 Bad Request`：参数错误
- `401 Unauthorized`：未授权或Token无效
- `403 Forbidden`：权限不足
- `404 Not Found`：菜单不存在
- `500 Internal Server Error`：服务器错误

## 5. 删除菜单接口

删除指定的菜单项。

**请求**：
- 方法：`DELETE`
- 路径：`/system/menu/{id}`
- 头部：需要包含`Authorization: Bearer {accessToken}`

**响应**：
```json
{
  "code": 200,           // 状态码
  "data": {
    "success": true      // 是否成功
  },
  "message": "string"    // 消息
}
```

**错误响应**：
- `401 Unauthorized`：未授权或Token无效
- `403 Forbidden`：权限不足
- `404 Not Found`：菜单不存在
- `500 Internal Server Error`：服务器错误

## 6. 检查菜单名称是否存在接口

检查指定的菜单名称是否已存在。

**请求**：
- 方法：`GET`
- 路径：`/system/menu/name-exists`
- 头部：需要包含`Authorization: Bearer {accessToken}`
- 查询参数：
  - `name`：菜单名称
  - `id`：菜单ID（可选，更新时使用）

**响应**：
```json
{
  "code": 200,           // 状态码
  "data": true,          // true表示存在，false表示不存在
  "message": "string"    // 消息
}
```

**错误响应**：
- `401 Unauthorized`：未授权或Token无效
- `500 Internal Server Error`：服务器错误

## 7. 检查菜单路径是否存在接口

检查指定的菜单路径是否已存在。

**请求**：
- 方法：`GET`
- 路径：`/system/menu/path-exists`
- 头部：需要包含`Authorization: Bearer {accessToken}`
- 查询参数：
  - `path`：菜单路径
  - `id`：菜单ID（可选，更新时使用）

**响应**：
```json
{
  "code": 200,           // 状态码
  "data": true,          // true表示存在，false表示不存在
  "message": "string"    // 消息
}
```

**错误响应**：
- `401 Unauthorized`：未授权或Token无效
- `500 Internal Server Error`：服务器错误
