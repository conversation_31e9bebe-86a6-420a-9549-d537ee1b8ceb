# 用户管理 API 文档（更新版）

本文档描述了用户管理相关的 API 接口，包括系统用户和租户用户的管理功能。系统采用统一接口设计，根据用户类型和租户信息自动选择相应的实现逻辑。

## 通用说明

### 基础路径

所有 API 请求的基础路径为：`/api`

### 认证方式

除了登录接口外，所有接口都需要在请求头中携带 JWT 令牌：

```http
Authorization: Bearer {token}
```

对于租户用户的接口，还需要在请求头中携带租户代码：

```http
X-Tenant-Code: {tenantCode}
```

### 响应格式

所有接口的响应格式统一为：

```json
{
  "code": 0,         // 状态码，0 表示成功，非 0 表示失败
  "data": {},        // 响应数据
  "message": "成功"   // 响应消息
}
```

## 1. 认证接口

### 1.1 用户登录

**请求方式**：POST

**请求路径**：`/auth/login`

**请求参数**：

| 参数名     | 类型   | 必填 | 描述                                 |
| ---------- | ------ | ---- | ------------------------------------ |
| username   | string | 是   | 用户名                               |
| password   | string | 是   | 密码                                 |
| tenantCode | string | 否   | 租户代码（租户用户必填，系统用户不填） |

**请求示例（系统用户）**：

```json
{
  "username": "admin",
  "password": "password123"
}
```

**请求示例（租户用户）**：

```json
{
  "username": "tenant_admin",
  "password": "password123",
  "tenantCode": "tenant1"
}
```

**响应示例**：

```json
{
  "code": 0,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "message": "登录成功"
}
```

## 2. 用户管理接口

用户管理接口根据当前登录用户的类型（系统用户或租户用户）和租户信息自动选择相应的实现逻辑。

### 2.1 获取用户信息

**请求方式**：GET

**请求路径**：`/users/info`

**响应示例（系统用户）**：

```json
{
  "code": 0,
  "data": {
    "id": 1,
    "username": "admin",
    "realName": "系统管理员",
    "email": "<EMAIL>",
    "avatar": "https://example.com/avatar.png",
    "status": 1,
    "lastLoginTime": "2025-05-07 14:30:45",
    "phoneNumber": "13800138000",
    "idCardNumber": "110101199001011234",
    "openid": "oWm8s5Cjh7uI9xJk3LpO",
    "adminRemark": "系统超级管理员",
    "roles": [
      {
        "id": "1",
        "name": "超级管理员",
        "code": "SUPER_ADMIN"
      }
    ]
  },
  "message": "获取成功"
}
```

**响应示例（租户用户）**：

```json
{
  "code": 0,
  "data": {
    "id": 1,
    "username": "tenant_admin",
    "realName": "租户管理员",
    "emailAddress": "<EMAIL>",
    "avatar": "https://example.com/avatar.png",
    "status": 1,
    "lastLoginTime": "2025-05-07 14:30:45",
    "tenantId": "tenant1",
    "phoneNumber": "13900139000",
    "idCardNumber": "110101199001011235",
    "openid": "oWm8s5Cjh7uI9xJk3LpQ",
    "adminRemark": "租户管理员账号",
    "roles": [
      {
        "id": "1",
        "name": "租户管理员",
        "code": "TENANT_ADMIN"
      }
    ]
  },
  "message": "获取成功"
}
```

### 2.2 获取用户列表

**请求方式**：GET

**请求路径**：`/users/list`

**请求参数**：

| 参数名       | 类型   | 必填 | 描述                 |
| ------------ | ------ | ---- | -------------------- |
| page         | number | 否   | 页码，默认 1         |
| pageSize     | number | 否   | 每页数量，默认 10    |
| username     | string | 否   | 用户名，模糊查询     |
| realName     | string | 否   | 真实姓名，模糊查询   |
| email        | string | 否   | 邮箱，模糊查询       |
| phoneNumber  | string | 否   | 手机号，模糊查询     |
| idCardNumber | string | 否   | 身份证号，模糊查询   |
| status       | number | 否   | 状态：0-禁用，1-启用 |
| startTime    | string | 否   | 开始时间，YYYY-MM-DD |
| endTime      | string | 否   | 结束时间，YYYY-MM-DD |

**响应示例**：

```json
{
  "code": 0,
  "data": {
    "items": [
      {
        "id": 1,
        "username": "admin",
        "realName": "系统管理员",
        "email": "<EMAIL>",
        "phoneNumber": "13800138000",
        "idCardNumber": "110101199001011234",
        "openid": "oWm8s5Cjh7uI9xJk3LpO",
        "adminRemark": "系统超级管理员",
        "status": 1,
        "createTime": "2025-05-01 10:00:00",
        "updateTime": "2025-05-07 14:30:45",
        "lastLoginTime": "2025-05-07 09:15:30"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  },
  "message": "获取成功"
}
```

### 2.3 创建用户

**请求方式**：POST

**请求路径**：`/users`

**请求参数**：

| 参数名       | 类型     | 必填 | 描述                 |
| ------------ | -------- | ---- | -------------------- |
| username     | string   | 是   | 用户名               |
| password     | string   | 是   | 密码                 |
| realName     | string   | 否   | 真实姓名             |
| email        | string   | 否   | 邮箱                 |
| phoneNumber  | string   | 否   | 手机号               |
| idCardNumber | string   | 否   | 身份证号             |
| openid       | string   | 否   | 微信openid           |
| unionid      | string   | 否   | 微信unionid          |
| adminRemark  | string   | 否   | 管理员备注           |
| status       | number   | 否   | 状态：0-禁用，1-启用 |
| roleIds      | string[] | 否   | 角色 ID 列表         |

**请求示例**：

```json
{
  "username": "user1",
  "password": "password123",
  "realName": "普通用户",
  "email": "<EMAIL>",
  "phoneNumber": "13800138001",
  "idCardNumber": "110101199001011236",
  "adminRemark": "测试用户",
  "status": 1,
  "roleIds": ["2"]
}
```

**响应示例**：

```json
{
  "code": 0,
  "data": {
    "id": 2,
    "username": "user1",
    "realName": "普通用户",
    "email": "<EMAIL>",
    "phoneNumber": "13800138001",
    "idCardNumber": "110101199001011236",
    "adminRemark": "测试用户",
    "status": 1,
    "createTime": "2025-05-07 15:30:00",
    "updateTime": "2025-05-07 15:30:00",
    "lastLoginTime": null
  },
  "message": "创建成功"
}
```

### 2.4 获取单个用户

**请求方式**：GET

**请求路径**：`/users/:id`

**路径参数**：

| 参数名 | 类型   | 必填 | 描述    |
| ------ | ------ | ---- | ------- |
| id     | number | 是   | 用户 ID |

**响应示例**：

```json
{
  "code": 0,
  "data": {
    "id": 2,
    "username": "user1",
    "realName": "普通用户",
    "email": "<EMAIL>",
    "phoneNumber": "13800138001",
    "idCardNumber": "110101199001011236",
    "openid": "oWm8s5Cjh7uI9xJk3LpP",
    "unionid": "oWm8s5Cjh7uI9xJk3LpP_UNIONID",
    "adminRemark": "测试用户",
    "status": 1,
    "createTime": "2025-05-07 15:30:00",
    "updateTime": "2025-05-07 15:30:00",
    "lastLoginTime": "2025-05-07 16:30:00",
    "roles": [
      {
        "id": "2",
        "name": "普通用户",
        "code": "USER"
      }
    ]
  },
  "message": "获取成功"
}
```

### 2.5 更新用户

**请求方式**：PATCH

**请求路径**：`/users/:id`

**路径参数**：

| 参数名 | 类型   | 必填 | 描述    |
| ------ | ------ | ---- | ------- |
| id     | number | 是   | 用户 ID |

**请求参数**：

| 参数名       | 类型     | 必填 | 描述                 |
| ------------ | -------- | ---- | -------------------- |
| realName     | string   | 否   | 真实姓名             |
| email        | string   | 否   | 邮箱                 |
| phoneNumber  | string   | 否   | 手机号               |
| idCardNumber | string   | 否   | 身份证号             |
| openid       | string   | 否   | 微信openid           |
| unionid      | string   | 否   | 微信unionid          |
| adminRemark  | string   | 否   | 管理员备注           |
| status       | number   | 否   | 状态：0-禁用，1-启用 |
| password     | string   | 否   | 密码（如需修改）     |
| roleIds      | string[] | 否   | 角色 ID 列表         |

**请求示例**：

```json
{
  "realName": "高级用户",
  "phoneNumber": "13800138002",
  "adminRemark": "已升级为高级用户",
  "status": 1,
  "roleIds": ["2", "3"]
}
```

**响应示例**：

```json
{
  "code": 0,
  "data": {
    "id": 2,
    "username": "user1",
    "realName": "高级用户",
    "email": "<EMAIL>",
    "phoneNumber": "13800138002",
    "idCardNumber": "110101199001011236",
    "adminRemark": "已升级为高级用户",
    "status": 1,
    "createTime": "2025-05-07 15:30:00",
    "updateTime": "2025-05-07 16:00:00",
    "lastLoginTime": "2025-05-07 16:30:00"
  },
  "message": "更新成功"
}
```

### 2.6 删除用户

**请求方式**：DELETE

**请求路径**：`/users/:id`

**路径参数**：

| 参数名 | 类型   | 必填 | 描述    |
| ------ | ------ | ---- | ------- |
| id     | number | 是   | 用户 ID |

**响应示例**：

```json
{
  "code": 0,
  "data": {
    "success": true
  },
  "message": "删除成功"
}
```
