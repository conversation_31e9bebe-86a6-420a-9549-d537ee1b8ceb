# 用户备注与标签 API 文档

本文档描述了用户备注、标签和扩展信息相关的 API 接口，作为用户管理功能的补充。

## 通用说明

### 基础路径

所有 API 请求的基础路径为：`/api`

### 认证方式

所有接口都需要在请求头中携带 JWT 令牌：

```http
Authorization: Bearer {token}
```

对于租户用户的接口，还需要在请求头中携带租户代码：

```http
X-Tenant-Code: {tenantCode}
```

### 响应格式

所有接口的响应格式统一为：

```json
{
  "code": 0,         // 状态码，0 表示成功，非 0 表示失败
  "data": {},        // 响应数据
  "message": "成功"   // 响应消息
}
```

## 1. 用户备注管理接口

用户备注功能允许用户对其他用户添加备注，以及管理员对用户添加备注。

### 1.1 获取用户备注列表

**请求方式**：GET

**请求路径**：`/users/remarks`

**请求参数**：

| 参数名      | 类型   | 必填 | 描述                 |
| ----------- | ------ | ---- | -------------------- |
| page        | number | 否   | 页码，默认 1         |
| pageSize    | number | 否   | 每页数量，默认 10    |
| userId      | number | 否   | 备注创建者ID         |
| targetUserId| number | 否   | 被备注的用户ID       |

**响应示例**：

```json
{
  "code": 0,
  "data": {
    "items": [
      {
        "id": 1,
        "userId": 1,
        "targetUserId": 2,
        "remark": "这是一个测试用户",
        "createTime": "2025-05-07 15:30:00",
        "updateTime": "2025-05-07 15:30:00"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  },
  "message": "获取成功"
}
```

### 1.2 创建用户备注

**请求方式**：POST

**请求路径**：`/users/remarks`

**请求参数**：

| 参数名      | 类型   | 必填 | 描述           |
| ----------- | ------ | ---- | -------------- |
| targetUserId| number | 是   | 被备注的用户ID |
| remark      | string | 是   | 备注内容       |

**请求示例**：

```json
{
  "targetUserId": 2,
  "remark": "这是一个测试用户"
}
```

**响应示例**：

```json
{
  "code": 0,
  "data": {
    "id": 1,
    "userId": 1,
    "targetUserId": 2,
    "remark": "这是一个测试用户",
    "createTime": "2025-05-07 15:30:00",
    "updateTime": "2025-05-07 15:30:00"
  },
  "message": "创建成功"
}
```

### 1.3 更新用户备注

**请求方式**：PUT

**请求路径**：`/users/remarks/:id`

**路径参数**：

| 参数名 | 类型   | 必填 | 描述    |
| ------ | ------ | ---- | ------- |
| id     | number | 是   | 备注 ID |

**请求参数**：

| 参数名 | 类型   | 必填 | 描述     |
| ------ | ------ | ---- | -------- |
| remark | string | 是   | 备注内容 |

**请求示例**：

```json
{
  "remark": "这是一个已更新的备注"
}
```

**响应示例**：

```json
{
  "code": 0,
  "data": {
    "id": 1,
    "userId": 1,
    "targetUserId": 2,
    "remark": "这是一个已更新的备注",
    "createTime": "2025-05-07 15:30:00",
    "updateTime": "2025-05-07 16:00:00"
  },
  "message": "更新成功"
}
```

### 1.4 删除用户备注

**请求方式**：DELETE

**请求路径**：`/users/remarks/:id`

**路径参数**：

| 参数名 | 类型   | 必填 | 描述    |
| ------ | ------ | ---- | ------- |
| id     | number | 是   | 备注 ID |

**响应示例**：

```json
{
  "code": 0,
  "data": {
    "success": true
  },
  "message": "删除成功"
}
```

## 2. 用户标签管理接口

用户标签功能允许管理员为用户分配标签，便于用户分类和管理。

### 2.1 获取用户标签列表

**请求方式**：GET

**请求路径**：`/users/tags`

**请求参数**：

| 参数名   | 类型   | 必填 | 描述              |
| -------- | ------ | ---- | ----------------- |
| page     | number | 否   | 页码，默认 1      |
| pageSize | number | 否   | 每页数量，默认 10 |
| name     | string | 否   | 标签名称，模糊查询|

**响应示例**：

```json
{
  "code": 0,
  "data": {
    "items": [
      {
        "id": 1,
        "name": "VIP用户",
        "color": "#ff0000",
        "createTime": "2025-05-07 15:30:00",
        "updateTime": "2025-05-07 15:30:00"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  },
  "message": "获取成功"
}
```

### 2.2 创建用户标签

**请求方式**：POST

**请求路径**：`/users/tags`

**请求参数**：

| 参数名 | 类型   | 必填 | 描述     |
| ------ | ------ | ---- | -------- |
| name   | string | 是   | 标签名称 |
| color  | string | 否   | 标签颜色 |

**请求示例**：

```json
{
  "name": "VIP用户",
  "color": "#ff0000"
}
```

**响应示例**：

```json
{
  "code": 0,
  "data": {
    "id": 1,
    "name": "VIP用户",
    "color": "#ff0000",
    "createTime": "2025-05-07 15:30:00",
    "updateTime": "2025-05-07 15:30:00"
  },
  "message": "创建成功"
}
```

### 2.3 更新用户标签

**请求方式**：PUT

**请求路径**：`/users/tags/:id`

**路径参数**：

| 参数名 | 类型   | 必填 | 描述    |
| ------ | ------ | ---- | ------- |
| id     | number | 是   | 标签 ID |

**请求参数**：

| 参数名 | 类型   | 必填 | 描述     |
| ------ | ------ | ---- | -------- |
| name   | string | 否   | 标签名称 |
| color  | string | 否   | 标签颜色 |

**请求示例**：

```json
{
  "name": "超级VIP用户",
  "color": "#ff5500"
}
```

**响应示例**：

```json
{
  "code": 0,
  "data": {
    "id": 1,
    "name": "超级VIP用户",
    "color": "#ff5500",
    "createTime": "2025-05-07 15:30:00",
    "updateTime": "2025-05-07 16:00:00"
  },
  "message": "更新成功"
}
```

### 2.4 删除用户标签

**请求方式**：DELETE

**请求路径**：`/users/tags/:id`

**路径参数**：

| 参数名 | 类型   | 必填 | 描述    |
| ------ | ------ | ---- | ------- |
| id     | number | 是   | 标签 ID |

**响应示例**：

```json
{
  "code": 0,
  "data": {
    "success": true
  },
  "message": "删除成功"
}
```

### 2.5 为用户分配标签

**请求方式**：POST

**请求路径**：`/users/assign-tags`

**请求参数**：

| 参数名 | 类型     | 必填 | 描述       |
| ------ | -------- | ---- | ---------- |
| userId | number   | 是   | 用户ID     |
| tagIds | number[] | 是   | 标签ID列表 |

**请求示例**：

```json
{
  "userId": 2,
  "tagIds": [1, 2, 3]
}
```

**响应示例**：

```json
{
  "code": 0,
  "data": {
    "success": true
  },
  "message": "分配成功"
}
```

### 2.6 获取用户的标签

**请求方式**：GET

**请求路径**：`/users/:userId/tags`

**路径参数**：

| 参数名  | 类型   | 必填 | 描述    |
| ------- | ------ | ---- | ------- |
| userId  | number | 是   | 用户 ID |

**响应示例**：

```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "name": "VIP用户",
      "color": "#ff0000"
    },
    {
      "id": 2,
      "name": "活跃用户",
      "color": "#00ff00"
    }
  ],
  "message": "获取成功"
}
```

## 3. 用户扩展信息接口

用户扩展信息功能允许存储用户的额外属性，无需频繁修改数据库结构。

### 3.1 获取用户扩展信息

**请求方式**：GET

**请求路径**：`/users/:userId/extension`

**路径参数**：

| 参数名  | 类型   | 必填 | 描述    |
| ------- | ------ | ---- | ------- |
| userId  | number | 是   | 用户 ID |

**响应示例**：

```json
{
  "code": 0,
  "data": {
    "id": 1,
    "userId": 2,
    "extendedInfo": {
      "interests": ["阅读", "旅游", "摄影"],
      "education": "本科",
      "company": "示例公司",
      "position": "工程师"
    },
    "createTime": "2025-05-07 15:30:00",
    "updateTime": "2025-05-07 15:30:00"
  },
  "message": "获取成功"
}
```

### 3.2 创建或更新用户扩展信息

**请求方式**：PUT

**请求路径**：`/users/:userId/extension`

**路径参数**：

| 参数名  | 类型   | 必填 | 描述    |
| ------- | ------ | ---- | ------- |
| userId  | number | 是   | 用户 ID |

**请求参数**：

| 参数名       | 类型   | 必填 | 描述       |
| ------------ | ------ | ---- | ---------- |
| extendedInfo | object | 是   | 扩展信息   |

**请求示例**：

```json
{
  "extendedInfo": {
    "interests": ["阅读", "旅游", "摄影"],
    "education": "本科",
    "company": "示例公司",
    "position": "工程师"
  }
}
```

**响应示例**：

```json
{
  "code": 0,
  "data": {
    "id": 1,
    "userId": 2,
    "extendedInfo": {
      "interests": ["阅读", "旅游", "摄影"],
      "education": "本科",
      "company": "示例公司",
      "position": "工程师"
    },
    "createTime": "2025-05-07 15:30:00",
    "updateTime": "2025-05-07 16:00:00"
  },
  "message": "更新成功"
}
```
