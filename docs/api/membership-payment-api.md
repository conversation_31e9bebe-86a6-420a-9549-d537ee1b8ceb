# 会员模块和支付模块接口文档

## 1. 会员模块接口

### 1.1 租户-获取会员计划列表

**接口描述**：租户获取自己创建的所有会员计划

**请求方法**：GET

**请求路径**：`/api/tenant/membership/plans/list`

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| status | string | 否 | 计划状态筛选，可选值：active, inactive, all，默认为active |
| page | number | 否 | 页码，默认为1 |
| pageSize | number | 否 | 每页数量，默认为10 |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "items": [
      {
        "id": 1,
        "tenantId": 1,
        "code": "basic",
        "name": "基础版",
        "description": "适合个人用户的基础功能套餐",
        "price": 99.00,
        "originalPrice": 199.00,
        "billingCycle": "monthly",
        "features": {
          "ai.ppt": {
            "enabled": true,
            "quota": 10,
            "templates": ["basic"]
          },
          "ai.document": {
            "enabled": true,
            "quota": 20
          }
        },
        "isActive": true,
        "sortOrder": 1,
        "createTime": "2025-06-01 12:00:00",
        "updateTime": "2025-06-01 12:00:00"
      }
    ],
    "total": 2,
    "page": 1,
    "pageSize": 10
  },
  "message": "获取成功"
}
```

### 1.2 租户-创建会员计划

**接口描述**：租户创建新的会员计划

**请求方法**：POST

**请求路径**：`/api/tenant/membership/plans`

**请求参数**：

```json
{
  "code": "enterprise",
  "name": "企业版",
  "description": "适合企业用户的高级套餐",
  "price": 999.00,
  "originalPrice": 1299.00,
  "billingCycle": "monthly",
  "features": {
    "ai.ppt": {
      "enabled": true,
      "quota": 100,
      "templates": ["basic", "professional", "enterprise"]
    },
    "ai.document": {
      "enabled": true,
      "quota": 200
    }
  },
  "isActive": true,
  "sortOrder": 3
}
```

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "id": 3,
    "tenantId": 1,
    "code": "enterprise",
    "name": "企业版",
    "description": "适合企业用户的高级套餐",
    "price": 999.00,
    "originalPrice": 1299.00,
    "billingCycle": "monthly",
    "features": {
      "ai.ppt": {
        "enabled": true,
        "quota": 100,
        "templates": ["basic", "professional", "enterprise"]
      },
      "ai.document": {
        "enabled": true,
        "quota": 200
      }
    },
    "isActive": true,
    "sortOrder": 3,
    "createTime": "2025-06-10 10:00:00",
    "updateTime": "2025-06-10 10:00:00"
  },
  "message": "创建成功"
}
```

### 1.3 租户-更新会员计划

**接口描述**：租户更新现有会员计划

**请求方法**：PUT

**请求路径**：`/api/tenant/membership/plans/{id}`

**请求参数**：

```json
{
  "name": "企业版Plus",
  "description": "适合大型企业的高级套餐",
  "price": 1299.00,
  "originalPrice": 1599.00,
  "features": {
    "ai.ppt": {
      "enabled": true,
      "quota": 200,
      "templates": ["basic", "professional", "enterprise", "custom"]
    },
    "ai.document": {
      "enabled": true,
      "quota": 300
    }
  },
  "isActive": true
}
```

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "id": 3,
    "tenantId": 1,
    "code": "enterprise",
    "name": "企业版Plus",
    "description": "适合大型企业的高级套餐",
    "price": 1299.00,
    "originalPrice": 1599.00,
    "billingCycle": "monthly",
    "features": {
      "ai.ppt": {
        "enabled": true,
        "quota": 200,
        "templates": ["basic", "professional", "enterprise", "custom"]
      },
      "ai.document": {
        "enabled": true,
        "quota": 300
      }
    },
    "isActive": true,
    "sortOrder": 3,
    "createTime": "2025-06-10 10:00:00",
    "updateTime": "2025-06-10 11:00:00"
  },
  "message": "更新成功"
}
```

### 1.4 租户-删除会员计划

**接口描述**：租户删除会员计划（软删除）

**请求方法**：DELETE

**请求路径**：`/api/tenant/membership/plans/{id}`

**请求参数**：无

**响应数据**：

```json
{
  "code": 0,
  "data": null,
  "message": "删除成功"
}
```

### 1.5 租户-获取用户会员列表

**接口描述**：租户获取其用户的会员订阅信息

**请求方法**：GET

**请求路径**：`/api/tenant/membership/users/list`

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| userId | number | 否 | 用户ID |
| planId | number | 否 | 会员计划ID |
| status | string | 否 | 会员状态，可选值：active, expired, cancelled |
| page | number | 否 | 页码，默认为1 |
| pageSize | number | 否 | 每页数量，默认为10 |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "items": [
      {
        "id": 1,
        "userId": 101,
        "username": "张三",
        "planId": 2,
        "planName": "专业版",
        "startDate": "2025-06-01T12:00:00.000Z",
        "endDate": "2025-07-01T12:00:00.000Z",
        "status": "active",
        "autoRenew": true,
        "createTime": "2025-06-01 12:00:00",
        "updateTime": "2025-06-01 12:00:00"
      }
    ],
    "total": 15,
    "page": 1,
    "pageSize": 10
  },
  "message": "获取成功"
}
```

### 1.6 租户-手动设置用户会员

**接口描述**：租户手动为用户设置会员（管理操作）

**请求方法**：POST

**请求路径**：`/api/tenant/membership/users/set`

**请求参数**：

```json
{
  "userId": 101,
  "planId": 2,
  "duration": 1,
  "billingCycle": "monthly",
  "status": "active",
  "autoRenew": false
}
```

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "id": 1,
    "userId": 101,
    "planId": 2,
    "startDate": "2025-06-10T10:00:00.000Z",
    "endDate": "2025-07-10T10:00:00.000Z",
    "status": "active",
    "autoRenew": false,
    "createTime": "2025-06-10 10:00:00",
    "updateTime": "2025-06-10 10:00:00"
  },
  "message": "设置成功"
}
```

### 1.7 租户-获取会员使用统计

**接口描述**：租户获取会员功能使用统计数据

**请求方法**：GET

**请求路径**：`/api/tenant/membership/statistics`

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| startTime | string | 否 | 开始时间，格式：YYYY-MM-DD |
| endTime | string | 否 | 结束时间，格式：YYYY-MM-DD |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "totalUsers": 150,
    "activeMembers": 120,
    "membershipRevenue": 35000,
    "planDistribution": [
      {
        "planId": 1,
        "planName": "基础版",
        "userCount": 50,
        "percentage": 41.67
      },
      {
        "planId": 2,
        "planName": "专业版",
        "userCount": 45,
        "percentage": 37.5
      },
      {
        "planId": 3,
        "planName": "企业版",
        "userCount": 25,
        "percentage": 20.83
      }
    ],
    "featureUsage": [
      {
        "featureCode": "ai.ppt",
        "totalUsage": 1250,
        "userCount": 95
      },
      {
        "featureCode": "ai.document",
        "totalUsage": 2800,
        "userCount": 110
      }
    ]
  },
  "message": "获取成功"
}
```

### 1.8 用户-获取可用会员计划

**接口描述**：用户获取租户提供的可用会员计划

**请求方法**：GET

**请求路径**：`/api/membership/plans/available`

**请求参数**：无

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "items": [
      {
        "id": 1,
        "code": "basic",
        "name": "基础版",
        "description": "适合个人用户的基础功能套餐",
        "price": 99.00,
        "originalPrice": 199.00,
        "billingCycle": "monthly",
        "features": [
          {
            "name": "AI PPT生成",
            "description": "每月可生成10个AI PPT",
            "highlight": true
          },
          {
            "name": "AI文档生成",
            "description": "每月可生成20个AI文档",
            "highlight": false
          }
        ],
        "isActive": true,
        "sortOrder": 1
      },
      {
        "id": 2,
        "code": "pro",
        "name": "专业版",
        "description": "适合专业用户的全功能套餐",
        "price": 299.00,
        "originalPrice": 399.00,
        "billingCycle": "monthly",
        "features": [
          {
            "name": "AI PPT生成",
            "description": "每月可生成50个AI PPT",
            "highlight": true
          },
          {
            "name": "AI文档生成",
            "description": "每月可生成100个AI文档",
            "highlight": true
          },
          {
            "name": "专业模板",
            "description": "使用专业设计模板",
            "highlight": true
          }
        ],
        "isActive": true,
        "sortOrder": 2
      }
    ]
  },
  "message": "获取成功"
}
```

### 1.9 用户-获取当前会员信息

**接口描述**：用户获取自己的会员信息

**请求方法**：GET

**请求路径**：`/api/membership/info`

**请求参数**：无

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "membership": {
      "id": 1,
      "userId": 101,
      "planId": 2,
      "plan": {
        "id": 2,
        "code": "pro",
        "name": "专业版",
        "description": "适合专业用户的全功能套餐",
        "price": 299.00,
        "billingCycle": "monthly"
      },
      "startDate": "2025-06-01T12:00:00.000Z",
      "endDate": "2025-07-01T12:00:00.000Z",
      "status": "active",
      "autoRenew": true,
      "createTime": "2025-06-01 12:00:00",
      "updateTime": "2025-06-01 12:00:00"
    },
    "features": {
      "ai.ppt": {
        "enabled": true,
        "quota": 50,
        "usedQuota": 10,
        "remainingQuota": 40
      },
      "ai.document": {
        "enabled": true,
        "quota": 100,
        "usedQuota": 25,
        "remainingQuota": 75
      }
    }
  },
  "message": "获取成功"
}
```

### 1.10 用户-订阅会员计划

**接口描述**：用户订阅会员计划

**请求方法**：POST

**请求路径**：`/api/membership/subscribe`

**请求参数**：

```json
{
  "planId": 2,
  "duration": 1,
  "billingCycle": "monthly",
  "autoRenew": true,
  "paymentMethod": "alipay"
}
```

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "orderNo": "M202506010001",
    "amount": 299.00,
    "paymentUrl": "https://payment-gateway.com/pay?orderNo=M202506010001",
    "qrCode": "base64-encoded-qr-code-image"
  },
  "message": "订阅成功，请完成支付"
}
```

### 1.11 用户-获取会员订单列表

**接口描述**：用户获取自己的会员订单列表

**请求方法**：GET

**请求路径**：`/api/membership/orders/list`

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| status | string | 否 | 订单状态，可选值：pending, success, failed, cancelled |
| startTime | string | 否 | 开始时间，格式：YYYY-MM-DD |
| endTime | string | 否 | 结束时间，格式：YYYY-MM-DD |
| page | number | 否 | 页码，默认为1 |
| pageSize | number | 否 | 每页数量，默认为10 |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "items": [
      {
        "id": 1,
        "orderNo": "M202506010001",
        "planId": 2,
        "planName": "专业版",
        "amount": 299.00,
        "status": "success",
        "paymentMethod": "alipay",
        "duration": 1,
        "billingCycle": "monthly",
        "createTime": "2025-06-01 12:00:00",
        "paymentTime": "2025-06-01 12:05:30"
      },
      {
        "id": 2,
        "orderNo": "M202506020001",
        "planId": 3,
        "planName": "企业版",
        "amount": 999.00,
        "status": "pending",
        "paymentMethod": "wechat",
        "duration": 1,
        "billingCycle": "monthly",
        "createTime": "2025-06-02 10:00:00",
        "paymentTime": null
      }
    ],
    "total": 5,
    "page": 1,
    "pageSize": 10
  },
  "message": "获取成功"
}
```

### 1.12 租户-生成会员卡密

**接口描述**：租户生成会员卡密（激活码）

**请求方法**：POST

**请求路径**：`/api/tenant/membership/card-keys/generate`

**请求参数**：

```json
{
  "planId": 2,
  "count": 10,
  "duration": 1,
  "billingCycle": "monthly",
  "expireDate": "2025-12-31",
  "prefix": "PRO",
  "remark": "618促销活动"
}
```

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| planId | number | 是 | 会员计划ID |
| count | number | 是 | 生成数量，1-100之间 |
| duration | number | 是 | 会员时长 |
| billingCycle | string | 是 | 计费周期，可选值：monthly, quarterly, yearly |
| expireDate | string | 否 | 卡密过期日期，格式：YYYY-MM-DD |
| prefix | string | 否 | 卡密前缀，3-5个字符 |
| remark | string | 否 | 备注信息 |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "batchId": "B202506010001",
    "planId": 2,
    "planName": "专业版",
    "count": 10,
    "duration": 1,
    "billingCycle": "monthly",
    "expireDate": "2025-12-31",
    "cardKeys": [
      "PRO-ABCD-EFGH-IJKL",
      "PRO-MNOP-QRST-UVWX",
      "PRO-1234-5678-90AB",
      "..."
    ],
    "createTime": "2025-06-01 12:00:00"
  },
  "message": "生成成功"
}
```

### 1.13 租户-获取卡密列表

**接口描述**：租户获取生成的卡密列表

**请求方法**：GET

**请求路径**：`/api/tenant/membership/card-keys/list`

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| batchId | string | 否 | 批次ID |
| planId | number | 否 | 会员计划ID |
| status | string | 否 | 卡密状态，可选值：unused, used, expired |
| startTime | string | 否 | 开始时间，格式：YYYY-MM-DD |
| endTime | string | 否 | 结束时间，格式：YYYY-MM-DD |
| page | number | 否 | 页码，默认为1 |
| pageSize | number | 否 | 每页数量，默认为10 |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "items": [
      {
        "id": 1,
        "cardKey": "PRO-ABCD-EFGH-IJKL",
        "batchId": "B202506010001",
        "planId": 2,
        "planName": "专业版",
        "duration": 1,
        "billingCycle": "monthly",
        "status": "unused",
        "expireDate": "2025-12-31",
        "usedBy": null,
        "usedTime": null,
        "createTime": "2025-06-01 12:00:00"
      },
      {
        "id": 2,
        "cardKey": "PRO-MNOP-QRST-UVWX",
        "batchId": "B202506010001",
        "planId": 2,
        "planName": "专业版",
        "duration": 1,
        "billingCycle": "monthly",
        "status": "used",
        "expireDate": "2025-12-31",
        "usedBy": "user123",
        "usedTime": "2025-06-05 15:30:00",
        "createTime": "2025-06-01 12:00:00"
      }
    ],
    "total": 10,
    "page": 1,
    "pageSize": 10
  },
  "message": "获取成功"
}
```

### 1.14 用户-使用卡密激活会员

**接口描述**：用户使用卡密激活会员

**请求方法**：POST

**请求路径**：`/api/membership/activate`

**请求参数**：

```json
{
  "cardKey": "PRO-ABCD-EFGH-IJKL"
}
```

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| cardKey | string | 是 | 卡密（激活码） |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "planId": 2,
    "planName": "专业版",
    "duration": 1,
    "billingCycle": "monthly",
    "startDate": "2025-06-10T10:00:00.000Z",
    "endDate": "2025-07-10T10:00:00.000Z",
    "status": "active"
  },
  "message": "激活成功"
}
```

### 1.15 租户-创建会员权益

**接口描述**：租户创建会员权益项目

**请求方法**：POST

**请求路径**：`/api/tenant/membership/benefits`

**请求参数**：

```json
{
  "code": "ai_ppt_pro",
  "name": "AI PPT专业版",
  "description": "使用AI生成专业级PPT",
  "category": "ai",
  "icon": "ppt-icon",
  "details": "可使用所有专业模板，支持高级编辑功能",
  "sortOrder": 1,
  "isActive": true,
  "metadata": {
    "featureCode": "ai.ppt",
    "templates": ["basic", "professional"]
  }
}
```

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| code | string | 是 | 权益代码，唯一标识 |
| name | string | 是 | 权益名称 |
| description | string | 是 | 权益简短描述 |
| category | string | 是 | 权益分类，如ai, content, service |
| icon | string | 否 | 权益图标 |
| details | string | 否 | 权益详细说明 |
| sortOrder | number | 否 | 排序顺序 |
| isActive | boolean | 否 | 是否激活，默认true |
| metadata | object | 否 | 权益元数据，可包含任意附加信息 |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "id": 1,
    "tenantId": 1,
    "code": "ai_ppt_pro",
    "name": "AI PPT专业版",
    "description": "使用AI生成专业级PPT",
    "category": "ai",
    "icon": "ppt-icon",
    "details": "可使用所有专业模板，支持高级编辑功能",
    "sortOrder": 1,
    "isActive": true,
    "metadata": {
      "featureCode": "ai.ppt",
      "templates": ["basic", "professional"]
    },
    "createTime": "2025-06-10 10:00:00",
    "updateTime": "2025-06-10 10:00:00"
  },
  "message": "创建成功"
}
```

### 1.16 租户-获取会员权益列表

**接口描述**：租户获取会员权益列表

**请求方法**：GET

**请求路径**：`/api/tenant/membership/benefits/list`

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| category | string | 否 | 权益分类 |
| isActive | boolean | 否 | 是否激活 |
| page | number | 否 | 页码，默认为1 |
| pageSize | number | 否 | 每页数量，默认为10 |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "items": [
      {
        "id": 1,
        "tenantId": 1,
        "code": "ai_ppt_pro",
        "name": "AI PPT专业版",
        "description": "使用AI生成专业级PPT",
        "category": "ai",
        "icon": "ppt-icon",
        "details": "可使用所有专业模板，支持高级编辑功能",
        "sortOrder": 1,
        "isActive": true,
        "metadata": {
          "featureCode": "ai.ppt",
          "templates": ["basic", "professional"]
        },
        "createTime": "2025-06-10 10:00:00",
        "updateTime": "2025-06-10 10:00:00"
      },
      {
        "id": 2,
        "tenantId": 1,
        "code": "ai_document_pro",
        "name": "AI文档专业版",
        "description": "使用AI生成专业级文档",
        "category": "ai",
        "icon": "doc-icon",
        "details": "支持多种文档类型，包含高级排版功能",
        "sortOrder": 2,
        "isActive": true,
        "metadata": {
          "featureCode": "ai.document",
          "maxLength": 5000
        },
        "createTime": "2025-06-10 11:00:00",
        "updateTime": "2025-06-10 11:00:00"
      }
    ],
    "total": 5,
    "page": 1,
    "pageSize": 10
  },
  "message": "获取成功"
}
```

### 1.17 租户-更新会员权益

**接口描述**：租户更新会员权益

**请求方法**：PUT

**请求路径**：`/api/tenant/membership/benefits/{id}`

**请求参数**：

```json
{
  "name": "AI PPT高级版",
  "description": "使用AI生成高级定制PPT",
  "details": "可使用所有专业模板，支持高级编辑和自定义功能",
  "isActive": true,
  "metadata": {
    "featureCode": "ai.ppt",
    "templates": ["basic", "professional", "custom"]
  }
}
```

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "id": 1,
    "tenantId": 1,
    "code": "ai_ppt_pro",
    "name": "AI PPT高级版",
    "description": "使用AI生成高级定制PPT",
    "category": "ai",
    "icon": "ppt-icon",
    "details": "可使用所有专业模板，支持高级编辑和自定义功能",
    "sortOrder": 1,
    "isActive": true,
    "metadata": {
      "featureCode": "ai.ppt",
      "templates": ["basic", "professional", "custom"]
    },
    "createTime": "2025-06-10 10:00:00",
    "updateTime": "2025-06-10 12:00:00"
  },
  "message": "更新成功"
}
```

### 1.18 租户-关联会员权益到会员计划

**接口描述**：租户将会员权益关联到会员计划

**请求方法**：POST

**请求路径**：`/api/tenant/membership/plans/{planId}/benefits`

**请求参数**：

```json
{
  "benefitIds": [1, 2, 3],
  "quotas": {
    "1": 50,
    "2": 100
  }
}
```

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| benefitIds | array | 是 | 权益ID数组 |
| quotas | object | 否 | 权益配额，key为权益ID，value为配额值 |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "planId": 2,
    "planName": "专业版",
    "benefits": [
      {
        "id": 1,
        "code": "ai_ppt_pro",
        "name": "AI PPT高级版",
        "quota": 50
      },
      {
        "id": 2,
        "code": "ai_document_pro",
        "name": "AI文档专业版",
        "quota": 100
      },
      {
        "id": 3,
        "code": "priority_support",
        "name": "优先客服支持",
        "quota": null
      }
    ]
  },
  "message": "关联成功"
}
```

### 1.19 用户-获取会员权益列表

**接口描述**：用户获取当前可用的会员权益列表

**请求方法**：GET

**请求路径**：`/api/membership/benefits`

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| category | string | 否 | 权益分类 |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "items": [
      {
        "id": 1,
        "code": "ai_ppt_pro",
        "name": "AI PPT高级版",
        "description": "使用AI生成高级定制PPT",
        "category": "ai",
        "icon": "ppt-icon",
        "details": "可使用所有专业模板，支持高级编辑和自定义功能",
        "quota": 50,
        "usedQuota": 10,
        "remainingQuota": 40,
        "isActive": true
      },
      {
        "id": 2,
        "code": "ai_document_pro",
        "name": "AI文档专业版",
        "description": "使用AI生成专业级文档",
        "category": "ai",
        "icon": "doc-icon",
        "details": "支持多种文档类型，包含高级排版功能",
        "quota": 100,
        "usedQuota": 25,
        "remainingQuota": 75,
        "isActive": true
      },
      {
        "id": 3,
        "code": "priority_support",
        "name": "优先客服支持",
        "description": "获得优先的客服响应",
        "category": "service",
        "icon": "support-icon",
        "details": "工作时间内30分钟响应保证",
        "quota": null,
        "usedQuota": null,
        "remainingQuota": null,
        "isActive": true
      }
    ]
  },
  "message": "获取成功"
}
```

### 1.20 租户-创建虚拟币类型

**接口描述**：租户创建虚拟币类型

**请求方法**：POST

**请求路径**：`/api/tenant/virtual-currency/types`

**请求参数**：

```json
{
  "code": "ai_coin",
  "name": "AI币",
  "description": "用于AI功能消费的虚拟币",
  "icon": "coin-icon",
  "exchangeRate": 1.0,
  "isActive": true,
  "metadata": {
    "color": "#FFD700",
    "displayFormat": "{value}个"
  }
}
```

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| code | string | 是 | 虚拟币代码，唯一标识 |
| name | string | 是 | 虚拟币名称 |
| description | string | 是 | 虚拟币描述 |
| icon | string | 否 | 虚拟币图标 |
| exchangeRate | number | 是 | 兑换比率（1单位虚拟币等于多少人民币） |
| isActive | boolean | 否 | 是否激活，默认true |
| metadata | object | 否 | 元数据，可包含任意附加信息 |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "id": 1,
    "tenantId": 1,
    "code": "ai_coin",
    "name": "AI币",
    "description": "用于AI功能消费的虚拟币",
    "icon": "coin-icon",
    "exchangeRate": 1.0,
    "isActive": true,
    "metadata": {
      "color": "#FFD700",
      "displayFormat": "{value}个"
    },
    "createTime": "2025-06-10 10:00:00",
    "updateTime": "2025-06-10 10:00:00"
  },
  "message": "创建成功"
}
```

### 1.21 租户-获取虚拟币类型列表

**接口描述**：租户获取虚拟币类型列表

**请求方法**：GET

**请求路径**：`/api/tenant/virtual-currency/types/list`

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| isActive | boolean | 否 | 是否激活 |
| page | number | 否 | 页码，默认为1 |
| pageSize | number | 否 | 每页数量，默认为10 |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "items": [
      {
        "id": 1,
        "tenantId": 1,
        "code": "ai_coin",
        "name": "AI币",
        "description": "用于AI功能消费的虚拟币",
        "icon": "coin-icon",
        "exchangeRate": 1.0,
        "isActive": true,
        "metadata": {
          "color": "#FFD700",
          "displayFormat": "{value}个"
        },
        "createTime": "2025-06-10 10:00:00",
        "updateTime": "2025-06-10 10:00:00"
      },
      {
        "id": 2,
        "tenantId": 1,
        "code": "credit_point",
        "name": "积分",
        "description": "通用积分，可用于兑换商品",
        "icon": "point-icon",
        "exchangeRate": 0.1,
        "isActive": true,
        "metadata": {
          "color": "#1E90FF",
          "displayFormat": "{value}分"
        },
        "createTime": "2025-06-10 11:00:00",
        "updateTime": "2025-06-10 11:00:00"
      }
    ],
    "total": 2,
    "page": 1,
    "pageSize": 10
  },
  "message": "获取成功"
}
```

### 1.22 租户-设置功能虚拟币消耗

**接口描述**：租户设置功能消耗的虚拟币数量

**请求方法**：POST

**请求路径**：`/api/tenant/features/{featureCode}/currency-cost`

**请求参数**：

```json
{
  "currencyCode": "ai_coin",
  "cost": 10,
  "memberDiscounts": {
    "basic": 0.9,
    "pro": 0.7,
    "enterprise": 0.5
  },
  "bulkDiscounts": [
    {
      "quantity": 10,
      "discount": 0.9
    },
    {
      "quantity": 50,
      "discount": 0.8
    },
    {
      "quantity": 100,
      "discount": 0.7
    }
  ],
  "isActive": true
}
```

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| currencyCode | string | 是 | 虚拟币代码 |
| cost | number | 是 | 基础消耗量 |
| memberDiscounts | object | 否 | 会员折扣，key为会员计划代码，value为折扣率 |
| bulkDiscounts | array | 否 | 批量购买折扣 |
| isActive | boolean | 否 | 是否激活，默认true |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "featureCode": "ai.ppt",
    "currencyCode": "ai_coin",
    "cost": 10,
    "memberDiscounts": {
      "basic": 0.9,
      "pro": 0.7,
      "enterprise": 0.5
    },
    "bulkDiscounts": [
      {
        "quantity": 10,
        "discount": 0.9
      },
      {
        "quantity": 50,
        "discount": 0.8
      },
      {
        "quantity": 100,
        "discount": 0.7
      }
    ],
    "isActive": true,
    "createTime": "2025-06-10 10:00:00",
    "updateTime": "2025-06-10 10:00:00"
  },
  "message": "设置成功"
}
```

### 1.23 租户-设置会员计划虚拟币赠送

**接口描述**：租户设置会员计划赠送的虚拟币数量

**请求方法**：POST

**请求路径**：`/api/tenant/membership/plans/{planId}/currency-gift`

**请求参数**：

```json
{
  "gifts": [
    {
      "currencyCode": "ai_coin",
      "amount": 100,
      "period": "monthly"
    },
    {
      "currencyCode": "credit_point",
      "amount": 500,
      "period": "once"
    }
  ]
}
```

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| gifts | array | 是 | 赠送的虚拟币列表 |
| currencyCode | string | 是 | 虚拟币代码 |
| amount | number | 是 | 赠送数量 |
| period | string | 是 | 赠送周期，可选值：once, daily, weekly, monthly, yearly |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "planId": 2,
    "planName": "专业版",
    "gifts": [
      {
        "currencyCode": "ai_coin",
        "amount": 100,
        "period": "monthly"
      },
      {
        "currencyCode": "credit_point",
        "amount": 500,
        "period": "once"
      }
    ],
    "createTime": "2025-06-10 10:00:00",
    "updateTime": "2025-06-10 10:00:00"
  },
  "message": "设置成功"
}
```

### 1.24 用户-查询虚拟币余额

**接口描述**：用户查询自己的虚拟币余额

**请求方法**：GET

**请求路径**：`/api/virtual-currency/balance`

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| currencyCode | string | 否 | 虚拟币代码，不传则返回所有 |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "balances": [
      {
        "currencyCode": "ai_coin",
        "currencyName": "AI币",
        "balance": 85.5,
        "icon": "coin-icon",
        "metadata": {
          "color": "#FFD700",
          "displayFormat": "{value}个"
        }
      },
      {
        "currencyCode": "credit_point",
        "currencyName": "积分",
        "balance": 1250,
        "icon": "point-icon",
        "metadata": {
          "color": "#1E90FF",
          "displayFormat": "{value}分"
        }
      }
    ]
  },
  "message": "获取成功"
}
```

### 1.25 用户-购买虚拟币

**接口描述**：用户购买虚拟币

**请求方法**：POST

**请求路径**：`/api/virtual-currency/purchase`

**请求参数**：

```json
{
  "currencyCode": "ai_coin",
  "amount": 100,
  "paymentMethod": "alipay"
}
```

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| currencyCode | string | 是 | 虚拟币代码 |
| amount | number | 是 | 购买数量 |
| paymentMethod | string | 是 | 支付方式，可选值：alipay, wechat, creditcard |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "orderNo": "C202506010001",
    "currencyCode": "ai_coin",
    "amount": 100,
    "price": 100.00,
    "paymentUrl": "https://payment-gateway.com/pay?orderNo=C202506010001",
    "qrCode": "base64-encoded-qr-code-image"
  },
  "message": "创建订单成功，请完成支付"
}
```

### 1.26 用户-虚拟币交易记录

**接口描述**：用户查询虚拟币交易记录

**请求方法**：GET

**请求路径**：`/api/virtual-currency/transactions/list`

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| currencyCode | string | 否 | 虚拟币代码 |
| type | string | 否 | 交易类型，可选值：purchase, consume, gift, refund |
| startTime | string | 否 | 开始时间，格式：YYYY-MM-DD |
| endTime | string | 否 | 结束时间，格式：YYYY-MM-DD |
| page | number | 否 | 页码，默认为1 |
| pageSize | number | 否 | 每页数量，默认为10 |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "items": [
      {
        "id": 1,
        "currencyCode": "ai_coin",
        "currencyName": "AI币",
        "amount": 100,
        "balance": 100,
        "type": "purchase",
        "description": "购买AI币",
        "metadata": {
          "orderNo": "C202506010001"
        },
        "createTime": "2025-06-01 12:00:00"
      },
      {
        "id": 2,
        "currencyCode": "ai_coin",
        "currencyName": "AI币",
        "amount": -10,
        "balance": 90,
        "type": "consume",
        "description": "使用AI PPT生成服务",
        "metadata": {
          "featureCode": "ai.ppt",
          "featureName": "AI PPT生成"
        },
        "createTime": "2025-06-01 14:00:00"
      },
      {
        "id": 3,
        "currencyCode": "ai_coin",
        "currencyName": "AI币",
        "amount": 50,
        "balance": 140,
        "type": "gift",
        "description": "会员月度赠送",
        "metadata": {
          "planId": 2,
          "planName": "专业版"
        },
        "createTime": "2025-06-02 00:00:00"
      }
    ],
    "total": 10,
    "page": 1,
    "pageSize": 10
  },
  "message": "获取成功"
}
```

### 1.27 租户-设置功能单次价格

**接口描述**：租户设置功能的单次使用价格

**请求方法**：POST

**请求路径**：`/api/tenant/features/{featureCode}/price`

**请求参数**：

```json
{
  "price": 15.00,
  "memberDiscounts": {
    "basic": 0.9,
    "pro": 0.7,
    "enterprise": 0.5
  },
  "bulkDiscounts": [
    {
      "quantity": 10,
      "discount": 0.9
    },
    {
      "quantity": 50,
      "discount": 0.8
    },
    {
      "quantity": 100,
      "discount": 0.7
    }
  ],
  "isActive": true
}
```

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| price | number | 是 | 基础单次价格 |
| memberDiscounts | object | 否 | 会员折扣，key为会员计划代码，value为折扣率 |
| bulkDiscounts | array | 否 | 批量购买折扣 |
| isActive | boolean | 否 | 是否激活，默认true |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "featureCode": "ai.ppt",
    "price": 15.00,
    "memberDiscounts": {
      "basic": 0.9,
      "pro": 0.7,
      "enterprise": 0.5
    },
    "bulkDiscounts": [
      {
        "quantity": 10,
        "discount": 0.9
      },
      {
        "quantity": 50,
        "discount": 0.8
      },
      {
        "quantity": 100,
        "discount": 0.7
      }
    ],
    "isActive": true,
    "createTime": "2025-06-10 10:00:00",
    "updateTime": "2025-06-10 10:00:00"
  },
  "message": "设置成功"
}
```

### 1.28 用户-功能单次购买

**接口描述**：用户购买功能的单次使用权

**请求方法**：POST

**请求路径**：`/api/features/{featureCode}/purchase`

**请求参数**：

```json
{
  "quantity": 10,
  "paymentMethod": "alipay",
  "useCurrency": false
}
```

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| quantity | number | 是 | 购买数量 |
| paymentMethod | string | 否 | 支付方式，可选值：alipay, wechat, creditcard |
| useCurrency | boolean | 否 | 是否使用虚拟币支付，默认false |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "orderNo": "F202506010001",
    "featureCode": "ai.ppt",
    "featureName": "AI PPT生成",
    "quantity": 10,
    "unitPrice": 13.50,
    "totalPrice": 135.00,
    "discount": 0.9,
    "paymentUrl": "https://payment-gateway.com/pay?orderNo=F202506010001",
    "qrCode": "base64-encoded-qr-code-image"
  },
  "message": "创建订单成功，请完成支付"
}
```

### 1.29 用户-查询功能可用次数

**接口描述**：用户查询功能的可用次数

**请求方法**：GET

**请求路径**：`/api/features/{featureCode}/available-uses`

**请求参数**：无

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "featureCode": "ai.ppt",
    "featureName": "AI PPT生成",
    "availableUses": {
      "membership": {
        "quota": 50,
        "used": 10,
        "remaining": 40,
        "planName": "专业版",
        "expireDate": "2025-07-01T12:00:00.000Z"
      },
      "purchased": {
        "total": 10,
        "used": 2,
        "remaining": 8,
        "expireDate": null
      },
      "total": {
        "remaining": 48
      }
    },
    "currencyOption": {
      "currencyCode": "ai_coin",
      "currencyName": "AI币",
      "cost": 7,
      "balance": 140
    },
    "purchaseOption": {
      "unitPrice": 13.50,
      "memberDiscount": 0.9
    }
  },
  "message": "获取成功"
}
```

### 1.30 用户-使用功能

**接口描述**：用户使用功能并记录消耗

**请求方法**：POST

**请求路径**：`/api/features/{featureCode}/use`

**请求参数**：

```json
{
  "useType": "auto",
  "metadata": {
    "templateId": "t123",
    "slideCount": 15
  }
}
```

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| useType | string | 否 | 使用类型，可选值：membership, purchased, currency, auto，默认auto |
| metadata | object | 否 | 使用元数据，记录使用详情 |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "usageId": "U202506010001",
    "featureCode": "ai.ppt",
    "featureName": "AI PPT生成",
    "useType": "membership",
    "consumed": {
      "type": "quota",
      "amount": 1
    },
    "remaining": {
      "membershipQuota": 39,
      "purchasedUses": 8,
      "currencyBalance": 140
    },
    "createTime": "2025-06-10 15:30:00"
  },
  "message": "使用成功"
}
```

## 2. 支付模块接口

### 2.1 用户-创建支付订单

**接口描述**：用户创建支付订单

**请求方法**：POST

**请求路径**：`/api/payment/create`

**请求参数**：

```json
{
  "businessType": "membership",
  "businessId": "1",
  "amount": 299.00,
  "subject": "专业版会员订阅",
  "description": "订阅专业版会员1个月",
  "paymentMethod": "alipay",
  "metadata": {
    "planId": 2,
    "duration": 1,
    "billingCycle": "monthly"
  }
}
```

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| businessType | string | 是 | 业务类型，如membership, upgrade |
| businessId | string | 是 | 业务ID，如会员ID |
| amount | number | 是 | 支付金额 |
| subject | string | 是 | 支付主题 |
| description | string | 是 | 支付描述 |
| paymentMethod | string | 是 | 支付方式，可选值：alipay, wechat, creditcard |
| metadata | object | 否 | 元数据，根据业务类型不同而不同 |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "orderNo": "P202506010001",
    "amount": 299.00,
    "paymentUrl": "https://payment-gateway.com/pay?orderNo=P202506010001",
    "qrCode": "base64-encoded-qr-code-image"
  },
  "message": "创建成功，请完成支付"
}
```

### 2.2 用户-查询支付状态

**接口描述**：查询支付订单状态

**请求方法**：GET

**请求路径**：`/api/payment/status/{orderNo}`

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| orderNo | string | 是 | 订单号 |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "orderNo": "P202506010001",
    "status": "success",
    "paymentTime": "2025-06-01 12:05:30",
    "amount": 299.00,
    "paymentMethod": "alipay",
    "businessType": "membership",
    "businessId": "1"
  },
  "message": "查询成功"
}
```

### 2.3 租户-获取支付配置

**接口描述**：获取租户的支付配置

**请求方法**：GET

**请求路径**：`/api/tenant/payment/config`

**请求参数**：无

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "alipay": {
      "enabled": true,
      "appId": "2021000000000000",
      "notifyUrl": "https://your-domain.com/api/payment/callback/alipay"
    },
    "wechat": {
      "enabled": true,
      "appId": "wx1234567890",
      "mchId": "1234567890"
    },
    "creditcard": {
      "enabled": false
    }
  },
  "message": "获取成功"
}
```

### 2.4 租户-获取支付订单列表

**接口描述**：获取租户的支付订单列表

**请求方法**：GET

**请求路径**：`/api/tenant/payment/orders/list`

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| businessType | string | 否 | 业务类型，如membership, upgrade |
| status | string | 否 | 支付状态，可选值：pending, success, failed, refunded |
| startTime | string | 否 | 开始时间，格式：YYYY-MM-DD |
| endTime | string | 否 | 结束时间，格式：YYYY-MM-DD |
| page | number | 否 | 页码，默认为1 |
| pageSize | number | 否 | 每页数量，默认为10 |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "items": [
      {
        "id": 1,
        "orderNo": "P202506010001",
        "userId": 101,
        "username": "张三",
        "amount": 299.00,
        "status": "success",
        "paymentMethod": "alipay",
        "businessType": "membership",
        "businessId": "1",
        "subject": "专业版会员订阅",
        "createTime": "2025-06-01 12:00:00",
        "paymentTime": "2025-06-01 12:05:30"
      },
      {
        "id": 2,
        "orderNo": "P202506020001",
        "userId": 102,
        "username": "李四",
        "amount": 999.00,
        "status": "pending",
        "paymentMethod": "wechat",
        "businessType": "membership",
        "businessId": "2",
        "subject": "企业版会员订阅",
        "createTime": "2025-06-02 10:00:00",
        "paymentTime": null
      }
    ],
    "total": 25,
    "page": 1,
    "pageSize": 10
  },
  "message": "获取成功"
}
```

### 2.5 租户-获取支付统计

**接口描述**：获取租户的支付统计数据

**请求方法**：GET

**请求路径**：`/api/tenant/payment/statistics`

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| startTime | string | 否 | 开始时间，格式：YYYY-MM-DD |
| endTime | string | 否 | 结束时间，格式：YYYY-MM-DD |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "totalRevenue": 45000,
    "successOrders": 150,
    "pendingOrders": 10,
    "failedOrders": 5,
    "refundedAmount": 1200,
    "paymentMethodDistribution": [
      {
        "method": "alipay",
        "count": 80,
        "amount": 25000,
        "percentage": 55.56
      },
      {
        "method": "wechat",
        "count": 70,
        "amount": 20000,
        "percentage": 44.44
      }
    ],
    "businessTypeDistribution": [
      {
        "type": "membership",
        "count": 120,
        "amount": 35000,
        "percentage": 77.78
      },
      {
        "type": "upgrade",
        "count": 30,
        "amount": 10000,
        "percentage": 22.22
      }
    ],
    "dailyRevenue": [
      {
        "date": "2025-06-01",
        "amount": 5000
      },
      {
        "date": "2025-06-02",
        "amount": 6000
      },
      {
        "date": "2025-06-03",
        "amount": 4500
      }
    ]
  },
  "message": "获取成功"
}
```

### 2.6 支付回调接口

**接口描述**：接收支付平台的支付结果回调

**请求方法**：POST

**请求路径**：`/api/payment/callback/{provider}`

**请求参数**：

根据不同支付平台的回调格式，接收相应的参数

**响应数据**：

根据不同支付平台的要求返回相应的成功标识

### 2.7 用户-申请退款

**接口描述**：用户申请退款

**请求方法**：POST

**请求路径**：`/api/payment/refund`

**请求参数**：

```json
{
  "orderNo": "P202506010001",
  "reason": "不想要了",
  "amount": 299.00
}
```

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| orderNo | string | 是 | 订单号 |
| reason | string | 是 | 退款原因 |
| amount | number | 是 | 退款金额，不能大于订单金额 |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "refundNo": "R202506050001",
    "orderNo": "P202506010001",
    "amount": 299.00,
    "status": "processing"
  },
  "message": "申请退款成功，请等待处理"
}
```

### 2.8 租户-处理退款申请

**接口描述**：租户处理退款申请

**请求方法**：POST

**请求路径**：`/api/tenant/payment/refund/process`

**请求参数**：

```json
{
  "refundNo": "R202506050001",
  "action": "approve",
  "comment": "同意退款"
}
```

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| refundNo | string | 是 | 退款单号 |
| action | string | 是 | 处理动作，可选值：approve, reject |
| comment | string | 否 | 处理备注 |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "refundNo": "R202506050001",
    "orderNo": "P202506010001",
    "amount": 299.00,
    "status": "approved",
    "comment": "同意退款"
  },
  "message": "处理成功"
}
```
