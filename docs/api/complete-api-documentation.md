# 多租户系统完整API文档

## 1. 认证模块

### 1.1 用户登录

**接口描述**：用户登录获取访问令牌

**请求方法**：POST

**请求路径**：`/auth/login`

**请求参数**：

```json
{
  "username": "admin",
  "password": "123456",
  "tenantCode": "demo" // 租户用户必填，系统用户可不填
}
```

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "username": "admin",
      "realName": "系统管理员",
      "userType": "SYSTEM"
    }
  },
  "message": "登录成功"
}
```

### 1.2 获取用户信息

**接口描述**：获取当前登录用户信息

**请求方法**：GET

**请求路径**：`/user/info`

**权限要求**：需要登录

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "id": 1,
    "username": "admin",
    "realName": "系统管理员",
    "email": "<EMAIL>",
    "userType": "SYSTEM",
    "roles": [
      {
        "id": "1",
        "code": "ADMIN",
        "name": "系统管理员"
      }
    ]
  },
  "message": "获取成功"
}
```

### 1.3 获取权限码

**接口描述**：获取当前用户的权限码列表

**请求方法**：GET

**请求路径**：`/auth/codes`

**权限要求**：需要登录

**响应数据**：

```json
{
  "code": 0,
  "data": [
    "system:user:view",
    "system:user:create",
    "system:user:update",
    "system:user:delete",
    "system:role:view",
    "system:role:create"
  ],
  "message": "获取成功"
}
```

### 1.4 刷新令牌

**接口描述**：刷新访问令牌

**请求方法**：POST

**请求路径**：`/auth/refresh`

**权限要求**：需要登录

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "message": "刷新成功"
}
```

### 1.5 退出登录

**接口描述**：用户退出登录

**请求方法**：POST

**请求路径**：`/auth/logout`

**权限要求**：需要登录

**响应数据**：

```json
{
  "code": 0,
  "data": null,
  "message": "退出成功"
}
```

## 2. 用户管理模块

### 2.1 获取用户列表

**接口描述**：分页获取用户列表

**请求方法**：GET

**请求路径**：`/api/users`

**权限要求**：`system:user:view` 或 `tenant:user:view`

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| page | number | 否 | 页码，默认为1 |
| pageSize | number | 否 | 每页数量，默认为10 |
| username | string | 否 | 用户名筛选 |
| realName | string | 否 | 真实姓名筛选 |
| status | number | 否 | 状态筛选：0-禁用，1-启用 |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "items": [
      {
        "id": 1,
        "username": "admin",
        "realName": "系统管理员",
        "email": "<EMAIL>",
        "status": 1,
        "createTime": "2025-01-01 00:00:00",
        "updateTime": "2025-01-01 00:00:00",
        "roles": [
          {
            "id": "1",
            "code": "ADMIN",
            "name": "系统管理员"
          }
        ]
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  },
  "message": "获取成功"
}
```

### 2.2 创建用户

**接口描述**：创建新用户

**请求方法**：POST

**请求路径**：`/api/users`

**权限要求**：`system:user:create` 或 `tenant:user:create`

**请求参数**：

```json
{
  "username": "newuser",
  "password": "123456",
  "realName": "新用户",
  "email": "<EMAIL>",
  "phoneNumber": "13800138000",
  "status": 1,
  "roleIds": ["2"]
}
```

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "id": 2,
    "username": "newuser",
    "realName": "新用户",
    "email": "<EMAIL>",
    "phoneNumber": "13800138000",
    "status": 1,
    "createTime": "2025-01-01 00:00:00",
    "updateTime": "2025-01-01 00:00:00"
  },
  "message": "创建成功"
}
```

### 2.3 更新用户

**接口描述**：更新用户信息

**请求方法**：PATCH

**请求路径**：`/api/users/{id}`

**权限要求**：`system:user:update` 或 `tenant:user:update`

**请求参数**：

```json
{
  "realName": "更新的用户名",
  "email": "<EMAIL>",
  "phoneNumber": "13900139000",
  "status": 1,
  "roleIds": ["2", "3"]
}
```

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "id": 2,
    "username": "newuser",
    "realName": "更新的用户名",
    "email": "<EMAIL>",
    "phoneNumber": "13900139000",
    "status": 1,
    "updateTime": "2025-01-01 01:00:00"
  },
  "message": "更新成功"
}
```

### 2.4 删除用户

**接口描述**：删除用户

**请求方法**：DELETE

**请求路径**：`/api/users/{id}`

**权限要求**：`system:user:delete` 或 `tenant:user:delete`

**响应数据**：

```json
{
  "code": 0,
  "data": null,
  "message": "删除成功"
}
```

### 2.5 修改密码

**接口描述**：用户修改自己的密码

**请求方法**：POST

**请求路径**：`/api/users/{id}/change-password`

**权限要求**：需要登录且只能修改自己的密码

**请求参数**：

```json
{
  "oldPassword": "123456",
  "newPassword": "newpassword",
  "confirmPassword": "newpassword"
}
```

**响应数据**：

```json
{
  "code": 0,
  "data": null,
  "message": "密码修改成功"
}
```

### 2.6 重置密码

**接口描述**：管理员重置用户密码

**请求方法**：POST

**请求路径**：`/api/users/{id}/reset-password`

**权限要求**：`system:user:reset-password` 或 `tenant:user:reset-password`

**请求参数**：

```json
{
  "newPassword": "newpassword",
  "confirmPassword": "newpassword"
}
```

**响应数据**：

```json
{
  "code": 0,
  "data": null,
  "message": "密码重置成功"
}
```

## 3. 角色管理模块

### 3.1 获取角色列表

**接口描述**：获取角色列表

**请求方法**：GET

**请求路径**：`/system/role/list`

**权限要求**：`system:role:view` 或 `tenant:role:view`

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| page | number | 否 | 页码，默认为1 |
| pageSize | number | 否 | 每页数量，默认为10 |
| name | string | 否 | 角色名称筛选 |
| code | string | 否 | 角色代码筛选 |
| status | number | 否 | 状态筛选：0-禁用，1-启用 |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "items": [
      {
        "id": "1",
        "code": "ADMIN",
        "name": "系统管理员",
        "description": "系统管理员角色",
        "status": 1,
        "createTime": "2025-01-01 00:00:00",
        "updateTime": "2025-01-01 00:00:00"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  },
  "message": "获取成功"
}
```

### 3.2 创建角色

**接口描述**：创建新角色

**请求方法**：POST

**请求路径**：`/system/role`

**权限要求**：`system:role:create` 或 `tenant:role:create`

**请求参数**：

```json
{
  "code": "USER",
  "name": "普通用户",
  "description": "普通用户角色",
  "status": 1
}
```

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "id": "2",
    "code": "USER",
    "name": "普通用户",
    "description": "普通用户角色",
    "status": 1,
    "createTime": "2025-01-01 00:00:00",
    "updateTime": "2025-01-01 00:00:00"
  },
  "message": "创建成功"
}
```

### 3.3 更新角色

**接口描述**：更新角色信息

**请求方法**：PUT

**请求路径**：`/system/role/{id}`

**权限要求**：`system:role:update` 或 `tenant:role:update`

**请求参数**：

```json
{
  "name": "更新的角色名",
  "description": "更新的角色描述",
  "status": 1
}
```

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "id": "2",
    "code": "USER",
    "name": "更新的角色名",
    "description": "更新的角色描述",
    "status": 1,
    "updateTime": "2025-01-01 01:00:00"
  },
  "message": "更新成功"
}
```

### 3.4 删除角色

**接口描述**：删除角色

**请求方法**：DELETE

**请求路径**：`/system/role/{id}`

**权限要求**：`system:role:delete` 或 `tenant:role:delete`

**响应数据**：

```json
{
  "code": 0,
  "data": null,
  "message": "删除成功"
}
```

## 4. 权限管理模块

### 4.1 获取权限列表

**接口描述**：获取权限列表

**请求方法**：GET

**请求路径**：`/system/permission/list`

**权限要求**：`system:permission:view` 或 `tenant:permission:view`

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| name | string | 否 | 权限名称筛选 |
| code | string | 否 | 权限代码筛选 |
| status | number | 否 | 状态筛选：0-禁用，1-启用 |

**响应数据**：

```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "name": "查看用户",
      "code": "system:user:view",
      "description": "查看用户列表权限",
      "status": 1,
      "createdAt": "2025-01-01T00:00:00.000Z",
      "updatedAt": "2025-01-01T00:00:00.000Z"
    }
  ],
  "message": "获取成功"
}
```

### 4.2 创建权限

**接口描述**：创建新权限

**请求方法**：POST

**请求路径**：`/system/permission`

**权限要求**：`system:permission:create` 或 `tenant:permission:create`

**请求参数**：

```json
{
  "name": "创建用户",
  "code": "system:user:create",
  "description": "创建用户权限",
  "status": 1
}
```

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "id": 2,
    "name": "创建用户",
    "code": "system:user:create",
    "description": "创建用户权限",
    "status": 1,
    "createdAt": "2025-01-01T00:00:00.000Z",
    "updatedAt": "2025-01-01T00:00:00.000Z"
  },
  "message": "创建成功"
}
```

### 4.3 更新权限

**接口描述**：更新权限信息

**请求方法**：PUT

**请求路径**：`/system/permission/{id}`

**权限要求**：`system:permission:update` 或 `tenant:permission:update`

**请求参数**：

```json
{
  "name": "更新的权限名",
  "description": "更新的权限描述",
  "status": 1
}
```

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "id": 2,
    "name": "更新的权限名",
    "code": "system:user:create",
    "description": "更新的权限描述",
    "status": 1,
    "updatedAt": "2025-01-01T01:00:00.000Z"
  },
  "message": "更新成功"
}
```

### 4.4 删除权限

**接口描述**：删除权限

**请求方法**：DELETE

**请求路径**：`/system/permission/{id}`

**权限要求**：`system:permission:delete` 或 `tenant:permission:delete`

**响应数据**：

```json
{
  "code": 0,
  "data": null,
  "message": "删除成功"
}
```

### 4.5 检查权限码是否存在

**接口描述**：检查权限码是否已存在

**请求方法**：GET

**请求路径**：`/system/permission/code-exists`

**权限要求**：`system:permission:view` 或 `tenant:permission:view`

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| code | string | 是 | 权限码 |
| id | number | 否 | 排除的权限ID（用于更新时检查） |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "exists": false
  },
  "message": "检查完成"
}
```

### 4.6 分配权限给角色

**接口描述**：为角色分配权限

**请求方法**：POST

**请求路径**：`/system/permission/assign`

**权限要求**：`system:permission:assign` 或 `tenant:permission:assign`

**请求参数**：

```json
{
  "roleId": "1",
  "permissionIds": [1, 2, 3]
}
```

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "success": true
  },
  "message": "分配成功"
}
```

### 4.7 获取角色权限列表

**接口描述**：获取指定角色的权限列表

**请求方法**：GET

**请求路径**：`/system/permission/roles/{roleId}`

**权限要求**：`system:permission:view` 或 `tenant:permission:view`

**响应数据**：

```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "name": "查看用户",
      "code": "system:user:view",
      "description": "查看用户列表权限",
      "status": 1
    }
  ],
  "message": "获取成功"
}
```

## 5. 菜单管理模块

### 5.1 获取所有菜单

**接口描述**：获取当前用户可访问的菜单树

**请求方法**：GET

**请求路径**：`/menu/all`

**权限要求**：需要登录

**响应数据**：

```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "name": "系统管理",
      "path": "/system",
      "component": "Layout",
      "type": "menu",
      "icon": "system",
      "sort": 1,
      "meta": {
        "title": "系统管理",
        "icon": "system",
        "hidden": false
      },
      "children": [
        {
          "id": 2,
          "name": "用户管理",
          "path": "/system/user",
          "component": "system/user/index",
          "type": "menu",
          "icon": "user",
          "sort": 1,
          "meta": {
            "title": "用户管理",
            "icon": "user"
          }
        }
      ]
    }
  ],
  "message": "获取成功"
}
```

### 5.2 获取菜单列表

**接口描述**：获取菜单列表（管理用）

**请求方法**：GET

**请求路径**：`/system/menu/list`

**权限要求**：`system:menu:view` 或 `tenant:menu:view`

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| name | string | 否 | 菜单名称筛选 |
| type | string | 否 | 菜单类型筛选 |
| status | number | 否 | 状态筛选：0-禁用，1-启用 |

**响应数据**：

```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "name": "系统管理",
      "path": "/system",
      "component": "Layout",
      "type": "menu",
      "parentId": null,
      "icon": "system",
      "sort": 1,
      "status": 1,
      "meta": {
        "title": "系统管理",
        "icon": "system",
        "hidden": false
      },
      "createTime": "2025-01-01 00:00:00",
      "updateTime": "2025-01-01 00:00:00"
    }
  ],
  "message": "获取成功"
}
```

### 5.3 创建菜单

**接口描述**：创建新菜单

**请求方法**：POST

**请求路径**：`/system/menu`

**权限要求**：`system:menu:create` 或 `tenant:menu:create`

**请求参数**：

```json
{
  "name": "新菜单",
  "path": "/new-menu",
  "component": "new-menu/index",
  "type": "menu",
  "parentId": 1,
  "icon": "menu",
  "sort": 1,
  "status": 1,
  "meta": {
    "title": "新菜单",
    "icon": "menu",
    "hidden": false
  }
}
```

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "id": 3,
    "name": "新菜单",
    "path": "/new-menu",
    "component": "new-menu/index",
    "type": "menu",
    "parentId": 1,
    "icon": "menu",
    "sort": 1,
    "status": 1,
    "meta": {
      "title": "新菜单",
      "icon": "menu",
      "hidden": false
    },
    "createTime": "2025-01-01 00:00:00",
    "updateTime": "2025-01-01 00:00:00"
  },
  "message": "创建成功"
}
```

## 6. 租户管理模块

### 6.1 获取租户列表

**接口描述**：获取租户列表（系统管理员专用）

**请求方法**：GET

**请求路径**：`/api/tenant/list`

**权限要求**：`system:tenant:view`

**请求参数**：

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| page | number | 否 | 页码，默认为1 |
| pageSize | number | 否 | 每页数量，默认为10 |
| name | string | 否 | 租户名称筛选 |
| code | string | 否 | 租户代码筛选 |
| status | number | 否 | 状态筛选：0-禁用，1-启用 |

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "items": [
      {
        "id": 1,
        "code": "demo",
        "name": "演示租户",
        "description": "用于演示的租户",
        "status": 1,
        "website": "https://demo.example.com",
        "contactEmail": "<EMAIL>",
        "contactPhone": "************",
        "createTime": "2025-01-01 00:00:00",
        "updateTime": "2025-01-01 00:00:00"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  },
  "message": "获取成功"
}
```

### 6.2 创建租户

**接口描述**：创建新租户（系统管理员专用）

**请求方法**：POST

**请求路径**：`/api/tenant`

**权限要求**：`system:tenant:create`

**请求参数**：

```json
{
  "code": "newtenant",
  "name": "新租户",
  "description": "新创建的租户",
  "status": 1,
  "website": "https://newtenant.example.com",
  "contactEmail": "<EMAIL>",
  "contactPhone": "************"
}
```

**响应数据**：

```json
{
  "code": 0,
  "data": {
    "id": 2,
    "code": "newtenant",
    "name": "新租户",
    "description": "新创建的租户",
    "status": 1,
    "website": "https://newtenant.example.com",
    "contactEmail": "<EMAIL>",
    "contactPhone": "************",
    "createTime": "2025-01-01 00:00:00",
    "updateTime": "2025-01-01 00:00:00"
  },
  "message": "创建成功"
}
```

## 7. 待实现模块

### 7.1 数据源管理模块

以下接口需要实现：

- `GET /system/datasource/list` - 获取数据源列表
- `POST /system/datasource` - 创建数据源
- `PUT /system/datasource/{id}` - 更新数据源
- `DELETE /system/datasource/{id}` - 删除数据源
- `POST /system/datasource/test` - 测试数据源连接

### 7.2 会员管理模块

详细接口参考：`docs/api/membership-payment-api.md`

主要包括：
- 会员计划管理
- 会员订阅管理
- 会员权益管理
- 虚拟币管理

### 7.3 支付模块

详细接口参考：`docs/api/membership-payment-api.md`

主要包括：
- 支付订单管理
- 支付配置管理
- 退款管理
- 支付统计

## 8. 错误响应格式

所有接口的错误响应格式统一如下：

```json
{
  "code": 400,
  "data": null,
  "message": "参数错误",
  "timestamp": "2025-01-01T00:00:00.000Z",
  "path": "/api/users"
}
```

常见错误码：

| 错误码 | 描述 |
|-------|------|
| 400 | 请求参数错误 |
| 401 | 未认证或Token无效 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突（如重复创建） |
| 500 | 服务器内部错误 |

## 9. 权限系统说明

### 9.1 权限码格式

权限码采用分层结构：`模块:操作对象:操作类型`

例如：
- `system:user:view` - 系统用户查看权限
- `system:user:create` - 系统用户创建权限
- `tenant:role:update` - 租户角色更新权限

### 9.2 权限装饰器使用

在控制器方法上使用权限装饰器：

```typescript
@Permissions('system:user:view')
@Get('list')
async getUserList() {
  // 方法实现
}
```

### 9.3 角色装饰器使用

在控制器方法上使用角色装饰器：

```typescript
@Roles('ADMIN', 'MANAGER')
@Post()
async createUser() {
  // 方法实现
}
```

## 10. 开发优先级

### 高优先级（立即开发）
1. 数据源管理模块
2. 权限守卫完善
3. 用户密码管理完善

### 中优先级（下一阶段）
1. 会员管理模块
2. 支付模块
3. 租户功能增强

### 低优先级（后续规划）
1. AI模块集成
2. 高级分析功能
3. 第三方集成 
