# 角色管理接口文档

本文档描述了vue-vben-admin系统中与角色管理相关的API接口规范。

## 1. 获取角色列表接口

获取系统中所有角色列表。

**请求**：
- 方法：`GET`
- 路径：`/system/role/list`
- 头部：需要包含`Authorization: Bearer {accessToken}`
- 查询参数（可选）：
  - `page`：页码
  - `pageSize`：每页数量
  - `name`：角色名称（模糊查询）
  - `status`：角色状态

**响应**：
```json
{
  "code": 200,           // 状态码
  "data": [
    {
      "id": "string",           // 角色ID
      "name": "string",         // 角色名称
      "permissions": ["string"], // 角色拥有的权限列表
      "remark": "string",       // 角色描述
      "status": 1,              // 角色状态：0-禁用，1-启用
      "createTime": "string",   // 创建时间
      "updateTime": "string"    // 更新时间
    }
  ],
  "message": "string"    // 消息
}
```

**错误响应**：
- `401 Unauthorized`：未授权或Token无效
- `403 Forbidden`：权限不足
- `500 Internal Server Error`：服务器错误

## 2. 创建角色接口

创建新的角色。

**请求**：
- 方法：`POST`
- 路径：`/system/role`
- 头部：需要包含`Authorization: Bearer {accessToken}`
- 内容类型：`application/json`

**请求参数**：
```json
{
  "name": "string",           // 角色名称
  "permissions": ["string"],  // 角色权限列表
  "remark": "string",         // 角色描述
  "status": 1                 // 角色状态：0-禁用，1-启用
}
```

**响应**：
```json
{
  "code": 200,           // 状态码
  "data": {
    "id": "string",           // 新创建的角色ID
    "name": "string",         // 角色名称
    "permissions": ["string"], // 角色权限列表
    "remark": "string",       // 角色描述
    "status": 1,              // 角色状态
    "createTime": "string"    // 创建时间
  },
  "message": "string"    // 消息
}
```

**错误响应**：
- `400 Bad Request`：参数错误
- `401 Unauthorized`：未授权或Token无效
- `403 Forbidden`：权限不足
- `409 Conflict`：角色名称已存在
- `500 Internal Server Error`：服务器错误

## 3. 更新角色接口

更新现有角色。

**请求**：
- 方法：`PUT`
- 路径：`/system/role/{id}`
- 头部：需要包含`Authorization: Bearer {accessToken}`
- 内容类型：`application/json`

**请求参数**：
```json
{
  "name": "string",           // 角色名称
  "permissions": ["string"],  // 角色权限列表
  "remark": "string",         // 角色描述
  "status": 1                 // 角色状态：0-禁用，1-启用
}
```

**响应**：
```json
{
  "code": 200,           // 状态码
  "data": {
    "id": "string",           // 角色ID
    "name": "string",         // 角色名称
    "permissions": ["string"], // 角色权限列表
    "remark": "string",       // 角色描述
    "status": 1,              // 角色状态
    "updateTime": "string"    // 更新时间
  },
  "message": "string"    // 消息
}
```

**错误响应**：
- `400 Bad Request`：参数错误
- `401 Unauthorized`：未授权或Token无效
- `403 Forbidden`：权限不足
- `404 Not Found`：角色不存在
- `409 Conflict`：角色名称已存在
- `500 Internal Server Error`：服务器错误

## 4. 删除角色接口

删除指定的角色。

**请求**：
- 方法：`DELETE`
- 路径：`/system/role/{id}`
- 头部：需要包含`Authorization: Bearer {accessToken}`

**响应**：
```json
{
  "code": 200,           // 状态码
  "data": {
    "success": true      // 是否成功
  },
  "message": "string"    // 消息
}
```

**错误响应**：
- `401 Unauthorized`：未授权或Token无效
- `403 Forbidden`：权限不足
- `404 Not Found`：角色不存在
- `500 Internal Server Error`：服务器错误

## 5. 分配角色给用户接口

将角色分配给指定用户。

**请求**：
- 方法：`POST`
- 路径：`/system/role/assign`
- 头部：需要包含`Authorization: Bearer {accessToken}`
- 内容类型：`application/json`

**请求参数**：
```json
{
  "userId": "string",    // 用户ID
  "roleIds": ["string"]  // 角色ID列表
}
```

**响应**：
```json
{
  "code": 200,           // 状态码
  "data": {
    "success": true      // 是否成功
  },
  "message": "string"    // 消息
}
```

**错误响应**：
- `400 Bad Request`：参数错误
- `401 Unauthorized`：未授权或Token无效
- `403 Forbidden`：权限不足
- `404 Not Found`：用户或角色不存在
- `500 Internal Server Error`：服务器错误

## 6. 移除用户角色接口

移除指定用户的角色。

**请求**：
- 方法：`DELETE`
- 路径：`/system/role/users/{userId}/roles/{roleId}`
- 头部：需要包含`Authorization: Bearer {accessToken}`

**响应**：
```json
{
  "code": 200,           // 状态码
  "data": {
    "success": true      // 是否成功
  },
  "message": "string"    // 消息
}
```

**错误响应**：
- `401 Unauthorized`：未授权或Token无效
- `403 Forbidden`：权限不足
- `404 Not Found`：用户、角色或用户角色关联不存在
- `500 Internal Server Error`：服务器错误

## 7. 获取角色权限接口

获取指定角色的权限列表。

**请求**：
- 方法：`GET`
- 路径：`/system/permission/roles/{roleId}`
- 头部：需要包含`Authorization: Bearer {accessToken}`

**响应**：
```json
{
  "code": 200,           // 状态码
  "data": [
    {
      "id": "string",      // 权限ID
      "name": "string",    // 权限名称
      "resource": "string", // 资源
      "action": "string",  // 操作
      "description": "string" // 描述
    }
  ],
  "message": "string"    // 消息
}
```

**错误响应**：
- `401 Unauthorized`：未授权或Token无效
- `403 Forbidden`：权限不足
- `404 Not Found`：角色不存在
- `500 Internal Server Error`：服务器错误

## 实现注意事项

1. **角色与权限关系**：
   - 一个用户可以拥有多个角色
   - 一个角色可以拥有多个权限
   - 权限应该与菜单和按钮操作关联

2. **权限码格式**：
   - 建议采用`资源:操作`格式，如`system:user:create`
   - 确保前后端权限码命名一致

3. **角色状态**：
   - 禁用的角色不应该被分配给用户
   - 如果角色被禁用，已分配该角色的用户应该失去相应权限

4. **超级管理员**：
   - 超级管理员角色应该拥有所有权限
   - 超级管理员角色不应该被删除或禁用
