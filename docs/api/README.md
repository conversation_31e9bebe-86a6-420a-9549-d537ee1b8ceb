# 多租户系统 API 文档

本目录包含多租户系统的 API 文档，供前端开发人员使用。

## 文档目录

1. [前端 API 文档](./前端API文档.md)
   - 认证相关接口
   - 用户信息接口
   - 菜单相关接口

2. [租户管理 API 文档](./租户管理API文档.md)
   - 租户管理接口
   - 数据源管理接口

## 通用说明

### 基础 URL

所有 API 请求的基础 URL 为：`/api`

### 认证方式

除了登录接口外，所有接口都需要在请求头中携带 JWT 令牌：

```http
Authorization: Bearer {token}
```

### 租户标识

对于租户相关的请求，需要在请求头中携带租户代码：

```http
x-tenant-code: {tenantCode}
```

### 响应格式

所有接口的响应格式统一为：

```json
{
  "code": 0,         // 状态码，0 表示成功，非 0 表示失败
  "data": {},        // 响应数据
  "message": "操作成功" // 响应消息
}
```

### 错误码说明

| 错误码 | 说明 |
| ------ | ---- |
| 0      | 成功 |
| 400    | 请求参数错误 |
| 401    | 未授权 |
| 403    | 禁止访问 |
| 404    | 资源不存在 |
| 500    | 服务器内部错误 |

## 接口规范

1. 所有接口都应该遵循 RESTful API 设计规范
2. 接口路径使用小写字母，单词之间使用连字符（-）分隔
3. 接口返回的数据格式统一为 JSON
4. 接口返回的状态码统一为 200，错误信息通过响应体中的 `code` 和 `message` 字段表示

## 接口安全性

1. 所有接口都应该使用 HTTPS
2. 敏感信息（如密码）应该在传输前进行加密
3. 所有接口都应该进行权限验证
4. 接口应该限制请求频率，防止恶意攻击

## 更新日志

### 2023-05-01

- 初始版本发布
- 添加认证相关接口
- 添加菜单相关接口

### 2023-05-15

- 添加租户管理接口
- 添加数据源管理接口
- 更新菜单类型，支持多种菜单类型
