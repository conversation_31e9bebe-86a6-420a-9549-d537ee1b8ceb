# 租户功能权限管理与配置管理 API 文档

## 文档信息

- **版本**：1.1
- **创建日期**：2025-05-15
- **最后更新**：2025-05-15
- **状态**：草稿

## 1. 概述

本文档定义了租户功能权限管理和租户配置管理的 API 接口，供前后端开发人员参考。前端需要基于这些 API 开发对应的管理界面，后端需要实现这些 API 接口。

> **修订说明**：
> 1. 前端后台管理系统使用 Vue 3 + TypeScript 开发
> 2. 前端已有基础的角色管理、菜单管理、部门管理、用户管理和租户管理功能

## 2. 基础信息

### 2.1 基础路径

所有 API 均以 `/api` 为基础路径。

### 2.2 认证方式

除了特别标注的公开接口外，所有接口都需要在请求头中包含 JWT 令牌：

```
Authorization: Bearer {token}
```

### 2.3 响应格式

所有 API 响应均采用以下统一格式：

```json
{
  "code": 0,         // 0 表示成功，非 0 表示错误
  "data": {},        // 响应数据，可能是对象、数组或 null
  "message": "成功"   // 响应消息
}
```

### 2.4 错误处理

当发生错误时，响应格式如下：

```json
{
  "code": 10001,             // 错误代码
  "data": null,              // 通常为 null
  "message": "错误详细信息"    // 错误描述
}
```

常见错误代码：
- `10001` - 参数错误
- `10002` - 资源不存在
- `10003` - 权限不足
- `10004` - 功能未启用
- `10005` - 配额不足

## 3. 租户功能权限管理 API

### 3.1 功能模板管理

#### 3.1.1 获取所有功能模板

**请求**：
- 方法：`GET`
- 路径：`/api/system/feature-templates/list`
- 权限：系统管理员

**响应**：
```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "code": "basic",
      "name": "基础版",
      "features": {
        "ai.ppt": {
          "enabled": true,
          "quota": 10
        },
        "tenant-config.email": {
          "enabled": true
        }
      },
      "isActive": true,
      "createdAt": "2025-05-15T08:00:00Z",
      "updatedAt": "2025-05-15T08:00:00Z"
    },
    {
      "id": 2,
      "code": "pro",
      "name": "专业版",
      "features": {
        "ai.ppt": {
          "enabled": true,
          "quota": 50
        },
        "ai.document": {
          "enabled": true,
          "quota": 20
        },
        "tenant-config.email": {
          "enabled": true
        },
        "tenant-config.sms": {
          "enabled": true
        }
      },
      "isActive": true,
      "createdAt": "2025-05-15T08:00:00Z",
      "updatedAt": "2025-05-15T08:00:00Z"
    }
  ],
  "message": "获取成功"
}
```

#### 3.1.2 获取功能模板详情

**请求**：
- 方法：`GET`
- 路径：`/api/system/feature-templates/{code}`
- 权限：系统管理员
- 参数：
  - `code` - 模板代码（路径参数）

**响应**：
```json
{
  "code": 0,
  "data": {
    "id": 1,
    "code": "basic",
    "name": "基础版",
    "features": {
      "ai.ppt": {
        "enabled": true,
        "quota": 10
      },
      "tenant-config.email": {
        "enabled": true
      }
    },
    "isActive": true,
    "createdAt": "2025-05-15T08:00:00Z",
    "updatedAt": "2025-05-15T08:00:00Z"
  },
  "message": "获取成功"
}
```

#### 3.1.3 创建或更新功能模板

**请求**：
- 方法：`POST`
- 路径：`/api/system/feature-templates/upsert`
- 权限：系统管理员
- 请求体：
```json
{
  "code": "enterprise",
  "name": "企业版",
  "features": {
    "ai.ppt": {
      "enabled": true,
      "quota": null
    },
    "ai.document": {
      "enabled": true,
      "quota": null
    },
    "tenant-config.email": {
      "enabled": true
    },
    "tenant-config.sms": {
      "enabled": true
    },
    "tenant-config.oss": {
      "enabled": true
    },
    "tenant-config.payment": {
      "enabled": true
    }
  },
  "isActive": true
}
```

**响应**：
```json
{
  "code": 0,
  "data": {
    "id": 3,
    "code": "enterprise",
    "name": "企业版",
    "features": {
      "ai.ppt": {
        "enabled": true,
        "quota": null
      },
      "ai.document": {
        "enabled": true,
        "quota": null
      },
      "tenant-config.email": {
        "enabled": true
      },
      "tenant-config.sms": {
        "enabled": true
      },
      "tenant-config.oss": {
        "enabled": true
      },
      "tenant-config.payment": {
        "enabled": true
      }
    },
    "isActive": true,
    "createdAt": "2025-05-15T08:30:00Z",
    "updatedAt": "2025-05-15T08:30:00Z"
  },
  "message": "保存成功"
}
```

#### 3.1.4 删除功能模板

**请求**：
- 方法：`DELETE`
- 路径：`/api/system/feature-templates/{code}`
- 权限：系统管理员
- 参数：
  - `code` - 模板代码（路径参数）

**响应**：
```json
{
  "code": 0,
  "data": null,
  "message": "删除成功"
}
```

### 3.2 租户功能管理

#### 3.2.1 获取租户功能列表

**请求**：
- 方法：`GET`
- 路径：`/api/system/tenant-features/list`
- 权限：系统管理员
- 参数：
  - `tenantId` - 租户ID（查询参数）

**响应**：
```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "tenantId": 1,
      "featureCode": "ai.ppt",
      "enabled": true,
      "expiresAt": "2025-12-31T23:59:59Z",
      "quota": 50,
      "usedQuota": 10,
      "config": {
        "allowedTemplates": ["basic", "professional"]
      },
      "createdAt": "2025-05-15T08:00:00Z",
      "updatedAt": "2025-05-15T08:00:00Z"
    },
    {
      "id": 2,
      "tenantId": 1,
      "featureCode": "tenant-config.email",
      "enabled": true,
      "expiresAt": null,
      "quota": null,
      "usedQuota": 0,
      "config": {},
      "createdAt": "2025-05-15T08:00:00Z",
      "updatedAt": "2025-05-15T08:00:00Z"
    }
  ],
  "message": "获取成功"
}
```

#### 3.2.2 启用功能

**请求**：
- 方法：`POST`
- 路径：`/api/system/tenant-features/enable`
- 权限：系统管理员
- 请求体：
```json
{
  "tenantId": 1,
  "featureCode": "ai.document",
  "config": {
    "expiresAt": "2025-12-31T23:59:59Z",
    "quota": 20,
    "allowedTemplates": ["basic", "professional"]
  }
}
```

**响应**：
```json
{
  "code": 0,
  "data": null,
  "message": "功能已启用"
}
```

#### 3.2.3 禁用功能

**请求**：
- 方法：`POST`
- 路径：`/api/system/tenant-features/disable`
- 权限：系统管理员
- 请求体：
```json
{
  "tenantId": 1,
  "featureCode": "ai.document"
}
```

**响应**：
```json
{
  "code": 0,
  "data": null,
  "message": "功能已禁用"
}
```

#### 3.2.4 应用功能模板

**请求**：
- 方法：`POST`
- 路径：`/api/system/tenant-features/apply-template`
- 权限：系统管理员
- 请求体：
```json
{
  "tenantId": 1,
  "templateCode": "pro"
}
```

**响应**：
```json
{
  "code": 0,
  "data": null,
  "message": "模板已应用"
}
```

#### 3.2.5 获取当前租户功能

**请求**：
- 方法：`GET`
- 路径：`/api/tenant/features/my-features`
- 权限：租户用户

**响应**：
```json
{
  "code": 0,
  "data": [
    {
      "code": "ai.ppt",
      "enabled": true,
      "quota": 50,
      "usedQuota": 10,
      "remaining": 40,
      "expiresAt": "2025-12-31T23:59:59Z",
      "config": {
        "allowedTemplates": ["basic", "professional"]
      }
    },
    {
      "code": "tenant-config.email",
      "enabled": true,
      "quota": null,
      "usedQuota": 0,
      "remaining": null,
      "expiresAt": null,
      "config": {}
    }
  ],
  "message": "获取成功"
}
```

#### 3.2.6 获取功能配置

**请求**：
- 方法：`GET`
- 路径：`/api/tenant/features/config/{featureCode}`
- 权限：租户用户
- 参数：
  - `featureCode` - 功能代码（路径参数）

**响应**：
```json
{
  "code": 0,
  "data": {
    "quota": 50,
    "usedQuota": 10,
    "remaining": 40,
    "expiresAt": "2025-12-31T23:59:59Z",
    "allowedTemplates": ["basic", "professional"]
  },
  "message": "获取成功"
}
```

## 4. 租户配置管理 API

### 4.1 配置类别管理

#### 4.1.1 获取所有配置类别

**请求**：
- 方法：`GET`
- 路径：`/api/system/tenant-configs/categories`
- 权限：系统管理员或租户管理员

**响应**：
```json
{
  "code": 0,
  "data": [
    {
      "code": "email",
      "name": "邮件服务",
      "description": "配置邮件发送服务",
      "requiredFeature": "tenant-config.email"
    },
    {
      "code": "sms",
      "name": "短信服务",
      "description": "配置短信发送服务",
      "requiredFeature": "tenant-config.sms"
    },
    {
      "code": "oss",
      "name": "对象存储",
      "description": "配置文件存储服务",
      "requiredFeature": "tenant-config.oss"
    },
    {
      "code": "payment",
      "name": "支付服务",
      "description": "配置支付处理服务",
      "requiredFeature": "tenant-config.payment"
    }
  ],
  "message": "获取成功"
}
```

### 4.2 租户配置管理

#### 4.2.1 获取租户配置

**请求**：
- 方法：`GET`
- 路径：`/api/system/tenant-configs/{category}`
- 权限：系统管理员或租户管理员
- 参数：
  - `category` - 配置类别（路径参数）
  - `tenantId` - 租户ID（查询参数，系统管理员必填，租户管理员忽略）

**响应**：
```json
{
  "code": 0,
  "data": {
    "provider": "smtp",
    "host": "smtp.example.com",
    "port": 587,
    "username": "<EMAIL>",
    "password": "******",
    "fromName": "Example Service",
    "fromEmail": "<EMAIL>",
    "enableSSL": true
  },
  "message": "获取成功"
}
```

#### 4.2.2 更新租户配置

**请求**：
- 方法：`POST`
- 路径：`/api/system/tenant-configs/{category}`
- 权限：系统管理员或租户管理员
- 参数：
  - `category` - 配置类别（路径参数）
  - `tenantId` - 租户ID（查询参数，系统管理员必填，租户管理员忽略）
- 请求体（以邮件配置为例）：
```json
{
  "provider": "smtp",
  "host": "smtp.example.com",
  "port": 587,
  "username": "<EMAIL>",
  "password": "password123",
  "fromName": "Example Service",
  "fromEmail": "<EMAIL>",
  "enableSSL": true
}
```

**响应**：
```json
{
  "code": 0,
  "data": null,
  "message": "配置已更新"
}
```

## 5. 前端开发指南

基于已有的前端基础功能（角色管理、菜单管理、部门管理、用户管理和租户管理），需要开发以下新功能模块：

### 5.1 系统管理界面

#### 5.1.1 功能模板管理

- **功能模板列表页面**
  - 路径：`/system/feature-templates`
  - 功能：
    - 显示所有功能模板
    - 支持创建、编辑、删除模板
    - 显示每个模板包含的功能
  - API：
    - `GET /api/system/feature-templates/list`
    - `DELETE /api/system/feature-templates/{code}`

- **功能模板编辑页面**
  - 路径：`/system/feature-templates/edit`
  - 功能：
    - 编辑模板基本信息（名称、代码等）
    - 配置模板包含的功能
    - 设置每个功能的配额和其他配置
  - API：
    - `GET /api/system/feature-templates/{code}`
    - `POST /api/system/feature-templates/upsert`

#### 5.1.2 租户功能管理

- **租户功能列表页面**
  - 路径：`/system/tenant-features`
  - 功能：
    - 选择租户查看其功能列表
    - 显示每个功能的状态、配额和过期时间
    - 支持启用/禁用功能
    - 支持应用功能模板
  - API：
    - `GET /api/system/tenant-features/list`
    - `POST /api/system/tenant-features/enable`
    - `POST /api/system/tenant-features/disable`
    - `POST /api/system/tenant-features/apply-template`

#### 5.1.3 租户配置管理

- **租户配置列表页面**
  - 路径：`/system/tenant-configs`
  - 功能：
    - 选择租户和配置类别
    - 显示和编辑配置
    - 支持测试配置
  - API：
    - `GET /api/system/tenant-configs/categories`
    - `GET /api/system/tenant-configs/{category}`
    - `POST /api/system/tenant-configs/{category}`
    - `POST /api/system/tenant-configs/{category}/test`

### 5.2 租户管理界面

#### 5.2.1 功能概览

- **功能仪表盘**
  - 路径：`/tenant/features`
  - 功能：
    - 显示当前租户拥有的所有功能
    - 展示每个功能的状态、配额使用情况和过期时间
  - API：
    - `GET /api/tenant/features/my-features`

#### 5.2.2 租户配置管理

- **配置中心**
  - 路径：`/tenant/configs`
  - 功能：
    - 按类别组织不同的配置项
    - 提供配置表单和验证
    - 支持测试配置有效性
  - API：
    - 与系统管理界面共用API，但不需要指定tenantId参数

## 6. 开发计划

### 6.1 后端开发（4周）

1. **第一阶段：数据模型和核心服务（2周）**
   - 创建`TenantFeature`和`FeatureTemplate`数据表
   - 实现`TenantFeatureService`和`FeatureTemplateService`
   - 实现功能守卫和装饰器
   - 实现`TenantConfig`数据表和服务

2. **第二阶段：API实现（2周）**
   - 实现功能模板管理API
   - 实现租户功能管理API
   - 实现租户配置管理API
   - 编写单元测试和集成测试

### 6.2 前端开发（4周）

1. **第一阶段：系统管理界面（2周）**
   - 开发功能模板管理页面
   - 开发租户功能管理页面
   - 开发租户配置管理页面

2. **第二阶段：租户管理界面（2周）**
   - 开发功能仪表盘
   - 开发配置中心页面

## 7. 结论

本API文档定义了租户功能权限管理和租户配置管理的接口规范，为前后端开发提供了明确的指导。通过实现这些API，系统将能够灵活管理租户功能权限和配置，支持多租户系统的商业化运营和功能差异化。

前端团队应基于现有的Vue 3 + TypeScript架构，开发对应的管理界面；后端团队应实现这些API接口，确保功能的正确性和安全性。通过双方的紧密协作，可以高效地完成这两个核心模块的开发。