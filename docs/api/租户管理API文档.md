# 租户管理 API 文档

本文档描述了多租户系统中租户管理相关的 API 接口，供前端开发人员使用。

## 通用说明

### 基础 URL

所有 API 请求的基础 URL 为：`/api`

### 认证方式

所有接口都需要在请求头中携带 JWT 令牌，且用户必须具有系统管理员权限：

```
Authorization: Bearer {token}
```

### 响应格式

所有接口的响应格式统一为：

```json
{
  "code": 0,         // 状态码，0 表示成功，非 0 表示失败
  "data": {},        // 响应数据
  "message": "操作成功" // 响应消息
}
```

## 租户管理接口

### 获取租户列表

- **URL**: `/tenant/list`
- **方法**: `GET`
- **描述**: 获取所有租户列表
- **请求参数**:
  - `page`: 页码，默认 1
  - `pageSize`: 每页数量，默认 10
  - `name`: 租户名称，模糊查询（可选）
  - `code`: 租户代码，模糊查询（可选）
  - `status`: 租户状态，0-禁用，1-启用（可选）
- **响应示例**:

```json
{
  "code": 0,
  "data": {
    "items": [
      {
        "id": 1,
        "code": "tenant1",
        "name": "租户1",
        "website": "https://tenant1.example.com",
        "status": 1,
        "createdAt": "2023-05-01T00:00:00.000Z",
        "updatedAt": "2023-05-01T00:00:00.000Z",
        "datasource": {
          "id": 1,
          "name": "租户1数据源",
          "url": "postgresql://user:password@localhost:5432/tenant1"
        }
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  },
  "message": "获取成功"
}
```

### 获取租户详情

- **URL**: `/tenant/:id`
- **方法**: `GET`
- **描述**: 获取指定ID的租户详情
- **响应示例**:

```json
{
  "code": 0,
  "data": {
    "id": 1,
    "code": "tenant1",
    "name": "租户1",
    "website": "https://tenant1.example.com",
    "status": 1,
    "metadata": {
      "industry": "IT",
      "size": "中型",
      "region": "华东"
    },
    "createdAt": "2023-05-01T00:00:00.000Z",
    "updatedAt": "2023-05-01T00:00:00.000Z",
    "datasource": {
      "id": 1,
      "name": "租户1数据源",
      "url": "postgresql://user:password@localhost:5432/tenant1"
    }
  },
  "message": "获取成功"
}
```

### 创建租户

- **URL**: `/tenant`
- **方法**: `POST`
- **描述**: 创建新租户
- **请求参数**:

```json
{
  "code": "tenant2",
  "name": "租户2",
  "website": "https://tenant2.example.com",
  "status": 1,
  "metadata": {
    "industry": "金融",
    "size": "大型",
    "region": "华北"
  },
  "datasource": {
    "name": "租户2数据源",
    "url": "postgresql://user:password@localhost:5432/tenant2"
  }
}
```

- **响应示例**:

```json
{
  "code": 0,
  "data": {
    "id": 2,
    "code": "tenant2",
    "name": "租户2",
    "website": "https://tenant2.example.com",
    "status": 1,
    "metadata": {
      "industry": "金融",
      "size": "大型",
      "region": "华北"
    },
    "createdAt": "2023-05-01T00:00:00.000Z",
    "updatedAt": "2023-05-01T00:00:00.000Z",
    "datasource": {
      "id": 2,
      "name": "租户2数据源",
      "url": "postgresql://user:password@localhost:5432/tenant2"
    }
  },
  "message": "创建成功"
}
```

### 更新租户

- **URL**: `/tenant/:id`
- **方法**: `PUT`
- **描述**: 更新指定ID的租户
- **请求参数**:

```json
{
  "name": "租户2更新",
  "website": "https://tenant2-new.example.com",
  "status": 1,
  "metadata": {
    "industry": "金融科技",
    "size": "大型",
    "region": "华北"
  }
}
```

- **响应示例**:

```json
{
  "code": 0,
  "data": {
    "id": 2,
    "code": "tenant2",
    "name": "租户2更新",
    "website": "https://tenant2-new.example.com",
    "status": 1,
    "metadata": {
      "industry": "金融科技",
      "size": "大型",
      "region": "华北"
    },
    "createdAt": "2023-05-01T00:00:00.000Z",
    "updatedAt": "2023-05-01T00:00:00.000Z"
  },
  "message": "更新成功"
}
```

### 删除租户

- **URL**: `/tenant/:id`
- **方法**: `DELETE`
- **描述**: 删除指定ID的租户
- **响应示例**:

```json
{
  "code": 0,
  "data": {
    "success": true
  },
  "message": "删除成功"
}
```

### 更新租户状态

- **URL**: `/tenant/:id/status`
- **方法**: `PATCH`
- **描述**: 更新指定ID的租户状态
- **请求参数**:

```json
{
  "status": 0  // 0-禁用，1-启用
}
```

- **响应示例**:

```json
{
  "code": 0,
  "data": {
    "id": 2,
    "status": 0
  },
  "message": "更新成功"
}
```

### 检查租户代码是否存在

- **URL**: `/tenant/code-exists`
- **方法**: `GET`
- **描述**: 检查租户代码是否已存在
- **请求参数**:
  - `code`: 租户代码
  - `id`: 排除的租户ID（可选）
- **响应示例**:

```json
{
  "code": 0,
  "data": false,
  "message": "检查成功"
}
```

## 数据源管理接口

### 获取数据源列表

- **URL**: `/datasource/list`
- **方法**: `GET`
- **描述**: 获取所有数据源列表
- **请求参数**:
  - `page`: 页码，默认 1
  - `pageSize`: 每页数量，默认 10
  - `name`: 数据源名称，模糊查询（可选）
- **响应示例**:

```json
{
  "code": 0,
  "data": {
    "items": [
      {
        "id": 1,
        "name": "租户1数据源",
        "url": "postgresql://user:password@localhost:5432/tenant1",
        "createdAt": "2023-05-01T00:00:00.000Z",
        "updatedAt": "2023-05-01T00:00:00.000Z"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  },
  "message": "获取成功"
}
```

### 创建数据源

- **URL**: `/datasource`
- **方法**: `POST`
- **描述**: 创建新数据源
- **请求参数**:

```json
{
  "name": "新数据源",
  "url": "postgresql://user:password@localhost:5432/new_db",
  "metadata": {
    "type": "PostgreSQL",
    "version": "14"
  }
}
```

- **响应示例**:

```json
{
  "code": 0,
  "data": {
    "id": 3,
    "name": "新数据源",
    "url": "postgresql://user:password@localhost:5432/new_db",
    "metadata": {
      "type": "PostgreSQL",
      "version": "14"
    },
    "createdAt": "2023-05-01T00:00:00.000Z",
    "updatedAt": "2023-05-01T00:00:00.000Z"
  },
  "message": "创建成功"
}
```

### 更新数据源

- **URL**: `/datasource/:id`
- **方法**: `PUT`
- **描述**: 更新指定ID的数据源
- **请求参数**:

```json
{
  "name": "更新数据源",
  "url": "postgresql://user:password@localhost:5432/updated_db",
  "metadata": {
    "type": "PostgreSQL",
    "version": "15"
  }
}
```

- **响应示例**:

```json
{
  "code": 0,
  "data": {
    "id": 3,
    "name": "更新数据源",
    "url": "postgresql://user:password@localhost:5432/updated_db",
    "metadata": {
      "type": "PostgreSQL",
      "version": "15"
    },
    "createdAt": "2023-05-01T00:00:00.000Z",
    "updatedAt": "2023-05-01T00:00:00.000Z"
  },
  "message": "更新成功"
}
```

### 删除数据源

- **URL**: `/datasource/:id`
- **方法**: `DELETE`
- **描述**: 删除指定ID的数据源
- **响应示例**:

```json
{
  "code": 0,
  "data": {
    "success": true
  },
  "message": "删除成功"
}
```

### 测试数据源连接

- **URL**: `/datasource/test-connection`
- **方法**: `POST`
- **描述**: 测试数据源连接是否可用
- **请求参数**:

```json
{
  "url": "postgresql://user:password@localhost:5432/test_db"
}
```

- **响应示例**:

```json
{
  "code": 0,
  "data": {
    "success": true,
    "message": "连接成功"
  },
  "message": "测试成功"
}
```
