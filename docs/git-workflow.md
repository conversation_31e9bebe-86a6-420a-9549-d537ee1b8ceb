# Git 工作流规范

本文档定义了项目的 Git 工作流程和代码提交规范。

## 分支命名规范

所有分支名称必须遵循以下格式：

```
<type>/<description>
```

其中：

- `<type>` 是分支类型，必须是以下之一：
  - `feature`: 新功能开发
  - `bugfix`: 修复 bug
  - `hotfix`: 紧急修复生产环境问题
  - `release`: 版本发布
  - `chore`: 项目维护任务
- `<description>` 是对分支内容的简短描述，使用小写字母和连字符，例如 `user-authentication`

示例：

- `feature/user-authentication`
- `bugfix/login-error`
- `hotfix/security-vulnerability`
- `release/v1.2.0`
- `chore/update-dependencies`

## 提交信息规范

所有提交信息必须遵循 [Conventional Commits](https://www.conventionalcommits.org/) 规范。

格式如下：

```
<type>(<scope>): <subject>

<body>

<footer>
```

其中：

- `<type>` 是提交类型，必须是以下之一：
  - `feat`: 新功能
  - `fix`: 修复 Bug
  - `docs`: 文档变更
  - `style`: 代码风格变更（不影响功能）
  - `refactor`: 代码重构（不新增功能或修复 Bug）
  - `perf`: 性能优化
  - `test`: 添加或修改测试
  - `build`: 构建相关变更
  - `ci`: CI 配置变更
  - `chore`: 其他不修改 src 或 test 的变更
  - `revert`: 回退提交
- `<scope>` 是可选的，表示本次提交影响的范围
- `<subject>` 是对本次提交的简短描述
- `<body>` 是可选的，对本次提交的详细描述
- `<footer>` 是可选的，包含关闭 issue 的引用或破坏性变更的说明

示例：

```
feat(auth): 添加用户认证功能

实现了基于 JWT 的用户认证系统，包括登录、注册和密码重置功能。

Closes #123
```

## 工作流程

1. **创建新分支**

   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **在本地开发和提交**

   ```bash
   git add .
   git commit -m "feat: 添加新功能"
   ```

3. **定期从主分支拉取更新**

   ```bash
   git fetch origin main
   git merge origin/main
   # 或者使用 rebase
   git rebase origin/main
   ```

4. **推送到远程仓库**

   ```bash
   git push origin feature/your-feature-name
   ```

5. **创建 Pull Request**

   - 在 GitHub/GitLab 上创建 PR，请求将你的分支合并到主分支
   - 指定适当的审阅者
   - 在 PR 描述中提供清晰的说明

6. **代码审查和合并**
   - 代码审查通过后，由项目维护者合并 PR
   - 合并后删除特性分支

## 代码审查清单

- 代码是否遵循项目的编码规范
- 是否包含适当的测试
- 是否有潜在的性能问题
- 是否有潜在的安全漏洞
- 是否有不必要的复杂性
- 是否有重复代码
- 是否有适当的错误处理
- 是否有适当的日志记录
- 提交信息是否清晰且符合规范
