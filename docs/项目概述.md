# Multi-tenant NestJS 项目概述

## 项目简介

这是一个基于 NestJS 框架的多租户（Multi-tenant）API 项目，使用 Prisma ORM 和 PostgreSQL 数据库。该项目实现了一个多租户系统，允许不同的租户（tenant）使用同一套 API，但数据存储在不同的数据库或同一数据库的不同 schema 中。

## 核心架构

### 多租户模型

- **双数据库连接**：
  - **Public 数据库**：存储租户元数据和数据源信息
  - **Tenant 数据库**：存储租户的实际业务数据

### Prisma 服务

- **PublicPrismaService**：连接到公共数据库，管理租户和数据源信息
- **TenantPrismaService**：连接到租户数据库，使用查询扩展确保数据隔离

### 中间件

- **TenantDatasourceMiddleware**：根据请求头中的 `x-tenant-code` 确定当前租户，并设置相应的数据源

### 依赖注入

- 使用 NestJS 的依赖注入系统，为每个请求创建特定租户的 Prisma 服务实例

## 数据模型

### 公共数据库模型

- **Tenant**：租户信息，包括代码、名称、网站等
- **Datasource**：数据源信息，包括名称、URL 等

### 租户数据库模型

- **User**：用户信息，包括电子邮件、姓名等
- 每个模型都包含 `tenantId` 字段，用于数据隔离

## 工作流程

1. 客户端发送请求，在请求头中包含 `x-tenant-code`
2. `TenantDatasourceMiddleware` 拦截请求，根据租户代码查找对应的数据源
3. 创建特定租户的 Prisma 服务实例，并应用查询扩展，确保只能访问该租户的数据
4. 控制器和服务使用注入的 Prisma 服务实例处理请求

## 特点

1. **数据隔离**：通过 Prisma 查询扩展，确保每个租户只能访问自己的数据
2. **灵活的数据源配置**：支持多种数据库配置方式
3. **模块化设计**：使用 NestJS 的模块系统，实现清晰的代码组织
4. **API 文档**：集成 Swagger 文档

## 使用方法

1. 创建 `.env` 文件，配置数据库连接
   ```
   PUBLIC_DATABASE_URL="postgresql://johndoe:randompassword@localhost:5432/mydb?schema=public"
   DATABASE_URL="postgresql://johndoe:randompassword@localhost:5432/mydb?schema=tenant"
   ```

2. 安装依赖：
   ```bash
   pnpm install
   ```

3. 运行开发服务器：
   ```bash
   pnpm dev
   ```

4. 访问 API 时，在请求头中添加 `x-tenant-code` 指定租户

## 数据库管理

- 更新公共数据库架构：
  ```bash
  pnpm prisma:push:public
  ```

- 更新租户数据库架构：
  ```bash
  pnpm prisma:push:tenant
  ```
