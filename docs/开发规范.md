# 多租户 NestJS 项目开发规范

## 1. 代码风格与格式

### 1.1 ESLint 规则遵循

本项目使用 ESLint 进行代码质量和风格检查。所有代码必须通过 ESLint 检查，不允许使用 `eslint-disable` 注释绕过规则，除非有特殊情况并经过团队讨论。

#### 基本规则

- 使用 2 个空格缩进
- 使用单引号 `'` 而非双引号 `"`
- 每行末尾不留多余空格
- 文件末尾保留一个空行
- 操作符两侧添加空格
- 逗号后面添加空格
- 代码块大括号使用一致的风格

#### 运行 ESLint 检查

```bash
# 检查代码
pnpm lint

# 自动修复可修复的问题
pnpm lint:fix
```

### 1.2 代码格式化

使用 Prettier 进行代码格式化，确保团队代码风格一致。

```bash
# 格式化代码
pnpm format
```

建议在编辑器中配置保存时自动格式化。

## 2. TypeScript 类型规范

### 2.1 避免使用 `any` 类型

禁止不必要地使用 `any` 类型。使用 `any` 会绕过 TypeScript 的类型检查，失去静态类型的优势。

#### 不推荐的写法

```typescript
// ❌ 不推荐
function processData(data: any): any {
  return data.map(item => item.value);
}

// ❌ 不推荐
const config: any = {
  apiUrl: 'https://api.example.com',
  timeout: 5000
};
```

#### 推荐的写法

```typescript
// ✅ 推荐
interface DataItem {
  value: string;
  id: number;
}

function processData(data: DataItem[]): string[] {
  return data.map(item => item.value);
}

// ✅ 推荐
interface Config {
  apiUrl: string;
  timeout: number;
}

const config: Config = {
  apiUrl: 'https://api.example.com',
  timeout: 5000
};
```

### 2.2 使用适当的类型替代 `any`

当不确定具体类型时，使用以下替代方案：

1. **使用 `unknown`**：当不确定输入类型时，使用 `unknown` 而非 `any`。`unknown` 类型的值必须经过类型检查才能使用。

```typescript
function processInput(input: unknown): string {
  if (typeof input === 'string') {
    return input.toUpperCase();
  }
  return String(input);
}
```

2. **使用泛型**：当函数需要处理多种类型时，使用泛型而非 `any`。

```typescript
function identity<T>(arg: T): T {
  return arg;
}
```

3. **使用联合类型**：当值可能是几种类型之一时，使用联合类型。

```typescript
function formatValue(value: string | number): string {
  return String(value);
}
```

4. **使用类型断言**：在确定类型但 TypeScript 无法推断时，使用类型断言。

```typescript
const element = document.getElementById('app') as HTMLDivElement;
```

### 2.3 定义明确的接口和类型

为所有数据结构定义明确的接口或类型别名：

```typescript
// DTO 定义
export class CreateTenantDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  code: string;

  @IsString()
  @IsOptional()
  website?: string;
}

// 实体类型定义
export interface Tenant {
  id: number;
  name: string;
  code: string;
  website?: string;
  createdAt: Date;
  updatedAt: Date;
}
```

## 3. NestJS 最佳实践

### 3.1 模块组织

- 每个功能模块应该有自己的目录
- 模块目录应包含控制器、服务、实体、DTO 等相关文件
- 使用 NestJS CLI 创建模块和组件

```
src/modules/tenants/
├── dto/
│   ├── create-tenant.dto.ts
│   └── update-tenant.dto.ts
├── entities/
│   └── tenant.entity.ts
├── tenants.controller.ts
├── tenants.module.ts
├── tenants.service.ts
└── tests/
    ├── tenants.controller.spec.ts
    └── tenants.service.spec.ts
```

### 3.2 依赖注入

- 使用构造函数注入依赖
- 使用接口和令牌进行抽象
- 避免使用全局模块，除非必要

```typescript
@Injectable()
export class TenantsService {
  constructor(
    @Inject(TENANT_PRISMA_SERVICE) private readonly prisma: TenantPrismaService,
    private readonly configService: ConfigService,
  ) {}
}
```

### 3.3 异常处理

- 使用 NestJS 内置的异常类
- 创建自定义异常过滤器处理特定异常
- 提供有意义的错误消息

```typescript
// 抛出适当的异常
if (!tenant) {
  throw new NotFoundException(`Tenant with code ${tenantCode} not found`);
}

// 自定义异常过滤器
@Catch(PrismaClientKnownRequestError)
export class PrismaExceptionFilter implements ExceptionFilter {
  catch(exception: PrismaClientKnownRequestError, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();

    // 处理特定的 Prisma 错误
    if (exception.code === 'P2002') {
      response.status(409).json({
        statusCode: 409,
        message: 'Resource already exists',
      });
    } else {
      response.status(500).json({
        statusCode: 500,
        message: 'Internal server error',
      });
    }
  }
}
```

## 4. 多租户特定规范

### 4.1 租户数据隔离

- 所有租户相关的数据模型必须包含 `tenantId` 字段
- 使用 `TenantPrismaService` 的查询扩展确保数据隔离
- 不要使用原始 SQL 查询绕过租户过滤

```typescript
// ✅ 推荐：使用 TenantPrismaService
const users = await this.prisma.user.findMany({
  where: {
    email: { contains: searchTerm }
    // tenantId 会通过查询扩展自动添加
  }
});

// 中文注释示例
// 查询活跃用户
// 注意：这里使用了 Prisma 的查询扩展，会自动添加 tenantId 过滤条件
// 确保数据隔离，每个租户只能查询自己的用户数据
const activeUsers = await this.prisma.user.findMany({
  where: {
    isActive: true,
    lastLoginAt: {
      gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30天内登录过的用户
    }
  }
});

// ❌ 不推荐：使用原始 SQL 查询
const users = await this.prisma.$queryRaw`
  SELECT * FROM "User" WHERE "email" LIKE ${searchTerm}
`;
```

### 4.2 租户识别

- 所有需要租户上下文的 API 端点必须从请求中获取租户信息
- 使用中间件或守卫验证租户存在性
- 在服务层不要硬编码租户 ID

```typescript
// ✅ 推荐：从请求中获取租户信息
@Get('users')
async getUsers(@Req() request: IRequestWithProps) {
  const { tenant } = request;
  return this.usersService.findAll();
}

// ❌ 不推荐：硬编码租户 ID
@Get('users')
async getUsers() {
  return this.usersService.findAllForTenant('tenant123');
}
```

## 5. 测试规范

### 5.1 单元测试

- 每个服务和控制器都应有单元测试
- 使用 Jest 的模拟功能隔离依赖
- 测试覆盖率目标：80%以上

```typescript
describe('TenantsService', () => {
  let service: TenantsService;
  let prismaMock: MockType<TenantPrismaService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TenantsService,
        {
          provide: TENANT_PRISMA_SERVICE,
          useFactory: () => ({
            tenant: {
              findUnique: jest.fn(),
              create: jest.fn(),
              update: jest.fn(),
              delete: jest.fn(),
            },
          }),
        },
      ],
    }).compile();

    service = module.get<TenantsService>(TenantsService);
    prismaMock = module.get(TENANT_PRISMA_SERVICE);
  });

  it('should find a tenant by code', async () => {
    const mockTenant = { id: 1, code: 'test', name: 'Test Tenant' };
    prismaMock.tenant.findUnique.mockResolvedValue(mockTenant);

    expect(await service.findByCode('test')).toEqual(mockTenant);
    expect(prismaMock.tenant.findUnique).toHaveBeenCalledWith({
      where: { code: 'test' },
    });
  });
});
```

### 5.2 端到端测试

- 为关键 API 端点编写端到端测试
- 使用测试数据库环境
- 测试多租户场景，确保数据隔离

## 6. 文档规范

### 6.1 代码注释

- 使用 JSDoc 风格的注释
- 为所有公共 API 方法添加注释
- 解释复杂的业务逻辑
- **关键代码必须添加中文注释**，确保团队成员能清晰理解实现逻辑

```typescript
/**
 * 查找指定租户的所有用户
 * @param options - 查询选项
 * @param options.where - 过滤条件
 * @param options.orderBy - 排序条件
 * @param options.pagination - 分页参数
 * @returns 用户列表和总数
 */
async findUsers(options: FindUsersOptions): Promise<UsersResult> {
  // 获取查询参数
  const { where, orderBy, pagination } = options;

  // 构建查询条件
  const filter = this.buildFilter(where);

  // 查询总记录数
  // 注意：这里需要先查询总数，再查询分页数据，避免数据不一致
  const total = await this.prisma.user.count({ where: filter });

  // 查询用户列表
  // 这里应用了分页参数，确保大数据量时的性能
  const users = await this.prisma.user.findMany({
    where: filter,
    orderBy,
    skip: pagination.skip,
    take: pagination.take,
  });

  return { users, total };
}
```

### 6.2 API 文档

- 使用 Swagger 装饰器记录 API
- 为所有 DTO 添加属性描述
- 包含请求示例和响应示例

```typescript
@ApiTags('tenants')
@Controller('tenants')
export class TenantsController {
  @Post()
  @ApiOperation({ summary: '创建新租户' })
  @ApiResponse({ status: 201, description: '租户创建成功', type: TenantDto })
  @ApiResponse({ status: 400, description: '无效的输入' })
  create(@Body() createTenantDto: CreateTenantDto) {
    return this.tenantsService.create(createTenantDto);
  }
}
```

## 7. 提交规范

### 7.1 Git 提交消息

使用约定式提交规范（Conventional Commits）：

```text
<类型>[可选的作用域]: <描述>

[可选的正文]

[可选的脚注]
```

类型包括：

- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更改
- `style`: 不影响代码含义的更改（空格、格式化等）
- `refactor`: 既不修复 bug 也不添加功能的代码更改
- `perf`: 改进性能的代码更改
- `test`: 添加或修正测试
- `chore`: 对构建过程或辅助工具的更改

示例：

```text
feat(tenant): 添加租户创建功能

- 实现租户创建 API
- 添加租户验证
- 更新文档

Closes #123
```

## 8. 安全规范

### 8.1 数据验证

- 使用 class-validator 验证所有输入数据
- 不信任客户端输入，始终进行服务器端验证
- 使用 DTO 类进行数据转换和验证

```typescript
export class CreateUserDto {
  @IsString()
  @IsEmail()
  @ApiProperty({ example: '<EMAIL>' })
  email: string;

  @IsString()
  @MinLength(8)
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, {
    message: '密码必须包含至少一个小写字母、一个大写字母和一个数字',
  })
  @ApiProperty({ example: 'Password123' })
  password: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: 'John Doe' })
  name: string;
}
```

### 8.2 租户数据安全

- 确保租户只能访问自己的数据
- 使用中间件和守卫验证租户权限
- 记录敏感操作的审计日志

## 9. 性能优化

- 使用适当的索引优化数据库查询
- 实现缓存机制减少数据库负载
- 分页处理大量数据
- 避免 N+1 查询问题，使用 Prisma 的 `include` 功能

```typescript
// ✅ 推荐：使用 include 避免 N+1 查询
const tenants = await this.prisma.tenant.findMany({
  include: { datasource: true },
});

// ❌ 不推荐：导致 N+1 查询
const tenants = await this.prisma.tenant.findMany();
for (const tenant of tenants) {
  tenant.datasource = await this.prisma.datasource.findUnique({
    where: { id: tenant.datasourceId },
  });
}
```
