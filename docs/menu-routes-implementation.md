# 前端路由与后端菜单权限实现文档

本文档详细说明了如何将前端路由配置与后端菜单权限系统集成，以实现基于角色的动态路由控制。

## 1. 概述

系统采用了前后端分离的架构，前端路由配置需要与后端菜单权限系统保持一致。本次实现将前端路由配置同步到后端数据库，并按角色权限区分分配菜单和权限。

## 2. 前端路由结构

前端路由配置采用以下结构：

```json
{
  "routes": [
    {
      "name": "Dashboard",
      "path": "/dashboard",
      "component": "BasicLayout",
      "meta": {
        "icon": "lucide:layout-dashboard",
        "order": -1,
        "title": "仪表盘"
      },
      "children": [...]
    }
  ]
}
```

每个路由项包含以下关键字段：

- `name`: 路由名称，用于前端路由跳转
- `path`: 路由路径
- `component`: 组件路径或布局类型
- `meta`: 元数据，包含图标、排序、标题等信息
- `children`: 子路由数组

## 3. 后端菜单数据结构

系统采用了多租户架构，菜单分为系统菜单和租户菜单两种类型：

### 3.1 系统菜单表结构

系统菜单存储在 `SystemMenu` 表中，由系统管理员使用：

```typescript
model SystemMenu {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  name      String   // 菜单名称
  path      String   // 路由路径
  component String   // 组件路径
  type      String   // 菜单类型：menu, button, directory
  icon      String?  // 图标
  permission String? // 权限标识
  pid       Int?     // 父菜单ID
  orderNo   Int      @default(0) // 排序号
  status    Int      @default(1) // 状态：0-禁用，1-启用
  meta      String?  // 元数据，JSON格式

  // 关联
  parent    SystemMenu?  @relation("MenuToMenu", fields: [pid], references: [id], onDelete: SetNull)
  children  SystemMenu[] @relation("MenuToMenu")
  roleMenus SystemRoleMenu[]
}
```

### 3.2 租户菜单表结构

租户菜单存储在 `Menu` 表中，由租户用户使用：

```typescript
model Menu {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  name      String   // 菜单名称
  path      String   // 路由路径
  component String   // 组件路径
  type      String   // 菜单类型：menu, button, directory
  icon      String?  // 图标
  permission String? // 权限标识
  pid       Int?     // 父菜单ID
  orderNo   Int      @default(0) // 排序号
  status    Int      @default(1) // 状态：0-禁用，1-启用
  meta      String?  // 元数据，JSON格式
  tenantId  Int      // 租户ID

  // 关联
  tenant    Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  parent    Menu?     @relation("MenuToMenu", fields: [pid], references: [id], onDelete: SetNull)
  children  Menu[]    @relation("MenuToMenu")
  roleMenus RoleMenu[]
}
```

## 4. 实现步骤

### 4.1 更新菜单配置

在 `prisma/seed-config.js` 文件中，我们添加了与前端路由对应的菜单配置：

```javascript
systemMenus: {
  // 仪表盘
  dashboard: {
    name: 'Dashboard',
    path: '/dashboard',
    component: 'BasicLayout',
    type: 'menu',
    icon: 'lucide:layout-dashboard',
    orderNo: -1,
    status: 1,
    meta: {
      title: '仪表盘',
      icon: 'lucide:layout-dashboard',
      order: -1,
    },
  },
  // 仪表盘子菜单...
}
```

### 4.2 更新权限配置

添加了与菜单对应的权限配置：

```javascript
permissions: [
  // 用户管理权限
  { name: '用户查询', code: 'system:user:list', description: '查询用户列表' },
  // 更多权限...
]
```

### 4.3 修改初始化脚本

在 `prisma/seed.js` 文件中，我们修改了菜单和权限的创建和分配逻辑：

1. **创建菜单**：
   - 创建所有菜单并存储菜单ID
   - 设置正确的父子关系
   - 将元数据转换为JSON字符串

2. **按角色分配菜单**：
   - 超级管理员角色：分配所有菜单
   - 测试管理员角色：只分配仪表盘和部分系统管理菜单

3. **创建权限**：
   - 创建所有权限并存储权限ID

4. **按角色分配权限**：
   - 超级管理员角色：分配所有权限
   - 测试管理员角色：只分配查询权限和部分管理权限

## 5. 角色权限区分

系统中的角色权限分为系统角色权限和租户角色权限两部分。

### 5.1 系统角色权限

#### 5.1.1 超级管理员角色

- **菜单权限**：拥有所有系统菜单的访问权限
- **操作权限**：拥有所有系统操作权限（查询、创建、编辑、删除）

#### 5.1.2 测试管理员角色

- **菜单权限**：
  - 仪表盘（包括分析页和工作台）
  - 系统管理（仅用户管理和角色管理）

- **操作权限**：
  - 所有模块的查询权限
  - 用户和角色模块的完整操作权限（查询、创建、编辑、删除）

### 5.2 租户角色权限

租户角色权限不再在初始化脚本中硬编码，而是通过以下方式动态管理：

1. **系统管理员分配**：
   - 系统管理员可以为每个租户分配不同的菜单权限
   - 系统管理员可以设置租户默认角色的权限

2. **租户管理员分配**：
   - 租户管理员可以在分配给租户的菜单范围内，为租户用户分配权限
   - 租户管理员不能分配超出租户权限范围的菜单

#### 5.2.1 租户角色权限建议

**租户管理员角色**：

- **建议菜单权限**：
  - 仪表盘（包括分析页和工作台）
  - 租户管理（显示为"功能中心"，包括功能看板、配置中心）
  - 系统管理（显示为"用户中心"，包括用户管理、角色管理）

- **建议操作权限**：拥有所有租户操作权限（查询、创建、编辑、删除）

**租户普通用户角色**：

- **建议菜单权限**：
  - 仪表盘（包括分析页和工作台）
  - 租户管理（显示为"功能中心"，仅功能看板，无配置中心）

- **建议操作权限**：
  - 基本的查询权限
  - 无管理权限

## 6. 前端路由与后端菜单的映射关系

### 6.1 系统菜单映射

| 前端路由名称 | 后端菜单名称 | 显示标题 | 权限代码 |
|------------|------------|---------|---------|
| Dashboard | Dashboard | 仪表盘 | - |
| Analytics | Analytics | 分析页 | - |
| Workspace | Workspace | 工作台 | - |
| System | System | 系统管理 | - |
| SystemRole | SystemRole | 角色管理 | system:role:list |
| SystemMenu | SystemMenu | 菜单管理 | system:menu:list |
| SystemDept | SystemDept | 部门管理 | system:dept:list |
| SystemTenant | SystemTenant | 租户管理 | system:tenant:list |
| SystemUser | SystemUser | 用户管理 | system:user:list |
| SystemFeatureTemplate | SystemFeatureTemplate | 功能模板管理 | system:feature-template:list |
| SystemFeatureCode | SystemFeatureCode | 功能代码管理 | system:feature-code:list |
| TenantManagement | TenantManagement | 租户 | - |
| TenantFeatures | TenantFeatures | 功能看板 | tenant:features:list |
| TenantConfigs | TenantConfigs | 配置中心 | tenant:configs:list |

### 6.2 租户菜单实现

租户菜单不再在初始化脚本中硬编码，而是通过以下方式动态生成：

1. **基于系统菜单自动生成**：
   - 在租户创建时，系统会自动基于系统菜单生成租户菜单
   - 保持路径和权限代码与系统菜单一致，只修改显示标题

2. **标题映射**：
   - 系统中维护一个路径到显示标题的映射表
   - 例如：`/tenant` 路径显示为 "功能中心"，`/system` 路径显示为 "用户中心"

3. **权限控制**：
   - 租户菜单的权限由系统管理员统一管理
   - 租户管理员只能分配已有的菜单权限，不能创建新菜单

#### 租户菜单标题映射示例

| 系统路径 | 租户显示标题 |
|---------|------------|
| /tenant | 功能中心 |
| /system | 用户中心 |

## 7. 使用方法

### 7.1 初始化数据库

运行以下命令初始化数据库：

```bash
npm run db:reset
```

这将执行 `seed.js` 脚本，创建所有菜单和权限，并按角色分配。

### 7.2 前端获取菜单

前端通过以下API获取当前用户的菜单：

```http
GET /api/menu/all
```

返回的菜单数据将根据用户角色进行过滤，只包含用户有权访问的菜单。

### 7.3 前端动态路由生成

前端使用获取到的菜单数据生成动态路由：

```javascript
// 示例代码
const generateRoutes = (menus) => {
  return menus.map(menu => ({
    path: menu.path,
    name: menu.name,
    component: resolveComponent(menu.component),
    meta: menu.meta,
    children: menu.children ? generateRoutes(menu.children) : undefined
  }));
};

// 添加路由
const routes = generateRoutes(menus);
routes.forEach(route => router.addRoute(route));
```

## 8. 注意事项

1. **菜单组件路径**：
   - `BasicLayout` 表示使用基础布局
   - 其他路径表示具体的页面组件

2. **权限代码**：
   - 权限代码格式为 `模块:资源:操作`
   - 例如：`system:user:list` 表示系统模块下用户资源的查询操作

3. **菜单状态**：
   - 菜单状态为 1 表示启用
   - 菜单状态为 0 表示禁用

4. **菜单排序**：
   - 使用 `orderNo` 字段控制同级菜单的排序
   - 数值越小，排序越靠前

## 9. 未来优化

1. **菜单缓存优化**：
   - 实现更高效的菜单缓存机制
   - 在菜单更新时自动清除缓存

2. **权限粒度优化**：
   - 实现更细粒度的权限控制
   - 支持数据权限控制

3. **动态权限配置**：
   - 实现在线配置角色权限
   - 支持权限继承和覆盖
