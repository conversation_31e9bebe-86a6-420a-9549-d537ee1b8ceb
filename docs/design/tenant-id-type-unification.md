# 租户ID类型统一设计文档

## 文档信息

- **文档版本**：1.0
- **创建日期**：2025-05-15
- **最后更新**：2025-05-15
- **状态**：已实现

## 1. 概述

### 1.1 目的

本文档描述了多租户系统中租户ID类型统一的设计决策和实现细节。通过将所有 `tenantId` 字段从 `String` 类型改为 `Int` 类型，提高数据一致性和查询性能。

### 1.2 背景

在系统初始设计中，`tenantId` 字段使用字符串类型，存储的是租户代码（如 "tenant1"）。这导致了以下问题：

1. 数据不一致：租户表中的主键是数字类型的 `id`，而其他表中的 `tenantId` 是字符串类型，存储的是租户代码
2. 查询性能：字符串类型的外键查询性能低于数字类型
3. 代码混淆：在代码中，`tenantId` 有时指租户的数字ID，有时指租户代码，导致混淆

### 1.3 解决方案

将所有表中的 `tenantId` 字段从 `String` 类型改为 `Int` 类型，存储租户的数字ID而不是租户代码。同时，在需要租户代码的地方（如日志、缓存键等），使用 `tenantCode` 字段。

## 2. 设计详情

### 2.1 数据库模型修改

#### 2.1.1 租户数据库模型 (schema.prisma)

将所有表中的 `tenantId` 字段从 `String` 类型改为 `Int` 类型：

```prisma
model User {
  id           Int       @id @default(autoincrement())
  // 其他字段...
  tenantId     Int       // 租户ID（数字）
  // 关联字段...
}

model Role {
  id           String    @id @default(uuid())
  // 其他字段...
  tenantId     Int       // 租户ID（数字）
  // 关联字段...
}

// 其他模型...
```

### 2.2 实体类修改

修改所有实体类中的 `tenantId` 类型，从 `string` 改为 `number`：

```typescript
export class User implements Partial<IUser> {
  // 其他字段...
  @ApiProperty()
  tenantId: number;
  // 其他字段...
}
```

### 2.3 JWT载荷修改

修改 `JwtPayload` 接口，同时包含 `tenantId`（数字ID）和 `tenantCode`（字符串代码）：

```typescript
export interface JwtPayload {
  sub: string;
  username: string;
  userType: string;
  tenantId: number; // 租户的数字ID的字符串表示
  tenantCode: string; // 租户代码
}
```

### 2.4 请求对象修改

修改 `IRequestWithProps` 接口，添加 `tenantId` 字段：

```typescript
export interface IRequestWithProps extends Request {
  // 其他字段...
  tenant?: {
    tenantId?: number; // 租户ID - 用于数据库关系和内部操作
    tenantCode?: string; // 租户代码 - 用于识别租户和日志
    datasourceUrl?: string; // 数据源URL
  };
}
```

### 2.5 租户数据库服务修改

修改 `TenantPrismaService` 的 `withQueryExtensions` 方法，使用租户的数字ID：

```typescript
withQueryExtensions(tenantId: number) {
  return this.$extends({
    query: {
      $allOperations({ model, operation, args, query }) {
        // 只对查询操作添加租户ID过滤
        if (operation === 'findMany' || operation === 'findFirst' || operation === 'findUnique' || operation === 'count') {
          // 添加租户ID过滤
          return query({
            ...args,
            where: {
              ...args.where,
              tenantId: tenantId, // 使用租户的数字ID
            },
          });
        }

        // 对于更新和删除操作，不修改查询
        return query(args);
      },
    },
  });
}
```

### 2.6 认证策略修改

在认证策略中添加类型转换，将字符串类型的 `tenantId` 转换为数字类型：

```typescript
// 将 tenantId 转换为数字进行比较，因为数据库中的 tenantId 是数字类型
if (user.tenantId !== parseInt(tenantId)) {
  this.logger.warn(`用户 ${user.id} 的租户ID ${user.tenantId} 与请求的租户ID ${tenantId} 不匹配`);
  throw new UnauthorizedException(ApiMessage[ApiCode.PERMISSION_DENIED]);
}
```

### 2.7 缓存键生成修改

修改缓存键生成方法，同时包含租户ID和租户代码：

```typescript
private generateRoleCacheKey(userType: string, tenantId?: number, tenantCode?: string): string {
  return `role:${userType}:${tenantId || 'system'}:${tenantCode || ''}:list`;
}
```

## 3. 迁移策略

### 3.1 数据库迁移

使用 Prisma 的迁移功能，生成并应用迁移脚本：

```bash
npx prisma migrate dev --name change_tenant_id_to_int
```

### 3.2 代码迁移

1. 修改数据库模型
2. 修改实体类
3. 修改认证策略
4. 修改缓存键生成
5. 修改 seed.js 文件

## 4. 影响分析

### 4.1 正面影响

1. **数据一致性**：所有表中的 `tenantId` 字段都是数字类型，与租户表的 `id` 字段类型一致
2. **查询性能**：数字类型的外键查询性能优于字符串类型
3. **代码清晰度**：明确区分了租户ID和租户代码，减少混淆
4. **缓存效率**：缓存键同时包含租户ID和租户代码，提高缓存的准确性

### 4.2 潜在风险

1. **数据迁移**：需要进行数据库迁移，可能会影响现有数据
2. **代码兼容性**：需要修改所有使用 `tenantId` 的代码，确保类型一致
3. **API兼容性**：如果API返回 `tenantId`，可能需要保持向后兼容

## 5. 结论

通过将所有 `tenantId` 字段从 `String` 类型改为 `Int` 类型，我们提高了数据一致性和查询性能，同时减少了代码混淆。这是一个重要的架构改进，为未来的系统扩展和性能优化奠定了基础。

## 附录

### A. 修改文件列表

1. `prisma/schema.prisma`
2. `src/modules/user/entities/*.entity.ts`
3. `src/core/auth/strategies/tenant-auth.strategy.ts`
4. `src/core/middleware/tenant-datasource.middleware.ts`
5. `src/core/database/prisma/tenant-prisma.service.ts`
6. `src/core/database/prisma/prisma.module.ts`
7. `src/modules/role/role.service.ts`
8. `src/modules/menu/menu.service.ts`
9. `prisma/seed.js`

### B. 类型转换示例

```typescript
// 字符串转数字
const tenantIdNumber = parseInt(tenantId);

// 数字转字符串
const tenantIdString = tenantId.toString();
```
