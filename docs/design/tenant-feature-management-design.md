# 多租户系统功能权限管理设计文档

## 文档信息

- **文档版本**：1.0
- **创建日期**：2025-05-15
- **最后更新**：2025-05-16
- **状态**：实施中

## 1. 概述

### 1.1 目的

本文档旨在详细规划多租户系统的功能权限管理模块，采用专用功能表方案（方案2）实现租户功能权限控制、配置管理和使用统计。

### 1.2 背景

当前系统已实现基础的多租户架构，包括系统用户和租户用户的管理、角色权限控制、菜单管理等功能。为了支持AI模块、会员功能和支付功能等新业务模块的灵活配置和权限控制，需要设计一个可扩展的租户功能权限管理系统。

### 1.3 范围

本文档涵盖以下内容：

- 租户功能权限数据模型设计
- 功能权限服务实现
- 功能守卫和装饰器设计
- 缓存策略和性能优化
- 与会员系统和权限系统的集成

## 2. 系统设计

### 2.1 数据模型设计 ✅

采用专用功能表方案，创建`TenantFeature`表存储租户功能权限信息：

```prisma
// 租户功能权限表 ✅
model TenantFeature {
  id           Int       @id @default(autoincrement())
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  tenantId     Int       // 租户ID
  featureCode  String    // 功能代码，如 "ai.ppt", "payment"
  enabled      Boolean   @default(false) // 是否启用
  expiresAt    DateTime? // 过期时间
  quota        Int?      // 使用配额（null表示无限制）
  usedQuota    Int       @default(0) // 已使用配额
  config       Json?     // 功能配置（非敏感信息）

  tenant       Tenant    @relation(fields: [tenantId], references: [id])

  @@unique([tenantId, featureCode])
  @@index([featureCode])
  @@index([enabled])
  @@index([expiresAt])
}

// 租户功能使用记录表 ✅
model TenantFeatureUsage {
  id           Int       @id @default(autoincrement())
  createdAt    DateTime  @default(now())
  tenantId     Int       // 租户ID
  featureCode  String    // 功能代码
  userId       Int?      // 使用用户ID
  metadata     Json?     // 使用相关数据

  @@index([tenantId, featureCode])
  @@index([createdAt])
  @@index([userId])
}

// 功能配置模板表 ✅
model FeatureTemplate {
  id           Int       @id @default(autoincrement())
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  code         String    @unique // 模板代码，如 "basic", "pro"
  name         String    // 模板名称
  features     Json      // 包含的功能配置
  isActive     Boolean   @default(true)
}

// 租户配置表 ✅
model TenantConfig {
  id           Int       @id @default(autoincrement())
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  tenantId     Int       // 租户ID
  category     String    // 配置类别，如 "email", "sms"
  key          String    // 配置键
  value        String    // 配置值
  encrypted    Boolean   @default(false) // 是否加密

  @@unique([tenantId, category, key])
  @@index([tenantId, category])
}
```

### 2.2 功能代码设计 ✅

功能代码采用层级结构，便于组织和管理：

```text
[模块].[子功能]
```

例如：

- `ai.ppt` - AI PPT生成功能
- `ai.document` - AI文档生成功能
- `payment` - 支付功能
- `membership` - 会员功能
- `tenant-config.email` - 租户邮件配置功能

### 2.3 服务层设计 ✅

核心服务包括：

- `TenantFeatureService` - 管理租户功能权限 ✅
- `FeatureTemplateService` - 管理功能模板 ✅
- `TenantConfigService` - 管理租户配置 ✅
- `FeatureGuard` - 功能访问守卫 ✅
- `FeatureQuotaGuard` - 功能配额守卫 ✅

### 2.4 功能守卫和装饰器 ✅

```typescript
// 功能装饰器
export const RequireFeatures = (...features: string[]) => SetMetadata('features', features);

// 功能配额装饰器
export const CheckQuota = (feature: string) => SetMetadata('quota-feature', feature);
```

## 3. 与其他模块集成

### 3.1 与会员系统集成

会员系统可以通过功能模板来管理不同会员等级的功能权限：

```typescript
// 在会员订阅时应用功能模板
await this.tenantFeatureService.applyFeatureTemplate(tenantId, plan.code);
```

### 3.2 与权限系统集成

权限系统需要考虑功能权限的影响：

```typescript
// 检查权限时先验证功能是否开启
const featureEnabled = await this.tenantFeatureService.hasFeature(tenantId, feature);
if (!featureEnabled) {
  return false; // 功能未开启，直接拒绝
}
```

### 3.3 在业务模块中使用

```typescript
@Controller('ai/ppt')
@RequireFeatures('ai.ppt')
export class AiPptController {
  @Post('generate')
  @CheckQuota('ai.ppt')
  async generatePpt() {
    // 实现逻辑
  }
}
```

## 4. 缓存策略与性能优化

### 4.1 多级缓存策略 ✅

- 内存缓存：高频访问的功能状态 ✅
- Redis缓存：功能配置和模板 ✅
- 用户会话缓存：预加载常用功能权限 ⏳

### 4.2 批量预加载 ⏳

用户登录时预加载功能权限：

```typescript
// 将功能权限添加到令牌中
payload.features = featureMap;
```

## 5. 前端集成 ⏳

### 5.1 功能权限检查Hook ⏳

```typescript
export function useFeature(featureCode: string) {
  // 检查用户令牌中的功能权限
  const hasFeature = user?.features?.[featureCode] === true;
  return { hasFeature };
}
```

### 5.2 功能条件渲染组件 ⏳

```tsx
export function FeatureGuard({ featureCode, fallback = null, children }) {
  const { hasFeature } = useFeature(featureCode);
  if (!hasFeature) return fallback;
  return <>{children}</>;
}
```

## 6. 实施计划

1. **第一阶段**：基础设施（1周）✅
   - 数据模型设计与实现 ✅
   - 核心服务框架搭建 ✅
   - 功能守卫和装饰器实现 ✅
2. **第二阶段**：API和缓存（1周）✅
   - 功能模板管理API ✅
   - 租户功能管理API ✅
   - 租户配置管理API ✅
   - 缓存机制实现 ✅
3. **第三阶段**：与其他模块集成（1周）⏳
   - 与会员系统集成 ⏳
   - 与权限系统集成 ⏳
   - 在业务模块中使用 ⏳
4. **第四阶段**：前端集成（1周）⏳
   - 功能权限检查Hook ⏳
   - 功能条件渲染组件 ⏳
   - 管理界面开发 ⏳
5. **第五阶段**：测试和优化（1周）⏳
   - 性能测试 ⏳
   - 安全测试 ⏳
   - 用户体验优化 ⏳

## 7. 功能代码管理 ✅

功能代码是系统的基础配置数据，对于系统的正常运行至关重要。我们实现了一套完整的功能代码数据管理方案，确保功能代码数据在数据库初始化和迁移过程中的安全和可恢复性。

### 7.1 功能代码表设计 ✅

```prisma
// 功能代码表
model FeatureCode {
  id           Int       @id @default(autoincrement())
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  code         String    @unique // 功能代码，如 "ai.ppt", "payment"
  name         String    // 功能名称
  description  String    // 功能描述
  module       String    // 所属模块
  isActive     Boolean   @default(true) // 是否激活
  sortOrder    Int       @default(0) // 排序顺序
  metadata     Json?     // 额外元数据

  // 关联租户功能
  tenantFeatures TenantFeature[]
  usageRecords   TenantFeatureUsage[]

  @@index([module])
  @@index([isActive])
}
```

### 7.2 功能代码管理工具 ✅

系统提供了以下工具来管理功能代码数据：

1. **种子文件**：`prisma/seeds/feature-codes.js` 定义了初始功能代码数据
2. **导出工具**：`scripts/export-feature-codes.js` 从数据库导出功能代码数据
3. **导入工具**：`scripts/import-feature-codes.js` 从备份文件恢复功能代码数据

这些工具可以通过以下命令使用：

```bash
# 执行种子脚本，初始化数据库
pnpm db:seed

# 导出功能代码数据
pnpm db:export-feature-codes

# 导入功能代码数据
pnpm db:import-feature-codes ./backups/feature-codes-xxx.json
```

### 7.3 功能代码管理最佳实践 ✅

1. **定期备份**：每周或每次重要更新后执行导出命令
2. **版本控制**：将种子文件和备份文件纳入版本控制系统
3. **环境迁移**：使用导出和导入工具在环境间迁移数据
4. **谨慎修改**：避免修改已有功能代码的 `code` 字段

详细的功能代码管理指南请参考 [功能代码管理指南](../feature-codes-management.md)。

## 8. 结论

本设计采用专用功能表方案实现租户功能权限管理，具有以下优势：

1. **结构化数据**：使用专用表存储功能权限，提供清晰的数据结构
2. **查询灵活性**：支持复杂的查询和统计分析
3. **事务支持**：更好的事务和并发处理
4. **扩展性**：更容易添加新功能和属性
5. **微服务友好**：更适合未来的微服务架构
6. **数据安全**：提供完善的功能代码数据管理工具，确保数据安全和可恢复性

## 附录

### A. 功能代码列表 ✅

| 功能代码 | 描述 | 关联模块 | 状态 |
|---------|------|---------|------|
| ai.ppt | AI PPT生成功能 | AI模块 | ⏳ |
| ai.document | AI文档生成功能 | AI模块 | ⏳ |
| payment | 支付功能 | 支付模块 | ⏳ |
| membership | 会员功能 | 会员模块 | ⏳ |
| tenant-config.email | 租户邮件配置 | 租户配置模块 | ✅ |
| tenant-config.sms | 租户短信配置 | 租户配置模块 | ✅ |
| tenant-config.oss | 租户对象存储配置 | 租户配置模块 | ✅ |
| tenant-config.payment | 租户支付配置 | 租户配置模块 | ✅ |

### B. 会员等级与功能权限映射 ⏳

| 会员等级 | 功能权限 | 状态 |
|---------|---------|------|
| 基础版 | ai.ppt(配额:10), tenant-config.email | ⏳ |
| 专业版 | ai.ppt(配额:50), ai.document(配额:20), tenant-config.email, tenant-config.sms | ⏳ |
| 企业版 | ai.ppt(无限), ai.document(无限), tenant-config.* | ⏳ |
