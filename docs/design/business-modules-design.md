# 多租户系统业务模块设计规划文档

## 文档信息

- **文档版本**：1.1
- **创建日期**：2025-05-11
- **最后更新**：2025-05-11
- **状态**：规划阶段

## 1. 概述

### 1.1 目的

本文档旨在规划多租户系统的业务模块扩展，包括AI模块、会员功能和支付功能，以支持系统的商业化运营和功能差异化。

### 1.2 背景

当前系统已实现基础的多租户架构，包括系统用户和租户用户的管理、角色权限控制、菜单管理等功能。为了进一步提升系统价值和商业化能力，需要增加AI功能、会员管理和支付处理等业务模块。

### 1.3 范围

本文档涵盖以下内容：

- AI模块（包括AIPPT功能）的设计
- 会员功能的设计
- 支付功能的设计
- 租户配置管理功能的设计
- 这些模块与现有系统的集成方案

## 2. 系统架构

### 2.1 总体架构

![系统架构图](../assets/system-architecture.png)

系统采用模块化设计，新增业务模块将作为独立模块添加到现有系统中：

```plaintext
src/
└── modules/
    ├── ai/                      # AI模块
    │   ├── ppt/                 # AIPPT子模块
    │   └── other-ai-features/   # 其他AI功能
    ├── membership/              # 会员模块
    ├── payment/                 # 支付模块
    ├── tenant-config/           # 租户配置模块
    └── existing-modules/        # 现有模块
```

### 2.2 租户功能控制方案

采用专用功能表方案，创建`TenantFeature`表存储租户功能权限信息：

```prisma
// 租户功能权限表
model TenantFeature {
  id           Int       @id @default(autoincrement())
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  tenantId     Int       // 租户ID
  featureCode  String    // 功能代码，如 "ai.ppt", "payment"
  enabled      Boolean   @default(false) // 是否启用
  expiresAt    DateTime? // 过期时间
  quota        Int?      // 使用配额（null表示无限制）
  usedQuota    Int       @default(0) // 已使用配额
  config       Json?     // 功能配置（非敏感信息）

  tenant       Tenant    @relation(fields: [tenantId], references: [id])

  @@unique([tenantId, featureCode])
  @@index([featureCode])
}
```

> 注意：详细的租户功能权限管理设计请参考 [租户功能权限管理设计文档](./tenant-feature-management-design.md)。

### 2.3 数据模型关系

![数据模型关系图](../assets/data-model-relations.png)

## 3. AI模块设计

### 3.1 功能概述

AI模块提供智能化功能，初期重点实现AIPPT功能，允许用户通过AI生成专业PPT。

### 3.2 模块结构

```plaintext
src/modules/ai/
├── ai.module.ts         # AI主模块
├── ai.controller.ts     # AI主控制器
├── ai.service.ts        # AI主服务
├── ppt/                 # AIPPT子模块
│   ├── ppt.module.ts
│   ├── ppt.controller.ts
│   ├── ppt.service.ts
│   ├── dto/             # 数据传输对象
│   ├── strategies/      # 策略实现
│   └── interfaces/      # 接口定义
└── providers/           # AI服务提供商适配器
```

### 3.3 租户功能控制

- 使用功能守卫检查租户是否有权限访问AI功能
- 基于租户元数据中的配置控制功能访问和使用限制
- 提供功能装饰器简化权限控制

### 3.4 API设计

```plaintext
POST /api/ai/ppt/generate      # 生成PPT
GET  /api/ai/ppt/history       # 获取历史记录
GET  /api/ai/ppt/templates     # 获取可用模板
```

## 4. 会员功能设计

### 4.1 功能概述

会员功能提供不同等级的会员计划，每个等级对应不同的功能权限和使用限制。

### 4.2 数据模型

```prisma
model MembershipPlan {
  id              Int       @id @default(autoincrement())
  code            String    @unique // 如 "basic", "pro", "enterprise"
  name            String    // 显示名称
  price           Decimal   // 价格
  billingCycle    String    // "monthly", "quarterly", "yearly"
  features        Json      // 包含的功能和限制
  isActive        Boolean   @default(true)
  // 其他字段...
}

model TenantMembership {
  id              Int       @id @default(autoincrement())
  tenantId        Int
  planId          Int
  startDate       DateTime
  endDate         DateTime
  status          String    // "active", "expired", "cancelled"
  autoRenew       Boolean   @default(false)
  // 关联字段...
}
```

### 4.3 会员等级与权益

设计不同的会员等级，每个等级对应不同的功能权限和使用限制：

| 会员等级 | 价格（月） | AI PPT 功能 | 每月配额 | 可用模板 |
|---------|-----------|------------|---------|---------|
| 基础版   | ¥99       | ✓          | 10次    | 基础模板 |
| 专业版   | ¥299      | ✓          | 50次    | 全部模板 |
| 企业版   | ¥999      | ✓          | 无限制   | 全部模板 + 定制 |

### 4.4 API设计

```plaintext
GET  /api/membership/plans                # 获取会员计划列表
GET  /api/membership/tenant/:tenantId     # 获取租户会员信息
POST /api/membership/subscribe            # 订阅会员计划
POST /api/membership/renew                # 续费会员
POST /api/membership/upgrade              # 升级会员计划
```

## 5. 支付功能设计

### 5.1 功能概述

支付功能提供完整的支付处理流程，支持多种支付方式，处理会员订阅和其他付费功能的支付。

### 5.2 数据模型

```prisma
model Payment {
  id              Int       @id @default(autoincrement())
  tenantId        Int
  membershipId    Int?
  orderNo         String    @unique // 订单号
  amount          Decimal   // 支付金额
  paymentMethod   String    // "alipay", "wechat", "creditcard"
  status          String    // "pending", "success", "failed", "refunded"
  // 其他字段...
}

model PaymentCallback {
  id              Int       @id @default(autoincrement())
  paymentId       Int?
  provider        String    // 支付提供商
  rawData         Json      // 原始回调数据
  processed       Boolean   @default(false)
  // 关联字段...
}
```

### 5.3 支付提供商适配器

设计一个灵活的支付提供商适配器，以便轻松集成不同的支付方式：

```typescript
export interface IPaymentProvider {
  getName(): string;
  createPayment(order: CreatePaymentDto): Promise<PaymentResultDto>;
  verifyCallback(data: any): Promise<VerifyCallbackResultDto>;
  queryPaymentStatus(orderNo: string): Promise<PaymentStatusDto>;
  refund(orderNo: string, amount?: number): Promise<RefundResultDto>;
}
```

### 5.4 API设计

```plaintext
POST /api/payment/create                  # 创建支付订单
GET  /api/payment/status/:orderNo         # 查询支付状态
POST /api/payment/callback/:provider      # 支付回调接口
POST /api/payment/refund                  # 申请退款
GET  /api/payment/history                 # 获取支付历史
```

## 6. 租户配置管理功能设计

### 6.1 功能概述

租户配置管理功能允许每个租户自定义各种服务配置，包括邮件服务、短信服务、对象存储、API密钥和支付服务等。

### 6.2 数据模型

采用专用配置表方案存储租户配置：

```prisma
model TenantConfig {
  id           Int       @id @default(autoincrement())
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  tenantId     Int
  category     String    // "email", "sms", "oss", "payment", "apiKey"
  key          String    // 配置键
  value        String    // 配置值（敏感信息加密存储）
  encrypted    Boolean   @default(false)

  tenant       Tenant    @relation(fields: [tenantId], references: [id])

  @@unique([tenantId, category, key])
}
```

> 注意：租户配置管理与功能权限管理共同使用专用表方案，详细设计请参考 [租户功能权限管理设计文档](./tenant-feature-management-design.md)。

### 6.3 配置类别

| 配置类别 | 描述 | 敏感字段 |
|---------|------|---------|
| email   | 邮件服务配置 | password, apiKey |
| sms     | 短信服务配置 | accessKeySecret |
| oss     | 对象存储配置 | accessKeyId, accessKeySecret |
| payment | 支付服务配置 | privateKey, apiKey |
| apiKey  | 第三方API密钥 | 所有密钥值 |

### 6.4 API设计

```plaintext
GET  /api/tenant/:tenantId/configs                # 获取所有配置类别
GET  /api/tenant/:tenantId/configs/:category      # 获取特定类别的配置
POST /api/tenant/:tenantId/configs/:category      # 更新特定类别的配置
POST /api/tenant/:tenantId/configs/:category/test # 测试配置有效性
GET  /api/configs/templates/:category             # 获取配置模板
```

## 7. 模块集成

### 7.1 与现有系统集成

- 所有新模块通过租户ID与现有系统集成
- 使用现有的认证和授权机制
- 扩展现有的菜单和权限系统

### 7.2 模块间集成

- 会员功能控制AI模块的访问权限和使用配额
- 支付功能处理会员订阅的支付流程
- 租户配置为各模块提供必要的服务配置
- 使用事件系统处理模块间通信

## 8. 实施路线图

### 8.1 第一阶段：基础设施（2周）

- 扩展租户元数据结构
- 实现功能守卫和装饰器
- 创建会员、支付和配置相关数据表

### 8.2 第二阶段：租户配置管理（2周）

- 实现配置加密和解密机制
- 开发配置管理服务
- 实现配置验证功能

### 8.3 第三阶段：AI模块（3周）

- 实现AI服务提供商适配器
- 开发AI PPT生成功能
- 集成功能控制机制

### 8.4 第四阶段：会员功能（2周）

- 实现会员计划和权益管理
- 开发会员订阅和管理功能
- 与AI模块集成

### 8.5 第五阶段：支付功能（3周）

- 集成支付宝和微信支付
- 实现支付流程和回调处理
- 开发支付历史和管理功能

### 8.6 第六阶段：测试与优化（2周）

- 系统集成测试
- 性能优化
- 安全审查

## 9. 结论与建议

### 9.1 技术选择建议

- 使用专用功能表方案管理租户功能权限
- 采用策略模式和适配器模式实现灵活的服务集成
- 使用事件系统处理模块间通信
- 采用专用配置表方案存储租户配置，保护敏感信息

### 9.2 实施建议

- 分阶段实施，先完成基础功能再添加高级特性
- 重视支付安全和数据保护
- 为未来的微服务架构做好准备
- 优先实现租户配置管理，为其他模块提供基础支持

### 9.3 未来扩展

- 更多AI功能（如AI文档、AI图像等）
- 更丰富的会员权益和营销功能
- 国际化支付和多币种支持
- 更多第三方服务集成选项

## 附录

### A. 术语表

| 术语 | 定义 |
|------|------|
| 租户 | 系统中的独立组织或客户 |
| 会员计划 | 定义了一组功能权限和使用限制的付费方案 |
| AI PPT | 基于AI技术自动生成PPT的功能 |
| OSS | 对象存储服务，用于存储文件和媒体内容 |

### B. 参考文档

- 系统架构设计文档
- 多租户数据模型设计文档
- 第三方支付接口文档
- 各服务提供商API文档
