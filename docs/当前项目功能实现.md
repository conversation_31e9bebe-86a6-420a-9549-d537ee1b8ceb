# 当前项目功能实现

本文档总结了多租户 NestJS 项目中已经实现的核心功能和组件。

## 1. 多租户架构

### 1.1 数据库设计

项目使用两种数据库模型：

1. **公共数据库**：存储租户元数据和数据源信息
   ```prisma
   // prisma/public-schema.prisma
   model Tenant {
     id           Int         @id @default(autoincrement())
     createdAt    DateTime    @default(now())
     updatedAt    DateTime    @default(now())
     code         String
     name         String
     website      String?
     metadata     Json?
     datasource   Datasource? @relation(fields: [datasourceId], references: [id])
     datasourceId Int?
   }

   model Datasource {
     id        Int      @id @default(autoincrement())
     createdAt DateTime @default(now())
     updatedAt DateTime @default(now())
     name      String
     url       String
     metadata  Json?
     tenants   Tenant[]
   }
   ```

2. **租户数据库**：存储租户的业务数据
   ```prisma
   // prisma/schema.prisma
   model User {
     id           Int      @id @default(autoincrement())
     createdAt    DateTime @default(now())
     updatedAt    DateTime @default(now())
     emailAddress String
     firstName    String?
     lastName     String?
     metadata     Json?
     tenantId     String
   }
   ```

### 1.2 Prisma 服务

项目实现了两个 Prisma 服务来连接不同的数据库：

1. **PublicPrismaService**：连接公共数据库
   ```typescript
   // src/modules/prisma/public-prisma.service.ts
   @Injectable()
   export class PublicPrismaService extends PrismaClient implements OnModuleInit {
     constructor() {
       super({
         log: [
           { emit: "event", level: "query" },
           { emit: "event", level: "error" },
           { emit: "event", level: "info" },
           { emit: "event", level: "warn" },
         ],
       });
     }

     async onModuleInit() {
       this.$on("query" as never, (e: Prisma.QueryEvent) => {
         Logger.debug("Query: " + e.query);
         Logger.debug("Params: " + e.params);
         Logger.debug("Duration: " + e.duration + "ms");
       });

       await this.$connect();
     }
   }
   ```

2. **TenantPrismaService**：连接租户数据库，并实现查询扩展确保数据隔离
   ```typescript
   // src/modules/prisma/tenant-prisma.service.ts
   @Injectable()
   export class TenantPrismaService extends PrismaClient implements OnModuleInit {
     constructor(datasourceUrl: string) {
       super({
         datasourceUrl,
         log: [
           { emit: "event", level: "query" },
           { emit: "event", level: "error" },
           { emit: "event", level: "info" },
           { emit: "event", level: "warn" },
         ],
       });
     }

     withQueryExtensions(tenantCode: string) {
       return this.$extends({
         query: {
           $allOperations({ query }) {
             return query({ where: { tenantId: tenantCode } });
           },
         },
       });
     }

     async onModuleInit() {
       this.$on("query" as never, (e: Prisma.QueryEvent) => {
         Logger.debug("Query: " + e.query);
         Logger.debug("Params: " + e.params);
         Logger.debug("Duration: " + e.duration + "ms");
       });

       await this.$connect();
     }
   }

   export const TENANT_PRISMA_SERVICE = TenantPrismaService.name;
   ```

### 1.3 Prisma 模块

Prisma 模块负责提供 Prisma 服务的依赖注入：

```typescript
// src/modules/prisma/prisma.module.ts
@Global()
@Module({
  exports: [PublicPrismaService, TENANT_PRISMA_SERVICE],
  providers: [
    PublicPrismaService,
    {
      provide: TENANT_PRISMA_SERVICE,
      scope: Scope.REQUEST,
      inject: [REQUEST],
      useFactory: (request: IRequestWithProps) => {
        const { tenant } = request;

        if (!tenant) throw new BadRequestException("Invalid tenant code.");

        const { tenantCode, datasourceUrl } = tenant;

        if (datasourceUrl) {
          throw new NotFoundException("This tenant has no datasource.");
        }

        return new TenantPrismaService(datasourceUrl).withQueryExtensions(
          tenantCode,
        );
      },
    },
  ],
})
export class PrismaModule {}
```

## 2. 中间件实现

项目实现了三个主要的中间件：

### 2.1 RequestIdMiddleware

为每个请求生成唯一的请求 ID：

```typescript
// src/middlewares/request-id.middleware.ts
@Injectable()
export class RequestIdMiddleware implements NestMiddleware {
  use(request: Request, response: Response, next: NextFunction) {
    if (!request.headers["x-request-id"]) {
      const requestId = randomUUID();
      request.headers["x-request-id"] = requestId;
    }
    next();
  }
}
```

### 2.2 RequestLoggerMiddleware

记录每个请求的详细信息：

```typescript
// src/middlewares/request-logger.middleware.ts
@Injectable()
export class RequestLoggerMiddleware implements NestMiddleware {
  private readonly logger = new Logger(RequestLoggerMiddleware.name);

  use(request: Request, response: Response, next: NextFunction): void {
    const { method, originalUrl } = request;
    const start = Date.now();

    const requestId = request.headers["x-request-id"] as string;

    response.on("finish", () => {
      const { statusCode } = response;
      const duration = Date.now() - start;
      this.logger.log(
        `${method} ${originalUrl} ${statusCode} - ${duration}ms [${requestId}]`,
      );
    });

    next();
  }
}
```

### 2.3 TenantDatasourceMiddleware

多租户系统的核心中间件，负责识别租户并设置数据源：

```typescript
// src/middlewares/tenant-datasource.middleware.ts
@Injectable()
export class TenantDatasourceMiddleware implements NestMiddleware {
  constructor(private readonly publicPrisma: PublicPrismaService) {}

  async use(request: IRequestWithProps, response: Response, next: () => void) {
    const tenantCode = request.headers["x-tenant-code"] as string;

    const tenant = await this.publicPrisma.tenant.findFirst({
      include: { datasource: true },
      where: { code: tenantCode },
    });

    request.tenant = {
      tenantCode: tenantCode,
      datasourceUrl: tenant?.datasource?.url,
    };

    next();
  }
}
```

## 3. 守卫实现

项目实现了三个主要的守卫：

### 3.1 AuthGuard

基本的认证守卫，继承自 UserGuard：

```typescript
// src/guards/auth.guard.ts
@Injectable()
export class AuthGuard extends UserGuard implements CanActivate {
  constructor(protected readonly reflector: Reflector) {
    super(reflector);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublicRoute = this.reflector.get<boolean>(
      "allowPublicAccess",
      context.getHandler(),
    );

    if (isPublicRoute) return true;

    return await super.canActivate(context);
  }
}
```

### 3.2 UserGuard

用户认证守卫，继承自 RolesGuard：

```typescript
// src/guards/user.guard.ts
@Injectable()
export class UserGuard extends RolesGuard implements CanActivate {
  constructor(protected readonly reflector: Reflector) {
    super(reflector);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublicRoute = this.reflector.get<boolean>(
      "allowPublicAccess",
      context.getHandler(),
    );
    if (isPublicRoute) return true;

    const request: IRequestWithProps = context.switchToHttp().getRequest();

    const { auth } = request;

    if (!auth) return false;

    const { userId } = auth;

    const user = { userId };

    if (user) {
      Logger.debug(`✅ Authenticated: ${userId}`, "UserGuard");
    } else {
      Logger.debug(
        `⛔ Unauthorized: ${request.hostname} [${request.ip}]`,
        "UserGuard",
      );
      return false;
    }

    request.user = user;

    return await super.canActivate(context);
  }
}
```

### 3.3 RolesGuard

基于角色的访问控制守卫：

```typescript
// src/guards/roles.guard.ts
@Injectable()
export class RolesGuard implements CanActivate {
  constructor(protected reflector: Reflector) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublicRoute = this.reflector.get<boolean>(
      "allowPublicAccess",
      context.getHandler(),
    );
    if (isPublicRoute) return true;

    const request: IRequestWithProps = context.switchToHttp().getRequest();

    if (!request.user) return false;

    const { role: userRole } = request.user;

    const allowedRoles = this.reflector.get(RolesAllowed, context.getHandler());
    if (!allowedRoles || allowedRoles.length === 0) return true;

    return matchRoles(allowedRoles, userRole ?? "GUEST");
  }
}
```

## 4. 装饰器实现

项目实现了几个自定义装饰器：

### 4.1 PublicRoute

标记路由为公共访问，跳过认证检查：

```typescript
// src/decorators/public-route.decorator.ts
export const PublicRoute = () => SetMetadata("allowPublicAccess", true);
```

### 4.2 RolesAllowed

指定访问路由所需的用户角色：

```typescript
// src/decorators/roles-allowed.decorator.ts
export const RolesAllowed = Reflector.createDecorator<IUserRole[]>();
```

### 4.3 UserInRequest

参数装饰器，从请求对象中提取用户信息：

```typescript
// src/decorators/user-in-request.decorator.ts
export const UserInRequest = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request: IRequestWithProps = ctx.switchToHttp().getRequest();
    return request.user;
  },
);
```

## 5. 用户模块实现

项目实现了基本的用户模块：

### 5.1 UsersController

```typescript
// src/modules/routes/users/users.controller.ts
@Controller("users")
@ApiTags("users")
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  @ApiOkResponse({ type: FindUserDto })
  create(@Body() createUserDto: CreateUserDto) {
    return this.usersService.create(createUserDto);
  }

  @Get()
  @ApiOkResponse({ type: FindUsersDto })
  @ApiQuery({
    name: "take",
    type: "number",
    required: false,
    description: "Default = 10",
  })
  @ApiQuery({
    name: "skip",
    type: "number",
    required: false,
    description: "Default = 0",
  })
  async findAll(
    @Query(
      "take",
      new DefaultValuePipe(new PaginationOptions().take),
      ParseIntPipe,
    )
    take: number,
    @Query(
      "skip",
      new DefaultValuePipe(new PaginationOptions().skip),
      ParseIntPipe,
    )
    skip: number,
  ) {
    return await this.usersService.findAll({}, { take, skip });
  }

  @Get(":id")
  @ApiOkResponse({ type: FindUserDto })
  findOne(@Param("id") id: string) {
    return this.usersService.findOne(+id);
  }

  @Patch(":id")
  @ApiOkResponse({ type: FindUserDto })
  update(@Param("id") id: string, @Body() updateUserDto: UpdateUserDto) {
    return this.usersService.update(+id, updateUserDto);
  }

  @Delete(":id")
  remove(@Param("id") id: string) {
    return this.usersService.remove(+id);
  }
}
```

### 5.2 UsersService

```typescript
// src/modules/routes/users/users.service.ts
@Injectable()
export class UsersService {
  constructor(
    @Inject(TENANT_PRISMA_SERVICE) private readonly prisma: TenantPrismaService,
  ) {}

  create(createUserDto: CreateUserDto) {
    return "This action adds a new user";
  }

  async findAll(where: Prisma.UserWhereInput, options: PaginationOptions) {
    return await this.prisma.user.findMany();
  }

  findOne(id: number) {
    return `This action returns a #${id} user`;
  }

  update(id: number, updateUserDto: UpdateUserDto) {
    return `This action updates a #${id} user`;
  }

  remove(id: number) {
    return `This action removes a #${id} user`;
  }
}
```

### 5.3 UsersModule

```typescript
// src/modules/routes/users/users.module.ts
@Module({
  imports: [PrismaModule],
  controllers: [UsersController],
  providers: [UsersService],
})
export class UsersModule {}
```

## 6. 应用配置

### 6.1 应用模块

```typescript
// src/app.module.ts
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      validationSchema: Joi.object({
        NODE_ENV: Joi.string()
          .valid("development", "production", "test", "staging")
          .default("development"),
        PORT: Joi.number().port().default(3000),
        CORS_ORIGINS: Joi.string().default("*"),
        DATABASE_URL: Joi.string(),
      }),
      validationOptions: {
        allowUnknown: true,
        abortEarly: true,
      },
    }),
    TerminusModule,
    PrismaModule,
    UsersModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(RequestIdMiddleware).forRoutes("*");
    consumer.apply(RequestLoggerMiddleware).forRoutes("*");
    consumer.apply(TenantDatasourceMiddleware).forRoutes("*");
  }
}
```

### 6.2 主入口文件

```typescript
// src/main.ts
async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  const configService = app.get(ConfigService);

  const swaggerConfig = new DocumentBuilder()
    .setTitle(metadata.name)
    .setVersion(metadata.version)
    .setDescription(metadata.description)
    .build();
  const document = SwaggerModule.createDocument(app, swaggerConfig);
  SwaggerModule.setup("api/docs", app, document);

  app.use(helmet());

  app.enableCors({
    origin: true ?? configService.get("CORS_ORIGINS"),
  });

  app.useGlobalPipes(new ValidationPipe());

  await app.listen(3000);
}
bootstrap();
```

### 6.3 应用控制器

```typescript
// src/app.controller.ts
@Controller()
export class AppController {
  constructor(
    private readonly appService: AppService,
    private readonly publicPrisma: PublicPrismaService,
    @Inject(TENANT_PRISMA_SERVICE)
    private readonly tenantPrisma: TenantPrismaService,
  ) {}

  @Get()
  @HealthCheck()
  async getHealthcheck() {
    const app = {
      name: metadata.name,
      version: metadata.version,
      environment: process.env.NODE_ENV,
    };

    const healthcheck = await this.appService.getHealthcheck();

    return { app, ...healthcheck };
  }

  @Get("/tenants")
  async getTenants() {
    const tenants = await this.publicPrisma.tenant.findMany();

    return { tenants };
  }

  @Get("/users")
  async getUsers() {
    /**
     * Since we're using query extensions with the Prisma client,
     * this query should return only the users with the column
     * "tenantId" matching that in the request "x-tenant-code".
     */
    const users = await this.tenantPrisma.user.findMany();

    return { users };
  }
}
```

## 7. 数据库更新命令

项目提供了两个命令用于更新数据库结构：

```json
// package.json (scripts 部分)
{
  "scripts": {
    "db:push:tenant": "prisma db push",
    "db:generate:tenant": "prisma generate",
    "db:push:public": "prisma db push --schema prisma/public-schema.prisma",
    "db:generate:public": "prisma generate --schema prisma/public-schema.prisma",
    "postinstall": "pnpm db:generate:tenant && pnpm db:generate:public"
  }
}
```

## 8. 尚未实现的功能

1. **认证系统**：虽然有守卫和装饰器的框架，但尚未实现完整的认证逻辑
2. **超级管理员功能**：尚未实现超级管理员的数据模型和权限控制
3. **租户管理员功能**：尚未实现租户管理员的数据模型和权限控制
4. **完整的用户管理**：用户服务中的方法大多是占位符，尚未实现实际功能
5. **错误处理**：缺少全局异常过滤器和统一的错误响应格式

## 9. 工作流程

1. 客户端发送请求，在请求头中包含 `x-tenant-code`
2. `TenantDatasourceMiddleware` 拦截请求，根据租户代码查找对应的数据源
3. 创建特定租户的 Prisma 服务实例，并应用查询扩展，确保只能访问该租户的数据
4. 控制器和服务使用注入的 Prisma 服务实例处理请求

## 10. 环境设置

项目需要以下环境变量：

```
PUBLIC_DATABASE_URL="postgresql://username:password@localhost:5432/public_db?schema=public"
DATABASE_URL="postgresql://username:password@localhost:5432/tenant_db?schema=tenant"
```

- `PUBLIC_DATABASE_URL` 用于连接存储租户元数据和数据源信息的公共数据库
- `DATABASE_URL` 是默认的租户数据库连接，通常用于开发和测试
