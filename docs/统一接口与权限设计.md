# 统一接口与权限设计

## 1. 统一接口设计

### 1.1 统一用户接口

为了让前端拥有统一的接口体验，我们可以设计一套统一的用户管理 API，后端使用策略模式根据用户类型选择不同的实现：

```typescript
// src/modules/users/users.controller.ts
@Controller('users')
@ApiTags('users')
export class UsersController {
  constructor(
    private readonly userServiceFactory: UserServiceFactory,
  ) {}

  @Post('login')
  @Public()
  async login(@Body() loginDto: LoginDto) {
    // loginDto 包含 username, password, tenantCode(可选)
    const userService = this.userServiceFactory.createUserService(loginDto.tenantCode);
    return await userService.login(loginDto);
  }

  @Post('register')
  @Public()
  async register(@Body() registerDto: RegisterDto) {
    const userService = this.userServiceFactory.createUserService(registerDto.tenantCode);
    return await userService.register(registerDto);
  }

  @Post('logout')
  async logout(@UserInRequest() user: any) {
    const userService = this.userServiceFactory.createUserService(user.tenantId);
    return await userService.logout(user);
  }

  @Get('profile')
  async getProfile(@UserInRequest() user: any) {
    const userService = this.userServiceFactory.createUserService(user.tenantId);
    return await userService.getProfile(user.id);
  }

  @Put('profile')
  async updateProfile(@UserInRequest() user: any, @Body() updateDto: UpdateProfileDto) {
    const userService = this.userServiceFactory.createUserService(user.tenantId);
    return await userService.updateProfile(user.id, updateDto);
  }

  // 其他用户相关接口...
}
```

### 1.2 用户服务工厂

使用工厂模式创建适合的用户服务实现：

```typescript
// src/modules/users/user-service.factory.ts
@Injectable()
export class UserServiceFactory {
  constructor(
    private readonly systemUserService: SystemUserService,
    private readonly tenantUserService: TenantUserService,
  ) {}

  createUserService(tenantId?: string): IUserService {
    if (tenantId) {
      return this.tenantUserService;
    } else {
      return this.systemUserService;
    }
  }
}
```

### 1.3 用户服务接口

定义统一的用户服务接口：

```typescript
// src/modules/users/interfaces/user-service.interface.ts
export interface IUserService {
  login(loginDto: LoginDto): Promise<LoginResponseDto>;
  register(registerDto: RegisterDto): Promise<RegisterResponseDto>;
  logout(user: any): Promise<void>;
  getProfile(userId: string): Promise<ProfileDto>;
  updateProfile(userId: string, updateDto: UpdateProfileDto): Promise<ProfileDto>;
  // 其他方法...
}
```

### 1.4 系统用户服务实现

```typescript
// src/modules/users/system-user.service.ts
@Injectable()
export class SystemUserService implements IUserService {
  constructor(
    private readonly publicPrisma: PublicPrismaService,
    private readonly jwtService: JwtService,
  ) {}

  async login(loginDto: LoginDto): Promise<LoginResponseDto> {
    const { username, password } = loginDto;
    
    // 查找系统用户
    const user = await this.publicPrisma.systemUser.findFirst({
      where: { OR: [{ username }, { email: username }] },
    });
    
    if (!user || !await bcrypt.compare(password, user.password)) {
      throw new UnauthorizedException('无效的凭据');
    }
    
    // 生成令牌
    const token = this.jwtService.sign({
      sub: user.id,
      username: user.username,
      userType: 'SYSTEM',
      role: user.role,
    });
    
    return {
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        userType: 'SYSTEM',
      },
    };
  }
  
  // 实现其他方法...
}
```

### 1.5 租户用户服务实现

```typescript
// src/modules/users/tenant-user.service.ts
@Injectable()
export class TenantUserService implements IUserService {
  constructor(
    private readonly publicPrisma: PublicPrismaService,
    private readonly jwtService: JwtService,
    private readonly tenantPrismaFactory: TenantPrismaFactory,
  ) {}

  async login(loginDto: LoginDto): Promise<LoginResponseDto> {
    const { username, password, tenantCode } = loginDto;
    
    if (!tenantCode) {
      throw new BadRequestException('租户用户登录需要提供租户代码');
    }
    
    // 验证租户
    const tenant = await this.publicPrisma.tenant.findUnique({
      where: { code: tenantCode },
      include: { datasource: true },
    });
    
    if (!tenant) {
      throw new NotFoundException('租户不存在');
    }
    
    // 获取租户 Prisma 实例
    const tenantPrisma = await this.tenantPrismaFactory.createPrismaService(
      tenant.datasource.url,
      tenantCode,
    );
    
    // 查找租户用户
    const user = await tenantPrisma.user.findFirst({
      where: { OR: [{ username }, { email: username }] },
    });
    
    if (!user || !await bcrypt.compare(password, user.password)) {
      throw new UnauthorizedException('无效的凭据');
    }
    
    // 生成令牌
    const token = this.jwtService.sign({
      sub: user.id,
      username: user.username,
      userType: 'TENANT',
      role: user.role,
      tenantId: tenantCode,
    });
    
    return {
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        userType: 'TENANT',
        tenantId: tenantCode,
      },
    };
  }
  
  // 实现其他方法...
}
```

## 2. 统一权限与菜单设计

对于权限和菜单管理，我们可以采用一种灵活的方案，避免为系统用户和租户用户创建完全独立的表结构。

### 2.1 统一权限模型

使用 JSON 格式存储权限和菜单配置，这样可以避免创建过多的表：

```typescript
// 权限定义
interface Permission {
  resource: string;
  action: string;
  scope?: 'SYSTEM' | 'TENANT' | 'ALL';
}

// 菜单项定义
interface MenuItem {
  id: string;
  title: string;
  path?: string;
  icon?: string;
  pid?: string;
  order: number;
  scope: 'SYSTEM' | 'TENANT' | 'ALL';
  requiredPermissions?: Permission[];
  children?: MenuItem[];
}
```

### 2.2 角色表设计

根据最新的设计，系统角色和租户角色将存储在不同的表中，以实现更清晰的职责分离。

**系统角色表 (`prisma/public-schema.prisma`)**

系统角色存储在公共数据库中，用于管理系统级别的权限。

```prisma
// prisma/public-schema.prisma
model SystemRole {
  id           Int       @id @default(autoincrement())
  name         String
  code         String    @unique // 角色代码，全局唯一
  permissions  Json      // 存储权限列表 (例如: [{ resource: 'tenant', action: 'create' }, ...])
  menuIds      Json      // 存储可访问的菜单 ID 列表 (例如: ['system.tenants', 'system.users'])
  isDefault    Boolean   @default(false)
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @default(now())
}
```

**租户角色表 (`prisma/schema.prisma`)**

租户角色存储在各自租户的数据库中，用于管理租户内部的权限。

```prisma
// prisma/schema.prisma (租户特定的 schema)
model TenantRole {
  id           Int       @id @default(autoincrement())
  name         String
  code         String    @unique // 角色代码，在当前租户内唯一
  permissions  Json      // 存储权限列表 (例如: [{ resource: 'user', action: 'read', scope: 'TENANT' }, ...])
  menuIds      Json      // 存储可访问的菜单 ID 列表 (例如: ['tenant.users', 'dashboard'])
  isDefault    Boolean   @default(false) // 是否为租户的默认角色
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @default(now())
  tenantId     String    // 关联到 Tenant 的 code
}
```

### 2.3 用户-角色关联

用户表将关联到各自作用域的角色表。

**系统用户表 (`prisma/public-schema.prisma`)**

```prisma
// prisma/public-schema.prisma
model SystemUser {
  id             Int      @id @default(autoincrement())
  username       String   @unique
  email          String   @unique
  passwordHash   String
  // 其他字段...
  systemRoleCode String   // 关联到 SystemRole 表的 code 字段
  // systemRole     SystemRole @relation(fields: [systemRoleCode], references: [code]) // 可选的 Prisma 关系
  createdAt      DateTime @default(now())
  updatedAt      DateTime @default(now())
}
```

**租户用户表 (`prisma/schema.prisma`)**

```prisma
// prisma/schema.prisma (租户特定的 schema)
model User {
  id             Int      @id @default(autoincrement())
  username       String   // 在租户内唯一，需要结合 tenantId
  email          String   // 在租户内唯一，需要结合 tenantId
  passwordHash   String
  // 其他字段...
  tenantRoleCode String   // 关联到 TenantRole 表的 code 字段
  // tenantRole  TenantRole @relation(fields: [tenantRoleCode], references: [code]) // 可选的 Prisma 关系
  tenantId       String   // 租户代码，与公共库 Tenant 表的 code 对应
  createdAt      DateTime @default(now())
  updatedAt      DateTime @default(now())

  @@unique([email, tenantId])
  @@unique([username, tenantId])
}
```

注意：在 Prisma schema 中，`tenantId` 在租户用户表中用于标识用户所属的租户，通常对应于公共数据库中 `Tenant` 表的 `code` 字段。在 JWT 或其他上下文中，我们统一使用 `tenantCode` 来指代此标识。

### 2.4 菜单配置

菜单配置可以存储在配置文件或数据库中：

```typescript
// src/config/menus.config.ts
export const menuConfig: MenuItem[] = [
  // 系统管理菜单
  {
    id: 'system',
    title: '系统管理',
    icon: 'setting',
    order: 1,
    scope: 'SYSTEM',
    children: [
      {
        id: 'system.tenants',
        title: '租户管理',
        path: '/system/tenants',
        icon: 'team',
        order: 1,
        scope: 'SYSTEM',
        requiredPermissions: [{ resource: 'tenant', action: 'read', scope: 'SYSTEM' }],
      },
      {
        id: 'system.users',
        title: '系统用户管理',
        path: '/system/users',
        icon: 'user',
        order: 2,
        scope: 'SYSTEM',
        requiredPermissions: [{ resource: 'systemUser', action: 'read', scope: 'SYSTEM' }],
      },
      // 其他系统菜单...
    ],
  },
  
  // 租户管理菜单
  {
    id: 'tenant',
    title: '租户管理',
    icon: 'apartment',
    order: 1,
    scope: 'TENANT',
    children: [
      {
        id: 'tenant.users',
        title: '用户管理',
        path: '/tenant/users',
        icon: 'user',
        order: 1,
        scope: 'TENANT',
        requiredPermissions: [{ resource: 'user', action: 'read', scope: 'TENANT' }],
      },
      // 其他租户菜单...
    ],
  },
  
  // 通用菜单
  {
    id: 'dashboard',
    title: '仪表盘',
    path: '/dashboard',
    icon: 'dashboard',
    order: 0,
    scope: 'ALL',
  },
];
```

### 2.5 菜单服务

创建菜单服务，根据用户类型和权限过滤菜单：

```typescript
// src/modules/menu/menu.service.ts
@Injectable()
export class MenuService {
  constructor(
    private readonly publicPrisma: PublicPrismaService,
    private readonly permissionService: PermissionService,
  ) {}

  async getUserMenus(user: any): Promise<MenuItem[]> {
    // 获取所有菜单配置
    const allMenus = menuConfig;
    
    // 根据用户类型过滤菜单
    const userType = user.userType;
    const filteredMenus = allMenus.filter(menu => 
      menu.scope === 'ALL' || menu.scope === userType
    );
    
    // 递归过滤子菜单并检查权限
    return this.filterMenusRecursively(filteredMenus, user);
  }
  
  private async filterMenusRecursively(menus: MenuItem[], user: any): Promise<MenuItem[]> {
    const result = [];
    
    for (const menu of menus) {
      // 检查权限
      if (menu.requiredPermissions && menu.requiredPermissions.length > 0) {
        const hasPermission = await this.checkMenuPermissions(menu.requiredPermissions, user);
        if (!hasPermission) continue;
      }
      
      // 处理子菜单
      const filteredMenu = { ...menu };
      if (menu.children && menu.children.length > 0) {
        filteredMenu.children = await this.filterMenusRecursively(menu.children, user);
        // 如果过滤后没有子菜单，且当前菜单没有路径，则跳过
        if (filteredMenu.children.length === 0 && !filteredMenu.path) continue;
      }
      
      result.push(filteredMenu);
    }
    
    return result;
  }
  
  private async checkMenuPermissions(permissions: Permission[], user: any): Promise<boolean> {
    for (const permission of permissions) {
      const hasPermission = await this.permissionService.hasPermission(
        user,
        permission.resource,
        permission.action,
      );
      
      if (!hasPermission) return false;
    }
    
    return true;
  }
}
```

### 2.6 权限服务

创建权限服务，统一处理系统用户和租户用户的权限检查：

```typescript
// src/modules/permission/permission.service.ts
@Injectable()
export class PermissionService {
  constructor(
    private readonly publicPrisma: PublicPrismaService,
  ) {}

  async hasPermission(user: any, resource: string, action: string): Promise<boolean> {
    // 获取用户角色
    const role = await this.publicPrisma.role.findFirst({
      where: {
        code: user.roleCode,
        scope: user.userType === 'SYSTEM' ? 'SYSTEM' : 'TENANT',
      },
    });
    
    if (!role) return false;
    
    // 解析角色权限
    const permissions = role.permissions as Permission[];
    
    // 检查是否有通配符权限
    const hasWildcardPermission = permissions.some(
      p => (p.resource === '*' || p.resource === resource) && 
           (p.action === '*' || p.action === action)
    );
    
    if (hasWildcardPermission) return true;
    
    // 检查具体权限
    return permissions.some(
      p => p.resource === resource && p.action === action
    );
  }
  
  async getUserPermissions(user: any): Promise<Permission[]> {
    // 获取用户角色
    const role = await this.publicPrisma.role.findFirst({
      where: {
        code: user.roleCode,
        scope: user.userType === 'SYSTEM' ? 'SYSTEM' : 'TENANT',
      },
    });
    
    if (!role) return [];
    
    // 返回角色权限
    return role.permissions as Permission[];
  }
}
```

### 2.7 JWT 策略

修改 JWT 策略，统一处理系统用户和租户用户的认证：

```typescript
// src/strategies/jwt.strategy.ts
@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly configService: ConfigService,
    private readonly publicPrisma: PublicPrismaService,
    private readonly tenantPrismaFactory: TenantPrismaFactory,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get('JWT_SECRET'),
    });
  }

  async validate(payload: any) {
    const userType = payload.userType;
    
    if (userType === 'SYSTEM') {
      // 验证系统用户
      const user = await this.publicPrisma.systemUser.findUnique({
        where: { id: payload.sub },
      });
      
      if (!user) throw new UnauthorizedException();
      
      return {
        id: user.id,
        username: user.username,
        email: user.email,
        roleCode: user.roleCode,
        userType: 'SYSTEM',
      };
    } else {
      // 验证租户用户
      const tenantCode = payload.tenantCode; // 确保与JWT中声明一致，统一使用 tenantCode
      
      if (!tenantCode) throw new UnauthorizedException();
      
      // 获取租户信息
      const tenant = await this.publicPrisma.tenant.findUnique({
        where: { code: tenantCode },
        include: { datasource: true },
      });
      
      if (!tenant) throw new UnauthorizedException();
      
      // 获取租户 Prisma 实例
      const tenantPrisma = await this.tenantPrismaFactory.createPrismaService(
        tenant.datasource.url,
        tenantCode,
      );
      
      // 查找租户用户
      const user = await tenantPrisma.user.findUnique({
        where: { id: payload.sub },
      });
      
      if (!user) throw new UnauthorizedException();
      
      return {
        id: user.id,
        username: user.username,
        email: user.email,
        roleCode: user.roleCode,
        userType: 'TENANT',
        tenantCode,
      };
    }
  }
}
```

## 3. 前端实现

### 3.1 统一登录表单

```tsx
// React 示例
function LoginForm() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [tenantCode, setTenantCode] = useState('');
  const [showTenantField, setShowTenantField] = useState(false);
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const response = await api.post('/users/login', {
        username,
        password,
        tenantCode: tenantCode || undefined,
      });
      
      // 保存用户信息和令牌
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
      
      // 重定向到仪表盘
      history.push('/dashboard');
    } catch (error) {
      // 如果错误提示需要租户代码，显示租户字段
      if (error.response?.data?.message?.includes('租户代码')) {
        setShowTenantField(true);
      }
      // 显示错误消息
      message.error(error.response?.data?.message || '登录失败');
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <div>
        <label>用户名/邮箱</label>
        <input
          type="text"
          value={username}
          onChange={(e) => setUsername(e.target.value)}
          required
        />
      </div>
      <div>
        <label>密码</label>
        <input
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
        />
      </div>
      <div>
        <label>
          <input
            type="checkbox"
            checked={showTenantField}
            onChange={(e) => setShowTenantField(e.target.checked)}
          />
          我是租户用户
        </label>
      </div>
      {showTenantField && (
        <div>
          <label>租户代码</label>
          <input
            type="text"
            value={tenantCode}
            onChange={(e) => setTenantCode(e.target.value)}
            placeholder="租户用户必填"
            required={showTenantField}
          />
        </div>
      )}
      <button type="submit">登录</button>
    </form>
  );
}
```

### 3.2 动态菜单渲染

```tsx
// React 示例
function SideMenu() {
  const [menus, setMenus] = useState([]);
  
  useEffect(() => {
    // 获取用户菜单
    async function fetchMenus() {
      const response = await api.get('/menus');
      setMenus(response.data);
    }
    
    fetchMenus();
  }, []);
  
  return (
    <Menu theme="dark" mode="inline">
      {menus.map(menu => renderMenuItem(menu))}
    </Menu>
  );
}

function renderMenuItem(menu) {
  if (menu.children && menu.children.length > 0) {
    return (
      <Menu.SubMenu
        key={menu.id}
        title={
          <span>
            <Icon type={menu.icon} />
            <span>{menu.title}</span>
          </span>
        }
      >
        {menu.children.map(child => renderMenuItem(child))}
      </Menu.SubMenu>
    );
  }
  
  return (
    <Menu.Item key={menu.id}>
      <Link to={menu.path}>
        {menu.icon && <Icon type={menu.icon} />}
        <span>{menu.title}</span>
      </Link>
    </Menu.Item>
  );
}
```

## 4. 实现步骤

1. **创建角色表**：在公共数据库中创建统一的角色表
2. **修改用户表**：在系统用户和租户用户表中添加角色关联
3. **实现用户服务接口**：定义统一的用户服务接口
4. **实现服务工厂**：创建用户服务工厂，根据用户类型选择不同的实现
5. **实现统一控制器**：创建统一的用户控制器，使用工厂模式选择服务
6. **配置菜单**：创建菜单配置
7. **实现菜单服务**：创建菜单服务，根据用户类型和权限过滤菜单
8. **实现权限服务**：创建权限服务，统一处理权限检查
9. **修改 JWT 策略**：统一处理系统用户和租户用户的认证

## 5. 优势

1. **前端统一接口**：前端可以使用统一的 API 接口，简化开发
2. **灵活的权限管理**：使用 JSON 存储权限和菜单配置，避免创建过多的表
3. **清晰的职责分离**：使用策略模式和工厂模式，保持代码的清晰和可维护性
4. **可扩展性**：容易添加新的用户类型和权限规则

## 6. 注意事项

1. **性能考虑**：JSON 字段的查询性能可能不如关系表，但对于权限和菜单这种不频繁变化的数据，可以通过缓存优化
2. **数据一致性**：确保角色代码在系统和租户范围内的唯一性
3. **错误处理**：提供清晰的错误消息，特别是关于租户代码的要求
4. **安全性**：确保权限检查在所有需要的地方都得到执行
