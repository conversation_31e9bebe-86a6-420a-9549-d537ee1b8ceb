# 多租户架构

## 什么是多租户架构

多租户（Multi-tenant）架构是一种软件架构模式，允许单个应用实例服务多个客户（租户），同时确保每个租户的数据隔离。这种架构常用于 SaaS（Software as a Service）应用中。

## 本项目的多租户实现

本项目实现了一个基于 NestJS 和 Prisma 的多租户 API 系统，具有以下特点：

### 数据隔离策略

本项目采用了两种数据隔离策略的结合：

1. **独立数据库**：每个租户可以有自己的数据库连接
2. **共享数据库，隔离查询**：多个租户可以共享同一个数据库，通过查询过滤确保数据隔离

### 数据库架构

1. **公共数据库**：
   - 存储所有租户的元数据
   - 存储数据源配置信息
   - 用于租户识别和路由

2. **租户数据库**：
   - 存储租户的业务数据
   - 每个表都包含 `tenantId` 字段，用于数据隔离

### 请求流程

```
客户端 -> API 请求(带x-tenant-code) -> TenantDatasourceMiddleware -> 
查找租户数据源 -> 创建租户特定的 Prisma 实例 -> 
应用查询扩展(自动过滤tenantId) -> 控制器处理请求 -> 返回响应
```

## 关键技术实现

### 1. 租户识别

通过请求头 `x-tenant-code` 识别当前租户：

```typescript
const tenantCode = request.headers["x-tenant-code"] as string;

const tenant = await this.publicPrisma.tenant.findFirst({
  include: { datasource: true },
  where: { code: tenantCode },
});
```

### 2. 动态数据源

根据租户配置，动态创建数据库连接：

```typescript
new TenantPrismaService(datasourceUrl).withQueryExtensions(tenantCode)
```

### 3. 查询扩展

使用 Prisma 的查询扩展功能，自动为所有查询添加租户过滤条件：

```typescript
withQueryExtensions(tenantCode: string) {
  return this.$extends({
    query: {
      $allOperations({ query }) {
        return query({ where: { tenantId: tenantCode } });
      },
    },
  });
}
```

### 4. 请求作用域的服务

使用 NestJS 的请求作用域（REQUEST scope）确保每个请求使用正确的租户数据源：

```typescript
{
  provide: TENANT_PRISMA_SERVICE,
  scope: Scope.REQUEST,
  inject: [REQUEST],
  useFactory: (request: IRequestWithProps) => {
    // 创建租户特定的 Prisma 实例
  }
}
```

## 多租户架构的优势

1. **资源共享**：多个租户共享同一套应用代码和基础设施
2. **维护简化**：只需维护一个代码库和部署
3. **成本效益**：降低每个租户的运营成本
4. **灵活扩展**：可以根据需求轻松添加新租户
5. **数据隔离**：确保租户之间的数据安全隔离

## 多租户架构的挑战

1. **数据隔离**：确保不同租户的数据不会相互泄露
2. **性能考量**：需要确保一个租户的高负载不会影响其他租户
3. **定制化**：如何在共享架构的基础上提供租户特定的定制功能
4. **数据备份和恢复**：需要考虑租户级别的备份和恢复策略

## 适用场景

多租户架构特别适合以下场景：

1. SaaS 应用
2. 企业内部多部门共享的系统
3. 需要为多个客户提供相似服务的平台
4. 需要降低每个客户部署和维护成本的应用
