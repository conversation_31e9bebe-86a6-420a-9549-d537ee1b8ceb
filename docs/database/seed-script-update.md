# 数据库种子脚本更新文档

## 概述

本文档记录了对数据库种子脚本 (`seed.js`) 的更新，以支持新添加的表和功能。

## 更新日期

- **最后更新**：2025-05-16

## 更新内容

### 1. 新增种子文件

为了更好地组织和管理种子数据，我们创建了以下新的种子文件：

1. **功能代码种子文件**：`prisma/seeds/feature-codes.js`
   - 初始化功能代码表 (`FeatureCode`)
   - 包含 AI 模块、支付模块、会员模块和租户配置模块的功能代码

2. **租户配置种子文件**：`prisma/seeds/tenant-configs.js`
   - 初始化租户配置表 (`TenantConfig`)
   - 包含邮件、短信、对象存储和支付的配置

3. **租户功能种子文件**：`prisma/seeds/tenant-features.js`
   - 初始化租户功能表 (`TenantFeature`)
   - 初始化功能使用记录表 (`TenantFeatureUsage`)
   - 为租户分配功能权限和创建示例使用记录

### 2. 更新主种子脚本

更新了 `prisma/seed.js` 文件，添加了对新种子文件的引用和调用：

```javascript
// 导入新的种子文件
const { seedFeatureCodes } = require('./seeds/feature-codes');
const { seedTenantConfigs } = require('./seeds/tenant-configs');
const { seedTenantFeatures, seedFeatureUsage } = require('./seeds/tenant-features');

// 在适当的位置调用新的种子函数
// 8.5 创建功能代码
console.log('创建功能代码...');
await seedFeatureCodes(publicPrisma);
console.log('功能代码创建成功');

// 8.6 创建功能模板
console.log('创建功能模板...');
await seedFeatureTemplates(publicPrisma);
console.log('功能模板创建成功');

// 12. 创建租户配置
console.log('创建租户配置...');
await seedTenantConfigs(publicPrisma, tenant.id);
console.log('租户配置创建成功');

// 13. 创建租户功能权限
console.log('创建租户功能权限...');
await seedTenantFeatures(publicPrisma, tenant.id);
console.log('租户功能权限创建成功');

// 14. 创建租户功能使用记录
console.log('创建租户功能使用记录...');
await seedFeatureUsage(publicPrisma, tenant.id);
console.log('租户功能使用记录创建成功');
```

### 3. 添加功能代码管理工具

为了更好地管理功能代码数据，我们添加了以下工具：

1. **导出工具**：`scripts/export-feature-codes.js`
   - 从数据库导出功能代码数据
   - 生成更新的种子文件
   - 创建带时间戳的备份文件

2. **导入工具**：`scripts/import-feature-codes.js`
   - 从备份文件恢复功能代码数据
   - 支持环境间的数据迁移

3. **package.json 脚本**：
   - `db:export-feature-codes`：导出功能代码数据
   - `db:import-feature-codes`：导入功能代码数据

## 初始化数据说明

### 1. 功能代码数据

初始化的功能代码包括：

| 功能代码 | 名称 | 模块 |
|---------|------|------|
| ai.ppt | AI PPT生成 | AI模块 |
| ai.document | AI文档生成 | AI模块 |
| ai.chat | AI聊天 | AI模块 |
| ai.image | AI图像生成 | AI模块 |
| payment | 基础支付 | 支付模块 |
| payment.refund | 退款功能 | 支付模块 |
| payment.subscription | 订阅支付 | 支付模块 |
| membership | 基础会员 | 会员模块 |
| membership.upgrade | 会员升级 | 会员模块 |
| membership.points | 会员积分 | 会员模块 |
| tenant-config.email | 邮件配置 | 租户配置模块 |
| tenant-config.sms | 短信配置 | 租户配置模块 |
| tenant-config.oss | 对象存储配置 | 租户配置模块 |
| tenant-config.payment | 支付配置 | 租户配置模块 |

### 2. 租户配置数据

初始化的租户配置包括：

| 类别 | 配置项 | 说明 |
|------|-------|------|
| email | provider, host, port, username, password, fromEmail, fromName | 邮件服务配置 |
| sms | provider, accessKeyId, accessKeySecret, signName, templateCode | 短信服务配置 |
| oss | provider, region, bucket, accessKeyId, accessKeySecret, cdnDomain | 对象存储配置 |
| payment | alipay.*, wechat.* | 支付服务配置 |

### 3. 租户功能数据

初始化的租户功能包括：

| 功能代码 | 配额 | 过期时间 |
|---------|------|---------|
| ai.ppt | 50 | 一年后 |
| ai.document | 20 | 一年后 |
| tenant-config.email | 无限 | 无限 |
| tenant-config.sms | 无限 | 无限 |

## 使用方法

### 初始化数据库

```bash
# 重置数据库并执行种子脚本
pnpm db:reset

# 或者只执行种子脚本
pnpm db:seed
```

### 导出功能代码数据

```bash
pnpm db:export-feature-codes
```

### 导入功能代码数据

```bash
pnpm db:import-feature-codes ./backups/feature-codes-xxx.json
```

## 注意事项

1. 功能代码是系统的基础配置数据，对于系统的正常运行至关重要。在数据库初始化或迁移时，确保功能代码数据被正确保存和恢复。

2. 在生产环境中修改功能代码数据前，务必先创建备份。

3. 功能代码一旦创建并被租户使用，不建议删除，可以通过设置 `isActive` 为 `false` 来禁用。

4. 修改已有功能代码的 `code` 字段会导致关联数据失效，应该避免这种操作。

## 相关文档

- [功能代码管理指南](../feature-codes-management.md)
- [租户功能权限管理设计文档](../design/tenant-feature-management-design.md)
