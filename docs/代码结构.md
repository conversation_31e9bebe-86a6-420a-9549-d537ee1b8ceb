# 代码结构

## 目录结构

```
multi-tenant-nestjs/
├── prisma/                  # Prisma 模型定义
│   ├── public-schema.prisma # 公共数据库模型
│   └── schema.prisma        # 租户数据库模型
├── src/
│   ├── core/                # 核心功能模块
│   ├── decorators/         # 自定义装饰器
│   ├── guards/             # 守卫
│   ├── middlewares/        # 中间件
│   │   └── tenant-datasource.middleware.ts  # 租户数据源中间件
│   ├── modules/            # 功能模块
│   │   ├── prisma/         # Prisma 服务
│   │   │   ├── prisma.module.ts           # Prisma 模块
│   │   │   ├── public-prisma.service.ts   # 公共数据库服务
│   │   │   └── tenant-prisma.service.ts   # 租户数据库服务
│   │   └── routes/         # 路由模块
│   │       └── users/      # 用户模块
│   ├── types/              # 类型定义
│   │   └── IRequestWithProps.ts  # 请求接口定义
│   ├── app.controller.ts   # 主控制器
│   ├── app.module.ts       # 主模块
│   ├── app.service.ts      # 主服务
│   └── main.ts             # 应用入口
└── test/                   # 测试文件
```

## 核心文件说明

### Prisma 模型

#### 公共数据库模型 (public-schema.prisma)

```prisma
model Tenant {
  id           Int         @id @default(autoincrement())
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @default(now())
  code         String
  name         String
  website      String?
  metadata     Json?
  datasource   Datasource? @relation(fields: [datasourceId], references: [id])
  datasourceId Int?
}

model Datasource {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())
  name      String
  url       String
  metadata  Json?
  tenants   Tenant[]
}
```

#### 租户数据库模型 (schema.prisma)

```prisma
model User {
  id           Int      @id @default(autoincrement())
  createdAt    DateTime @default(now())
  updatedAt    DateTime @default(now())
  emailAddress String
  firstName    String?
  lastName     String?
  metadata     Json?
  tenantId     String
}
```

### 主要服务

#### 公共 Prisma 服务 (public-prisma.service.ts)

负责连接公共数据库，提供对租户和数据源信息的访问。

#### 租户 Prisma 服务 (tenant-prisma.service.ts)

负责连接租户数据库，并通过查询扩展确保数据隔离：

```typescript
withQueryExtensions(tenantCode: string) {
  return this.$extends({
    query: {
      $allOperations({ query }) {
        return query({ where: { tenantId: tenantCode } });
      },
    },
  });
}
```

### 中间件

#### 租户数据源中间件 (tenant-datasource.middleware.ts)

根据请求头中的租户代码，查找对应的数据源，并设置到请求对象中：

```typescript
async use(request: IRequestWithProps, response: Response, next: () => void) {
  const tenantCode = request.headers["x-tenant-code"] as string;

  const tenant = await this.publicPrisma.tenant.findFirst({
    include: { datasource: true },
    where: { code: tenantCode },
  });

  request.tenant = {
    tenantCode: tenantCode,
    datasourceUrl: tenant?.datasource?.url,
  };

  next();
}
```

### 模块配置

#### Prisma 模块 (prisma.module.ts)

配置 Prisma 服务的依赖注入，为每个请求创建特定租户的 Prisma 服务实例：

```typescript
@Global()
@Module({
  exports: [PublicPrismaService, TENANT_PRISMA_SERVICE],
  providers: [
    PublicPrismaService,
    {
      provide: TENANT_PRISMA_SERVICE,
      scope: Scope.REQUEST,
      inject: [REQUEST],
      useFactory: (request: IRequestWithProps) => {
        const { tenant } = request;

        if (!tenant) throw new BadRequestException("Invalid tenant code.");

        const { tenantCode, datasourceUrl } = tenant;

        if (datasourceUrl) {
          throw new NotFoundException("This tenant has no datasource.");
        }

        return new TenantPrismaService(datasourceUrl).withQueryExtensions(
          tenantCode,
        );
      },
    },
  ],
})
export class PrismaModule {}
```

#### 应用模块 (app.module.ts)

配置全局中间件，确保每个请求都经过租户数据源中间件处理：

```typescript
@Module({
  imports: [
    ConfigModule.forRoot({...}),
    TerminusModule,
    PrismaModule,
    UsersModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(RequestIdMiddleware).forRoutes("*");
    consumer.apply(RequestLoggerMiddleware).forRoutes("*");
    consumer.apply(TenantDatasourceMiddleware).forRoutes("*");
  }
}
```
