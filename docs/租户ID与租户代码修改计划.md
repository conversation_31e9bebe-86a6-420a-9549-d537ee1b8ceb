# 租户ID与租户代码修改计划

## 问题概述

在当前的多租户系统实现中，存在租户ID（tenantId）和租户代码（tenantCode）的概念混淆。具体表现为：

1. 在数据库中，`tenantId` 字段实际存储的是租户代码（字符串），而不是租户的数字ID
2. 在JWT令牌中，`tenantId` 字段存储的也是租户代码
3. 在缓存键生成中，使用 `tenantId` 变量名但实际上是租户代码
4. 在请求对象中，只存储了 `tenantCode`，没有存储租户的数字ID

## 修改计划

### 1. JWT令牌中的租户信息

#### 1.1 修改 `TenantAuthStrategy` 中的JWT载荷生成

文件：`src/core/auth/strategies/tenant-auth.strategy.ts`

```typescript
// 修改前
const payload: JwtPayload = {
  sub: user.id.toString(),
  username: user.username,
  userType: 'TENANT',
  tenantId: tenantCode,
};

// 修改后
const payload: JwtPayload = {
  sub: user.id.toString(),
  username: user.username,
  userType: 'TENANT',
  tenantId: tenant.id.toString(), // 使用租户的数字ID
  tenantCode: tenantCode, // 保留租户代码
};
```

#### 1.2 修改 `JwtPayload` 接口

文件：`src/core/auth/strategies/jwt.strategy.ts`

```typescript
// 修改前
export interface JwtPayload {
  sub: string;
  username: string;
  userType: 'SYSTEM' | 'TENANT';
  tenantId?: string;
}

// 修改后
export interface JwtPayload {
  sub: string;
  username: string;
  userType: 'SYSTEM' | 'TENANT';
  tenantId?: string; // 租户的数字ID
  tenantCode?: string; // 租户代码
}
```

#### 1.3 修改 `JwtStrategy` 中的验证方法

文件：`src/core/auth/strategies/jwt.strategy.ts`

```typescript
// 修改前
async validate(payload: JwtPayload) {
  return {
    userId: payload.sub,
    username: payload.username,
    userType: payload.userType,
    tenantId: payload.tenantId,
  };
}

// 修改后
async validate(payload: JwtPayload) {
  return {
    userId: payload.sub,
    username: payload.username,
    userType: payload.userType,
    tenantId: payload.tenantId, // 租户的数字ID
    tenantCode: payload.tenantCode, // 租户代码
  };
}
```

### 2. 请求对象中的租户信息

#### 2.1 修改 `IRequestWithProps` 接口

文件：`src/core/common/interfaces/request-with-props.interface.ts`

```typescript
// 修改前
tenant?: {
  /**
   * 租户代码
   */
  tenantCode?: string;

  /**
   * 数据源URL
   */
  datasourceUrl?: string;
};

// 修改后
tenant?: {
  /**
   * 租户ID - 用于数据库关系和内部操作
   */
  tenantId?: number;

  /**
   * 租户代码 - 用于识别租户和日志
   */
  tenantCode?: string;

  /**
   * 数据源URL
   */
  datasourceUrl?: string;
};
```

#### 2.2 修改 `tenant-datasource.middleware.ts` 中的租户信息存储

文件：`src/core/middleware/tenant-datasource.middleware.ts`

```typescript
// 修改前
request.tenant = {
  tenantCode: tenantCode,
  datasourceUrl: tenant.datasource.url,
};

// 修改后
request.tenant = {
  tenantId: tenant.id, // 存储租户的数字ID
  tenantCode: tenant.code, // 存储租户代码
  datasourceUrl: tenant.datasource.url,
};
```

### 3. 租户数据库服务

#### 3.1 修改 `TenantPrismaService` 中的 `withQueryExtensions` 方法

文件：`src/core/database/prisma/tenant-prisma.service.ts`

```typescript
// 修改前
withQueryExtensions(tenantCode: string) {
  return this.$extends({
    query: {
      $allOperations({ model, operation, args, query }) {
        if (operation === 'findMany' || operation === 'findFirst' || operation === 'findUnique' || operation === 'count') {
          return query({
            ...args,
            where: {
              ...args.where,
              tenantId: tenantCode,
            },
          });
        }
        return query(args);
      },
    },
  });
}

// 修改后
withQueryExtensions(tenantId: number) {
  return this.$extends({
    query: {
      $allOperations({ model, operation, args, query }) {
        if (operation === 'findMany' || operation === 'findFirst' || operation === 'findUnique' || operation === 'count') {
          return query({
            ...args,
            where: {
              ...args.where,
              tenantId: tenantId, // 使用租户的数字ID
            },
          });
        }
        return query(args);
      },
    },
  });
}
```

### 4. 缓存键生成

#### 4.1 修改 `RoleService` 中的缓存键生成方法

文件：`src/modules/role/role.service.ts`

```typescript
// 修改前
private generateRoleCacheKey(userType: string, tenantId?: string): string {
  return `role:${userType}:${tenantId || 'system'}:list`;
}

private generateRolePermissionsCacheKey(roleId: string, userType: string, tenantId?: string): string {
  return `role:${userType}:${tenantId || 'system'}:${roleId}:permissions`;
}

// 修改后
private generateRoleCacheKey(userType: string, tenantId?: number, tenantCode?: string): string {
  return `role:${userType}:${tenantId || 'system'}:${tenantCode || ''}:list`;
}

private generateRolePermissionsCacheKey(roleId: string, userType: string, tenantId?: number, tenantCode?: string): string {
  return `role:${userType}:${tenantId || 'system'}:${tenantCode || ''}:${roleId}:permissions`;
}
```

#### 4.2 修改 `MenuService` 中的缓存键生成方法

文件：`src/modules/menu/menu.service.ts`

```typescript
// 修改前
private generateMenuCacheKey(userId: number, tenantId?: string, userType?: string): string {
  return `menu:${userType || 'SYSTEM'}:${tenantId || 'system'}:${userId}`;
}

// 修改后
private generateMenuCacheKey(userId: number, tenantId?: number, tenantCode?: string, userType?: string): string {
  return `menu:${userType || 'SYSTEM'}:${tenantId || 'system'}:${tenantCode || ''}:${userId}`;
}
```

### 5. 策略类中的租户ID使用

需要检查所有租户策略类中的查询条件，确保使用租户的数字ID而不是租户代码。例如：

```typescript
// 修改前
const users = await this.prisma.user.findMany({
  where: {
    tenantId: tenantId, // 这里的tenantId实际上是租户代码
    // 其他条件...
  },
  // 其他选项...
});

// 修改后
const users = await this.prisma.user.findMany({
  where: {
    tenantId: tenantId, // 确保这里的tenantId是租户的数字ID
    // 其他条件...
  },
  // 其他选项...
});
```

## 实施步骤

1. **备份数据**：在进行任何修改之前，确保备份所有数据。

2. **修改数据库模型**：如果需要，修改Prisma模型定义，确保租户相关字段的命名和类型正确。

3. **修改代码**：按照上述计划修改代码。

4. **测试**：在开发环境中进行充分测试，确保所有功能正常工作。

5. **部署**：在测试通过后，部署到生产环境。

6. **监控**：部署后密切监控系统，确保没有出现问题。

## 注意事项

1. **数据迁移**：如果修改涉及到数据库结构的变化，需要编写数据迁移脚本。

2. **向后兼容**：考虑是否需要保持向后兼容，例如在一段时间内同时支持旧的和新的字段。

3. **文档更新**：更新系统文档，明确租户ID和租户代码的概念和用法。

4. **团队沟通**：确保团队所有成员都了解这些变化，特别是前端开发人员。
