# 代理模式最佳实践

## 概述

代理模式是一种结构型设计模式，它允许我们提供一个代理对象，控制对原始对象的访问。在我们的多租户系统中，我们使用代理模式来解决策略模式中的作用域问题和租户数据库连接问题。

本文档提供了在 NestJS 应用程序中使用代理模式的最佳实践，特别是在多租户系统中使用策略模式时。

## 何时使用代理模式

在以下情况下，应该考虑使用代理模式：

1. **作用域问题**：当需要在不同作用域的对象之间进行交互时，例如在默认作用域的服务中使用请求作用域的服务。

2. **上下文问题**：当 `this` 上下文可能会丢失时，例如在回调函数或异步操作中。

3. **资源管理**：当需要延迟初始化、缓存结果或控制对资源的访问时。

4. **错误处理**：当需要统一处理错误或提供降级策略时。

## 代理类的结构

一个典型的代理类应该包含以下部分：

1. **构造函数**：注入必要的依赖，例如 `ModuleRef` 和 `ConfigService`。

2. **执行方法**：提供一个统一的方法来执行原始对象的方法，例如 `execute` 方法。

3. **创建原始对象的方法**：负责创建原始对象的实例，例如 `createStrategy` 方法。

4. **获取资源的方法**：负责获取必要的资源，例如 `getTenantPrisma` 方法。

5. **错误处理**：提供统一的错误处理机制。

## 代理类的实现示例

```typescript
@Injectable()
export class StrategyProxy {
  private readonly logger = new Logger(StrategyProxy.name);

  constructor(
    private readonly moduleRef: ModuleRef,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 执行策略方法
   * @param userType 用户类型
   * @param methodName 方法名
   * @param args 方法参数
   * @param tenantId 租户ID（可选）
   * @returns 方法执行结果
   */
  async execute<T>(
    userType: string,
    methodName: string,
    args: any[],
    tenantId?: string
  ): Promise<T> {
    try {
      // 获取正确的策略类型
      const strategyType = userType === 'SYSTEM' ? StrategyType.SYSTEM : StrategyType.TENANT;
      
      // 根据策略类型获取策略类
      const StrategyClass = strategyType === StrategyType.SYSTEM 
        ? SystemStrategy 
        : TenantStrategy;
      
      try {
        // 创建策略实例
        const strategy = await this.createStrategy(StrategyClass, tenantId);
        
        // 确保策略实例有指定的方法
        if (typeof strategy[methodName] !== 'function') {
          // 如果策略没有指定的方法，尝试使用系统策略
          const systemStrategy = await this.moduleRef.resolve(SystemStrategy);
          return await systemStrategy[methodName](...args);
        }
        
        // 执行策略方法
        return await strategy[methodName](...args);
      } catch (strategyError) {
        // 如果创建策略实例失败，尝试使用系统策略
        const systemStrategy = await this.moduleRef.resolve(SystemStrategy);
        return await systemStrategy[methodName](...args);
      }
    } catch (error) {
      this.logger.error(`执行策略方法失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 创建策略实例
   * @param StrategyClass 策略类
   * @param tenantId 租户ID（可选）
   * @returns 策略实例
   */
  private async createStrategy(StrategyClass: any, tenantId?: string): Promise<any> {
    try {
      // 如果是系统策略，直接从模块引用中解析
      if (StrategyClass === SystemStrategy) {
        return await this.moduleRef.resolve(SystemStrategy);
      }
      
      // 如果是租户策略，需要特殊处理
      if (StrategyClass === TenantStrategy) {
        // 如果没有提供租户ID，可能是系统用户访问租户资源，应该使用系统策略
        if (!tenantId) {
          return await this.moduleRef.resolve(SystemStrategy);
        }
        
        try {
          // 获取租户数据库连接
          const tenantPrisma = await this.getTenantPrisma(tenantId);
          
          // 如果租户数据库连接不可用，使用系统策略
          if (!tenantPrisma) {
            return await this.moduleRef.resolve(SystemStrategy);
          }
          
          // 创建租户策略实例，并手动注入依赖
          const strategy = new TenantStrategy(tenantPrisma, ...);
          
          return strategy;
        } catch (error) {
          // 如果获取租户数据库连接失败，使用系统策略
          return await this.moduleRef.resolve(SystemStrategy);
        }
      }
      
      throw new Error(`Unknown strategy class: ${StrategyClass.name}`);
    } catch (error) {
      this.logger.error(`创建策略实例失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取租户数据库连接
   * @param tenantId 租户ID（可选）
   * @returns 租户数据库连接
   */
  private async getTenantPrisma(tenantId?: string): Promise<any> {
    try {
      // 尝试从请求中获取租户数据库连接
      const prisma = await this.moduleRef.resolve(TENANT_PRISMA_SERVICE, undefined, { strict: false });
      
      if (!prisma) {
        // 如果是系统用户访问，可以返回null，由调用者处理
        if (!tenantId) {
          return null;
        }
        
        throw new Error('租户数据库连接不可用');
      }
      
      return prisma;
    } catch (error) {
      // 如果是系统用户访问，可以返回null，由调用者处理
      if (!tenantId) {
        return null;
      }
      
      throw new Error('租户数据库连接不可用');
    }
  }
}
```

## 在服务类中使用代理

在服务类中，应该使用代理类来执行策略方法，而不是直接使用策略类：

```typescript
@Injectable()
export class Service {
  constructor(
    private readonly strategyFactory: StrategyFactory,
    private readonly strategyProxy: StrategyProxy,
  ) {}

  async findAll(queryParams: any, options: PaginationOptions, userType: string, tenantId?: string): Promise<ResultType> {
    try {
      // 使用代理执行策略方法
      return await this.strategyProxy.execute<ResultType>(
        userType,
        'findAll',
        [queryParams, options, tenantId],
        tenantId
      );
    } catch (error) {
      // 处理错误
      throw error;
    }
  }
}
```

## 在模块中注册代理

在模块的 `providers` 数组中注册代理类，确保代理类可以被正确注入到服务类中：

```typescript
@Module({
  imports: [PrismaModule, CommonModule],
  controllers: [Controller],
  providers: [
    Service,
    StrategyFactory,
    SystemStrategy,
    TenantStrategy,
    StrategyProxy,
  ],
  exports: [Service],
})
export class Module {}
```

## 最佳实践

1. **使用泛型**：在 `execute` 方法中使用泛型，以便在编译时提供类型检查。

2. **提供降级策略**：当策略实例创建失败或者策略方法不存在时，提供降级策略，例如使用系统策略。

3. **统一错误处理**：在代理类中提供统一的错误处理机制，避免在服务类中重复处理错误。

4. **日志记录**：在代理类中记录详细的日志，以便于调试和排查问题。

5. **参数验证**：在执行策略方法之前，验证参数的有效性，避免无效的参数导致错误。

6. **缓存结果**：对于频繁调用的方法，可以考虑缓存结果，以提高性能。

7. **避免硬编码**：避免在代理类中硬编码策略类型和方法名，应该使用枚举或常量。

8. **单一职责**：代理类应该只负责控制对原始对象的访问，不应该包含业务逻辑。

## 注意事项

1. **性能开销**：代理模式会引入额外的间接层，可能会影响性能。在性能敏感的场景中，应该谨慎使用。

2. **复杂性**：代理模式会增加代码的复杂性，应该在必要时使用，避免过度设计。

3. **依赖注入**：在 NestJS 中，应该尽量使用依赖注入来获取依赖，而不是手动创建实例。

4. **错误处理**：代理类应该提供统一的错误处理机制，但不应该吞掉错误，应该将错误传递给调用者。

5. **测试**：代理类应该有充分的单元测试，确保其正确性和稳定性。

## 结论

代理模式是一种强大的设计模式，可以帮助我们解决策略模式中的作用域问题和租户数据库连接问题。通过遵循本文档中的最佳实践，可以有效地使用代理模式，提高代码的可维护性和可扩展性。
