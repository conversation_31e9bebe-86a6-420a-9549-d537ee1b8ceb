# 使用和扩展指南

## 环境设置

### 1. 创建环境变量文件

在项目根目录创建 `.env` 文件：

```
PUBLIC_DATABASE_URL="postgresql://username:password@localhost:5432/mydb?schema=public"
DATABASE_URL="postgresql://username:password@localhost:5432/mydb?schema=tenant"
```

根据您的数据库配置修改连接字符串。

### 2. 安装依赖

```bash
pnpm install
```

### 3. 数据库初始化

```bash
# 初始化公共数据库
pnpm db:push:public

# 初始化租户数据库
pnpm db:push:tenant
```

### 4. 启动应用

```bash
# 开发模式
pnpm dev

# 生产模式
pnpm start
```

## API 使用

### 租户标识

所有 API 请求需要在请求头中包含 `x-tenant-code` 来标识当前租户：

```
GET /api/users
Headers:
  x-tenant-code: tenant1
```

### API 文档

启动应用后，可以通过以下地址访问 Swagger API 文档：

```
http://localhost:3000/api/docs
```

## 扩展指南

### 添加新的租户数据模型

1. 在 `prisma/schema.prisma` 文件中添加新模型：

```prisma
model Product {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())
  name      String
  price     Float
  tenantId  String   // 必须包含 tenantId 字段
}
```

2. 更新租户数据库：

```bash
pnpm db:push:tenant
```

### 创建新的模块

1. 创建模块目录结构：

```
src/modules/routes/products/
├── dto/
│   ├── create-product.dto.ts
│   └── update-product.dto.ts
├── entities/
│   └── product.entity.ts
├── products.controller.ts
├── products.module.ts
└── products.service.ts
```

2. 实现服务类：

```typescript
@Injectable()
export class ProductsService {
  constructor(
    @Inject(TENANT_PRISMA_SERVICE) private readonly prisma: TenantPrismaService,
  ) {}

  async create(createProductDto: CreateProductDto) {
    return this.prisma.product.create({
      data: createProductDto,
    });
  }

  async findAll() {
    // 查询扩展会自动添加 tenantId 过滤
    return this.prisma.product.findMany();
  }

  // 其他方法...
}
```

3. 在 `app.module.ts` 中导入新模块：

```typescript
@Module({
  imports: [
    // 其他模块...
    ProductsModule,
  ],
})
export class AppModule {}
```

### 添加新的租户

要添加新的租户，需要在公共数据库中创建租户记录：

```typescript
// 示例代码
async createTenant(tenantData) {
  return this.publicPrisma.tenant.create({
    data: {
      code: tenantData.code,
      name: tenantData.name,
      website: tenantData.website,
      datasource: {
        create: {
          name: `${tenantData.code} Database`,
          url: tenantData.databaseUrl,
        },
      },
    },
  });
}
```

### 自定义中间件

如果需要添加额外的租户相关功能，可以扩展现有中间件或创建新的中间件：

```typescript
@Injectable()
export class TenantAuthMiddleware implements NestMiddleware {
  constructor(private readonly publicPrisma: PublicPrismaService) {}

  async use(request: IRequestWithProps, response: Response, next: () => void) {
    const tenantCode = request.headers["x-tenant-code"] as string;
    const apiKey = request.headers["x-api-key"] as string;

    // 验证租户 API 密钥
    const tenant = await this.publicPrisma.tenant.findFirst({
      where: { 
        code: tenantCode,
        apiKey: apiKey 
      },
    });

    if (!tenant) {
      throw new UnauthorizedException("Invalid tenant credentials");
    }

    next();
  }
}
```

## 最佳实践

### 1. 数据隔离

- 确保所有租户数据模型都包含 `tenantId` 字段
- 使用 `TenantPrismaService` 进行所有数据库操作
- 避免直接使用原始 SQL 查询，以防绕过租户过滤

### 2. 错误处理

- 为租户相关错误创建自定义异常类
- 实现全局异常过滤器，统一处理租户错误

### 3. 性能优化

- 考虑为高流量租户使用独立数据库
- 为常用查询添加适当的索引
- 实现缓存机制，减少数据库负载

### 4. 安全性

- 实现租户级别的认证和授权
- 定期审计租户数据访问
- 考虑数据加密，特别是敏感信息
