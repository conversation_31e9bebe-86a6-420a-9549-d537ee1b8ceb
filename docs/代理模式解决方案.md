# 代理模式解决方案

## 问题背景

在多租户系统中，我们使用策略模式来处理不同用户类型（系统用户和租户用户）的业务逻辑。然而，我们遇到了以下问题：

1. **作用域问题**：策略类是默认作用域的，而 `TENANT_PRISMA_SERVICE` 是请求作用域的，导致在某些情况下无法正确获取租户数据库连接。

2. **`this` 上下文问题**：在策略类中，`this` 上下文可能会丢失，导致无法访问类的属性和方法。

3. **租户数据库连接问题**：由于作用域问题，租户数据库连接可能不可用，导致查询失败。

## 解决方案：代理模式

我们使用代理模式来解决这些问题。代理模式允许我们在不修改原有策略类的情况下，通过代理类来控制对策略类的访问，并在必要时提供额外的功能。

### 代理模式的优势

1. **保留策略模式的灵活性**：我们仍然使用策略模式来封装不同的业务逻辑，可以轻松添加新的策略。

2. **解决作用域问题**：代理负责创建正确的策略实例，不需要修改策略类的作用域。

3. **解决 `this` 上下文问题**：代理确保策略方法在正确的上下文中执行，不会出现 `this` 上下文丢失的问题。

4. **解决租户数据库连接问题**：代理负责获取租户数据库连接，确保策略实例有正确的数据库连接。

5. **统一错误处理**：代理提供统一的错误处理，简化了策略类的实现。

## 实现步骤

### 1. 创建策略代理类

为每个使用策略模式的模块创建一个代理类，例如 `RoleStrategyProxy`、`MenuStrategyProxy` 和 `DepartmentStrategyProxy`。

代理类的主要职责是：

- 根据用户类型创建正确的策略实例
- 获取租户数据库连接
- 执行策略方法
- 处理错误

代理类的核心方法是 `execute`，它接受用户类型、方法名、方法参数和租户ID，然后执行相应的策略方法。

```typescript
async execute<T>(
  userType: string,
  methodName: string,
  args: any[],
  tenantId?: string
): Promise<T> {
  try {
    // 获取正确的策略类型
    const strategyType = userType === 'SYSTEM' ? StrategyType.SYSTEM : StrategyType.TENANT;
    
    // 根据策略类型获取策略类
    const StrategyClass = strategyType === StrategyType.SYSTEM 
      ? SystemStrategy 
      : TenantStrategy;
    
    try {
      // 创建策略实例
      const strategy = await this.createStrategy(StrategyClass, tenantId);
      
      // 确保策略实例有指定的方法
      if (typeof strategy[methodName] !== 'function') {
        // 如果策略没有指定的方法，尝试使用系统策略
        const systemStrategy = await this.moduleRef.resolve(SystemStrategy);
        return await systemStrategy[methodName](...args);
      }
      
      // 执行策略方法
      return await strategy[methodName](...args);
    } catch (strategyError) {
      // 如果创建策略实例失败，尝试使用系统策略
      const systemStrategy = await this.moduleRef.resolve(SystemStrategy);
      return await systemStrategy[methodName](...args);
    }
  } catch (error) {
    this.logger.error(`执行策略方法失败: ${error.message}`);
    throw error;
  }
}
```

### 2. 修改服务类使用代理

修改服务类，使其使用代理类来执行策略方法，而不是直接使用策略类。

```typescript
// 修改前
const strategy = this.strategyFactory.getStrategyByUserType(userType);
return strategy.findAll(where, options, tenantId);

// 修改后
return await this.strategyProxy.execute<ResultType>(
  userType,
  'findAll',
  [where, options, tenantId],
  tenantId
);
```

### 3. 在模块中注册代理

在模块的 `providers` 数组中注册代理类，确保代理类可以被正确注入到服务类中。

```typescript
@Module({
  imports: [PrismaModule, CommonModule],
  controllers: [Controller],
  providers: [
    Service,
    StrategyFactory,
    SystemStrategy,
    TenantStrategy,
    StrategyProxy,
  ],
  exports: [Service],
})
export class Module {}
```

## 已实施的模块

我们已经在以下模块中实施了代理模式解决方案：

1. **角色模块**：`RoleStrategyProxy`
2. **菜单模块**：`MenuStrategyProxy`
3. **部门模块**：`DepartmentStrategyProxy`

## 代理类的实现细节

### 创建策略实例

代理类的 `createStrategy` 方法负责创建策略实例，并处理租户数据库连接问题：

```typescript
private async createStrategy(StrategyClass: any, tenantId?: string): Promise<any> {
  try {
    // 如果是系统策略，直接从模块引用中解析
    if (StrategyClass === SystemStrategy) {
      return await this.moduleRef.resolve(SystemStrategy);
    }
    
    // 如果是租户策略，需要特殊处理
    if (StrategyClass === TenantStrategy) {
      // 如果没有提供租户ID，可能是系统用户访问租户资源，应该使用系统策略
      if (!tenantId) {
        return await this.moduleRef.resolve(SystemStrategy);
      }
      
      try {
        // 获取租户数据库连接
        const tenantPrisma = await this.getTenantPrisma(tenantId);
        
        // 如果租户数据库连接不可用，使用系统策略
        if (!tenantPrisma) {
          return await this.moduleRef.resolve(SystemStrategy);
        }
        
        // 创建租户策略实例，并手动注入依赖
        const strategy = new TenantStrategy(tenantPrisma, ...);
        
        return strategy;
      } catch (error) {
        // 如果获取租户数据库连接失败，使用系统策略
        return await this.moduleRef.resolve(SystemStrategy);
      }
    }
    
    throw new Error(`Unknown strategy class: ${StrategyClass.name}`);
  } catch (error) {
    this.logger.error(`创建策略实例失败: ${error.message}`);
    throw error;
  }
}
```

### 获取租户数据库连接

代理类的 `getTenantPrisma` 方法负责获取租户数据库连接：

```typescript
private async getTenantPrisma(tenantId?: string): Promise<any> {
  try {
    // 尝试从请求中获取租户数据库连接
    const prisma = await this.moduleRef.resolve(TENANT_PRISMA_SERVICE, undefined, { strict: false });
    
    if (!prisma) {
      // 如果是系统用户访问，可以返回null，由调用者处理
      if (!tenantId) {
        return null;
      }
      
      throw new Error('租户数据库连接不可用');
    }
    
    return prisma;
  } catch (error) {
    // 如果是系统用户访问，可以返回null，由调用者处理
    if (!tenantId) {
      return null;
    }
    
    throw new Error('租户数据库连接不可用');
  }
}
```

## 结论

代理模式解决方案是一个接近"完美"的解决方案，它既解决了当前的问题，又不会牺牲策略模式的灵活性。通过代理模式，我们可以：

1. 解决策略模式中的作用域问题
2. 解决 `this` 上下文丢失的问题
3. 解决租户数据库连接问题
4. 提供统一的错误处理
5. 保持代码的可维护性和可扩展性

这个解决方案可以应用于所有使用策略模式的模块，以解决类似的问题。
