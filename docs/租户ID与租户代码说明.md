# 租户ID与租户代码说明

## 概念澄清

在多租户系统中，我们需要明确区分两个重要概念：

1. **租户ID (tenantId)**：租户在数据库中的唯一标识符，通常是一个自增的整数或UUID。用于数据库关系和内部操作。

2. **租户代码 (tenantCode)**：租户的业务标识符，通常是一个有业务含义的字符串，如 "tenant1", "company-a" 等。仅用于识别和判断租户。

## 问题描述

在当前的多租户系统实现中，存在租户ID（tenantId）和租户代码（tenantCode）的概念混淆。这种混淆可能导致以下问题：

1. 代码可读性降低，难以理解租户标识的真正含义
2. 数据库设计不一致，影响查询效率和数据完整性
3. 系统扩展性受限，难以支持更复杂的多租户场景

## 当前实现

### 数据库模型

在 `prisma/public-schema.prisma` 中，`Tenant` 模型定义如下：

```prisma
model Tenant {
  id           Int         @id @default(autoincrement())
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @default(now())
  code         String      @unique
  name         String
  website      String?
  domain       String?     @unique // 租户自定义域名，用于通过域名识别租户
  status       Int         @default(1) // 0-禁用，1-启用
  metadata     Json?
  datasource   Datasource? @relation(fields: [datasourceId], references: [id])
  datasourceId Int?
}
```

在 `Datasource` 模型的注释中提到：

```plaintext
// Multiple tenants can share the same database.
// Their data will be segragated using "tenantId" column in each table.
// This is handled using an extendion query on the Prisma client.
```

### 租户数据库服务

在 `tenant-prisma.service.ts` 中，`withQueryExtensions` 方法使用 `tenantCode` 作为 `tenantId` 的值：

```typescript
withQueryExtensions(tenantCode: string) {
  return this.$extends({
    query: {
      $allOperations({ model, operation, args, query }) {
        // 只对查询操作添加租户ID过滤
        if (operation === 'findMany' || operation === 'findFirst' || operation === 'findUnique' || operation === 'count') {
          // 添加租户ID过滤
          return query({
            ...args,
            where: {
              ...args.where,
              tenantId: tenantCode,
            },
          });
        }

        // 对于更新和删除操作，不修改查询
        return query(args);
      },
    },
  });
}
```

### 中间件实现

在 `tenant-datasource.middleware.ts` 中，将租户代码存储在 `request.tenant.tenantCode` 中：

```typescript
request.tenant = {
  tenantCode: tenantCode,
  datasourceUrl: tenant.datasource.url,
};
```

### 请求接口定义

在 `IRequestWithProps` 接口中，定义了 `tenant.tenantCode` 属性：

```typescript
tenant?: {
  /**
   * 租户代码
   */
  tenantCode?: string;

  /**
   * 数据源URL
   */
  datasourceUrl?: string;
};
```

## 问题分析

1. **概念混淆**：`tenantId` 和 `tenantCode` 在不同上下文中被混用，导致概念不清晰。

2. **数据库设计不一致**：在租户表中，使用 `id` 作为主键，`code` 作为业务标识符，但在其他表中，使用 `tenantId` 字段存储的实际上是 `code` 的值。

3. **性能影响**：使用字符串类型的 `code` 作为外键，而不是整数类型的 `id`，可能影响查询性能。

4. **维护困难**：如果需要更改租户代码，需要更新所有相关表中的外键引用。

## 正确的使用方式

根据系统设计原则，我们应该明确区分租户ID和租户代码的使用场景：

### 租户代码 (tenantCode) 的使用场景

1. **租户识别**：通过域名、请求头等方式识别租户
2. **日志和调试**：在日志中显示租户信息，便于调试
3. **用户界面**：在用户界面中显示租户信息，便于用户理解

### 租户ID (tenantId) 的使用场景

1. **数据库关系**：作为外键关联租户表
2. **内部操作**：在系统内部处理租户相关的业务逻辑
3. **权限控制**：控制用户对租户资源的访问权限

## 解决方案

### 修正当前实现

以下是修正当前实现的步骤：

**步骤1：修改 `tenant-prisma.service.ts` 中的 `withQueryExtensions` 方法，使用租户ID而不是租户代码**

```typescript
withQueryExtensions(tenantId: number) {
  return this.$extends({
    query: {
      $allOperations({ model, operation, args, query }) {
        if (operation === 'findMany' || operation === 'findFirst' || operation === 'findUnique' || operation === 'count') {
          return query({
            ...args,
            where: {
              ...args.where,
              tenantId: tenantId, // 使用数字ID
            },
          });
        }
        return query(args);
      },
    },
  });
}
```

**步骤2：修改 `tenant-datasource.middleware.ts`，同时存储租户ID和租户代码**

```typescript
request.tenant = {
  tenantId: tenant.id, // 存储数字ID，用于数据库操作
  tenantCode: tenant.code, // 存储代码，用于识别和日志
  datasourceUrl: tenant.datasource.url,
};
```

**步骤3：更新 `IRequestWithProps` 接口，同时包含 `tenantId` 和 `tenantCode` 属性**

```typescript
tenant?: {
  /**
   * 租户ID - 用于数据库关系和内部操作
   */
  tenantId?: number;

  /**
   * 租户代码 - 用于识别租户和日志
   */
  tenantCode?: string;

  /**
   * 数据源URL
   */
  datasourceUrl?: string;
};
```

**步骤4：在代码中明确区分 `tenantId` 和 `tenantCode` 的使用**

- 使用 `tenantId` 进行数据库操作和内部逻辑处理
- 使用 `tenantCode` 进行租户识别和日志记录

## 建议实施步骤

1. **审核代码**：全面审核代码，找出所有使用 `tenantId` 和 `tenantCode` 的地方。

2. **制定迁移计划**：根据审核结果，制定详细的迁移计划，包括数据库修改和代码更新。

3. **更新文档**：更新系统文档，明确租户ID和租户代码的概念和用法。

4. **分阶段实施**：分阶段实施迁移计划，每个阶段后进行充分测试。

5. **监控和验证**：实施后密切监控系统，确保所有功能正常工作。

## 结论

解决租户ID和租户代码混淆问题，对于提高系统的可维护性和可扩展性至关重要。建议采用方案一，统一使用租户ID作为租户标识，这样可以保持与数据库设计的一致性，并提高查询性能。
