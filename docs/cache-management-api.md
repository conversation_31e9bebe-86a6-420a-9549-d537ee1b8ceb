# 缓存管理 API 文档

本文档详细说明了系统中的缓存管理 API，包括缓存监控、缓存清理和缓存指标查询等功能。

## 基本信息

- **基础路径**: `/api/system/cache`
- **权限要求**: 需要管理员权限
- **认证方式**: JWT 认证

## API 端点

### 1. 获取缓存指标

获取当前系统的缓存使用情况和性能指标。

- **URL**: `/api/system/cache/metrics`
- **方法**: `GET`
- **权限**: 管理员
- **描述**: 返回缓存命中率、命中次数、未命中次数和错误次数等指标

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "hits": 1250,
    "misses": 320,
    "errors": 5,
    "hitRate": 79.62,
    "lastResetTime": "2025-05-16T22:08:32.000Z",
    "uptime": 3600
  }
}
```

### 2. 清空所有缓存

清空系统中的所有缓存数据。

- **URL**: `/api/system/cache/clear`
- **方法**: `DELETE`
- **权限**: 管理员
- **描述**: 清空所有缓存，包括 Redis 中的数据（如果启用）

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "success": true,
    "message": "所有缓存已清空"
  }
}
```

### 3. 删除指定键的缓存

删除特定键的缓存数据。

- **URL**: `/api/system/cache/key/{key}`
- **方法**: `DELETE`
- **权限**: 管理员
- **参数**:
  - `key` (路径参数): 要删除的缓存键

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "success": true,
    "message": "缓存 user:1 已删除"
  }
}
```

### 4. 使用模式删除多个缓存

使用模式匹配删除多个缓存数据。

- **URL**: `/api/system/cache/pattern`
- **方法**: `DELETE`
- **权限**: 管理员
- **参数**:
  - `pattern` (查询参数): 缓存键模式，如 `menu:*`

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "success": true,
    "message": "已删除 15 个缓存项",
    "count": 15
  }
}
```

### 5. 清除所有菜单缓存

清除所有与菜单相关的缓存数据。

- **URL**: `/api/system/cache/menu`
- **方法**: `DELETE`
- **权限**: 管理员
- **描述**: 清除所有以 `menu:` 开头的缓存键

**响应示例**:

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "success": true,
    "message": "已删除 8 个菜单缓存",
    "count": 8
  }
}
```

### 6. 缓存监控面板

提供可视化的缓存监控界面。

- **URL**: `/api/system/cache/dashboard`
- **方法**: `GET`
- **权限**: 管理员
- **描述**: 返回 HTML 页面，显示缓存指标和管理界面

**响应**: HTML 页面

## 缓存监控面板功能

缓存监控面板提供以下功能：

1. **实时指标显示**:
   - 缓存命中率
   - 命中次数
   - 未命中次数
   - 错误次数

2. **性能图表**:
   - 命中率趋势图
   - 自动每 5 秒更新一次

3. **缓存管理操作**:
   - 清空所有缓存
   - 清空菜单缓存
   - 模式删除缓存
   - 删除指定键的缓存

4. **操作日志**:
   - 记录所有缓存操作
   - 显示操作时间和结果

## 缓存策略说明

系统使用了以下缓存策略：

1. **缓存键生成**:
   - 菜单缓存键格式: `menu:{userType}:{tenantId}:{tenantCode}:{userId}`
   - 使用精确的用户信息生成唯一的缓存键

2. **缓存时间**:
   - 菜单缓存: 30 分钟
   - 其他缓存: 根据具体业务设置

3. **缓存刷新策略**:
   - 支持 staleWhileRevalidate 模式
   - 在缓存即将过期时，在后台刷新缓存

4. **缓存清除策略**:
   - 支持精确删除单个缓存
   - 支持模式删除多个缓存
   - 支持清除所有缓存

## 使用示例

### 使用 curl 获取缓存指标

```bash
curl -X GET "http://localhost:5666/api/system/cache/metrics" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 使用 curl 清除菜单缓存

```bash
curl -X DELETE "http://localhost:5666/api/system/cache/menu" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 使用 curl 模式删除缓存

```bash
curl -X DELETE "http://localhost:5666/api/system/cache/pattern?pattern=user:*" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 注意事项

1. 所有缓存管理 API 都需要管理员权限
2. 清除缓存操作可能会暂时影响系统性能
3. 建议在非高峰期执行大规模缓存清除操作
4. 缓存监控面板仅供管理员使用，不应对外暴露
