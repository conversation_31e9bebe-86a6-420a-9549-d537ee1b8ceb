# 代码规范

本文档定义了项目的代码规范和最佳实践。

## 基本原则

1. **可读性优先**：代码应该易于阅读和理解。
2. **一致性**：保持代码风格的一致性。
3. **简洁性**：代码应尽量简洁，避免不必要的复杂性。
4. **命名清晰**：使用有意义的命名，反映变量、函数或类的用途。
5. **单一职责**：每个函数、类或模块应该只有一个职责。

## TypeScript 规范

### 文件命名

- 使用 kebab-case（小写字母，单词之间用连字符分隔）
- 每个文件应该只包含一个概念
- 根据 Nest.js 的约定，文件名应该反映其内容类型：
  - `user.controller.ts`
  - `user.service.ts`
  - `user.module.ts`
  - `user.entity.ts`
  - `user.dto.ts`
  - `user.interface.ts`

### 类命名

- 使用 PascalCase（每个单词首字母大写）
- 控制器类以 `Controller` 结尾
- 服务类以 `Service` 结尾
- 模块类以 `Module` 结尾
- 实体类反映其表示的数据模型，例如 `User`
- DTO 类应以 `Dto` 结尾，例如 `CreateUserDto`

示例：

```typescript
export class UserController {}
export class AuthService {}
export class AppModule {}
export class User {}
export class CreateUserDto {}
```

### 变量命名

- 使用 camelCase（第一个单词首字母小写，其余单词首字母大写）
- 避免使用单字母变量名，除了循环中的迭代器
- 布尔变量应该使用 `is`、`has` 或 `should` 前缀

示例：

```typescript
const firstName = 'John';
const isActive = true;
const hasPermission = false;
```

### 常量命名

- 使用全大写字母，单词之间用下划线分隔
- 全局常量放在专门的文件中

示例：

```typescript
const MAX_RETRY_COUNT = 3;
const API_BASE_URL = 'https://api.example.com';
```

### 函数命名

- 使用 camelCase
- 使用动词前缀，如 `get`、`set`、`update`、`create`、`delete` 等
- 函数名应该清晰地表达其目的

示例：

```typescript
function getUserById(id: string): User {}
function validateEmail(email: string): boolean {}
function createAuthToken(user: User): string {}
```

### 接口命名

- 使用 PascalCase
- 不要使用 `I` 前缀

示例：

```typescript
interface User {
  id: string;
  name: string;
}

interface AuthenticationResult {
  token: string;
  expiresIn: number;
}
```

### 类型定义

- 尽可能使用 TypeScript 的类型系统
- 避免使用 `any` 类型
- 为函数定义返回类型
- 使用接口定义对象结构

示例：

```typescript
function findUser(id: string): User | null {
  // ...
}

function processData<T>(data: T[]): ProcessedResult<T> {
  // ...
}
```

## 代码格式

项目使用 Prettier 和 ESLint 来强制执行代码格式规范。

### 缩进

- 使用 2 个空格缩进
- 不要使用制表符

### 括号

- 控制结构的开括号放在同一行
- 始终使用花括号，即使只有一行代码

示例：

```typescript
if (condition) {
  doSomething();
}
```

### 空格

- 运算符前后各放一个空格
- 逗号后放一个空格
- 冒号后放一个空格（对象定义中）

示例：

```typescript
const x = a + b;
const arr = [1, 2, 3];
const obj = { key: value };
```

### 行长度

- 每行代码不超过 100 个字符
- 如果超过，使用适当的换行

### 注释

- 使用 JSDoc 格式为函数和类添加注释
- 注释应该解释"为什么"，而不仅仅是"是什么"
- 保持注释的更新，不要保留过时的注释

示例：

```typescript
/**
 * 验证用户并生成认证令牌
 * @param username 用户名
 * @param password 密码
 * @returns 包含令牌和过期时间的认证结果
 * @throws UnauthorizedException 如果认证失败
 */
async function authenticate(username: string, password: string): Promise<AuthResult> {
  // ...
}
```

## 异常处理

- 使用 Nest.js 提供的异常类
- 不要捕获异常后不处理
- 避免使用一般的 `try/catch` 捕获所有异常
- 在服务层处理业务逻辑异常，控制器只处理 HTTP 相关异常

示例：

```typescript
if (!user) {
  throw new NotFoundException('User not found');
}

if (!isValid) {
  throw new BadRequestException('Invalid input data');
}
```

## 项目结构

遵循 Nest.js 推荐的模块化结构：

```
src/
├── main.ts                # 应用程序入口点
├── app.module.ts          # 根模块
├── core/                  # 核心功能
│   ├── config/            # 应用程序配置
│   ├── guards/            # 全局守卫
│   ├── interceptors/      # 全局拦截器
│   └── filters/           # 全局过滤器
├── modules/               # 功能模块
│   ├── users/             # 用户模块
│   │   ├── dto/           # 数据传输对象
│   │   ├── entities/      # 实体定义
│   │   ├── user.controller.ts
│   │   ├── user.service.ts
│   │   └── user.module.ts
│   └── auth/              # 认证模块
│       ├── dto/
│       ├── guards/
│       ├── strategies/
│       ├── auth.controller.ts
│       ├── auth.service.ts
│       └── auth.module.ts
└── shared/                # 共享功能
    ├── constants/         # 常量
    ├── interfaces/        # 接口
    └── utils/             # 实用工具函数
```

## 导入顺序

导入语句应按以下顺序组织：

1. Node.js 内置模块
2. 外部依赖（npm 包）
3. 应用程序内部导入（相对路径）

每组之间应有一个空行，并且每组内部按字母顺序排序。

示例：

```typescript
import * as fs from 'fs';
import * as path from 'path';

import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { User } from '../entities/user.entity';
import { UserRepository } from '../repositories/user.repository';
```

## 最佳实践

1. **依赖注入**：使用 Nest.js 的依赖注入机制，而不是直接实例化依赖。
2. **配置管理**：使用 ConfigService 管理配置，避免硬编码值。
3. **环境变量**：使用环境变量存储敏感信息和环境特定配置。
4. **异步处理**：优先使用 async/await 而不是回调或原始 Promise。
5. **数据验证**：使用 DTO 和类验证器进行输入验证。
6. **日志记录**：使用日志服务而不是 console.log。
7. **错误处理**：集中处理错误并提供有意义的错误消息。

## CI/CD 流程

项目使用以下工具确保代码质量：

1. **ESLint**：静态代码分析
2. **Prettier**：代码格式化
3. **Jest**：单元测试和集成测试
4. **Husky**：Git hooks 来自动化代码验证
5. **lint-staged**：只对暂存的文件运行 linters
