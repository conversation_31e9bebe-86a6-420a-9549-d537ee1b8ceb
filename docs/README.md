# Multi-tenant NestJS 项目文档

欢迎使用 Multi-tenant NestJS 项目文档。本文档提供了关于项目架构、代码结构和使用方法的详细信息。

## 文档目录

1. [项目概述](./项目概述.md) - 项目的基本介绍和功能概述
2. [代码结构](./代码结构.md) - 项目的目录结构和主要文件说明
3. [多租户架构](./多租户架构.md) - 多租户架构的详细说明和实现方式
4. [使用和扩展指南](./使用和扩展指南.md) - 如何使用和扩展项目的指南

## 快速开始

### 环境准备

1. Node.js (>= 14.x)
2. PostgreSQL 数据库
3. pnpm 包管理器

### 安装和运行

1. 克隆项目
2. 创建 `.env` 文件并配置数据库连接
3. 安装依赖：`pnpm install`
4. 初始化数据库：`pnpm db:push:public` 和 `pnpm db:push:tenant`
5. 启动应用：`pnpm dev`

详细的安装和使用说明请参考 [使用和扩展指南](./使用和扩展指南.md)。

## 项目架构图

### 请求-响应生命周期

```
客户端 -> API 请求(带x-tenant-code) -> 中间件处理 -> 
查找租户数据源 -> 创建租户特定的 Prisma 实例 -> 
应用查询扩展(自动过滤tenantId) -> 控制器处理请求 -> 返回响应
```

### 数据模型关系

```
公共数据库:
  Tenant <-> Datasource (一对一关系)

租户数据库:
  所有实体都包含 tenantId 字段，用于数据隔离
```

## 贡献指南

欢迎对本项目进行贡献！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支：`git checkout -b feature/your-feature`
3. 提交更改：`git commit -m 'Add some feature'`
4. 推送到分支：`git push origin feature/your-feature`
5. 提交 Pull Request

## 许可证

本项目采用 MIT 许可证。详情请参阅 [LICENSE](../LICENSE) 文件。
