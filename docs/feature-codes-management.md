# 功能代码管理指南

功能代码是系统的基础配置数据，对于系统的正常运行至关重要。本文档提供了功能代码数据的管理指南，包括备份、恢复和维护。

## 功能代码的重要性

功能代码具有以下特点：

1. **系统基础配置**：功能代码定义了系统中所有可用的功能，是功能权限控制的基础
2. **跨表关联**：多个表（如租户功能、功能使用记录等）都依赖于功能代码表
3. **业务逻辑依赖**：代码中的业务逻辑依赖于这些功能代码的存在和正确性

因此，在数据库初始化或迁移时，确保功能代码数据被正确保存和恢复是非常必要的。

## 功能代码数据管理工具

系统提供了以下工具来管理功能代码数据：

### 1. 种子文件

功能代码的初始数据定义在 `prisma/seeds/feature-codes.js` 文件中。这个文件会在数据库初始化时被执行，创建基本的功能代码数据。

```bash
# 执行种子脚本，初始化数据库
pnpm db:seed
```

### 2. 导出工具

导出工具可以从数据库中导出当前的功能代码数据，并生成更新的种子文件。

```bash
# 导出功能代码数据
pnpm db:export-feature-codes
```

这个命令会：
- 从数据库中读取所有功能代码数据
- 更新 `prisma/seeds/feature-codes.js` 文件
- 在 `backups` 目录下创建一个带时间戳的备份文件

### 3. 导入工具

导入工具可以从备份文件中恢复功能代码数据。

```bash
# 从备份文件导入功能代码数据
pnpm db:import-feature-codes ./backups/feature-codes-2025-05-16T12-00-00-000Z.json
```

## 功能代码管理最佳实践

### 1. 添加新功能代码

当需要添加新的功能代码时，建议按照以下步骤操作：

1. 先在开发环境中通过 API 或直接在数据库中添加功能代码
2. 使用导出工具导出最新的功能代码数据：`pnpm db:export-feature-codes`
3. 检查更新后的 `prisma/seeds/feature-codes.js` 文件，确保新功能代码已正确添加
4. 提交更新后的种子文件到版本控制系统

### 2. 定期备份

建议定期执行导出命令，创建功能代码数据的备份：

```bash
# 每周或每次重要更新后执行
pnpm db:export-feature-codes
```

### 3. 环境迁移

当需要将功能代码数据从一个环境迁移到另一个环境时：

1. 在源环境中导出功能代码数据：`pnpm db:export-feature-codes`
2. 将生成的备份文件复制到目标环境
3. 在目标环境中导入功能代码数据：`pnpm db:import-feature-codes ./backups/feature-codes-xxx.json`

### 4. 数据库初始化

在新环境中初始化数据库时，确保执行种子脚本：

```bash
# 初始化数据库
pnpm db:reset
```

这将执行 `seed.js` 文件，初始化所有表的数据，包括功能代码。

## 功能代码结构

每个功能代码包含以下字段：

- **code**：功能代码，唯一标识，如 `ai.ppt`
- **name**：功能名称，如 `AI PPT生成`
- **description**：功能描述，如 `AI PPT生成功能`
- **module**：所属模块，如 `AI模块`
- **isActive**：是否激活
- **sortOrder**：排序顺序
- **metadata**：额外元数据，如图标、颜色、所需权限等

## 注意事项

1. 功能代码一旦创建并被租户使用，不建议删除，可以通过设置 `isActive` 为 `false` 来禁用
2. 功能代码的 `code` 字段是唯一标识，建议使用 `[模块].[子功能]` 的格式，如 `ai.ppt`
3. 修改已有功能代码的 `code` 字段会导致关联数据失效，应该避免这种操作
4. 在生产环境中修改功能代码数据前，务必先创建备份
