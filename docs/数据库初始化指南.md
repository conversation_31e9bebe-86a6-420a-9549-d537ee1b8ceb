<!--
 * @Author: ya<PERSON><PERSON>qi<PERSON> <EMAIL>
 * @Date: 2025-05-06 18:06:30
 * @LastEditors: yangwenqiang <EMAIL>
 * @LastEditTime: 2025-05-06 18:11:29
 * @FilePath: /multi-tenant-nestjs/docs/数据库初始化指南.md
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
# 数据库初始化指南

本文档提供了初始化多租户系统数据库的步骤和说明。

## 前提条件

1. 已安装 PostgreSQL 数据库
2. 已创建数据库用户和密码
3. 已创建名为 `multi_tenant` 的数据库

## 配置数据库连接

1. 打开项目根目录下的 `.env` 文件
2. 修改以下配置项，使用您的实际数据库连接信息：

```
PUBLIC_DATABASE_URL="postgresql://用户名:密码@主机:端口/multi_tenant?schema=public"
DATABASE_URL="postgresql://用户名:密码@主机:端口/multi_tenant?schema=tenant"
```

例如：

```
PUBLIC_DATABASE_URL="postgresql://postgres:password@localhost:5432/multi_tenant?schema=public"
DATABASE_URL="postgresql://postgres:password@localhost:5432/multi_tenant?schema=tenant"
```

## 初始化数据库

### 方法一：使用脚本自动初始化（推荐）

运行以下命令，自动创建表结构：

```bash
pnpm db:create-schema
```

这个命令会执行以下操作：
1. 创建公共数据库表结构
2. 创建租户数据库表结构
3. 检查数据库连接

然后，使用 SQL 脚本添加初始数据：

```bash
psql -U postgres -d multi_tenant -f scripts/init-data.sql
```

### 方法二：完全手动初始化

如果自动初始化失败，可以按照以下步骤手动初始化：

#### 1. 创建表结构

```bash
# 创建公共数据库表结构
pnpm db:push:public

# 创建租户数据库表结构
pnpm db:push:tenant
```

#### 2. 添加初始数据

您可以使用项目中提供的 SQL 脚本添加初始数据：

```bash
psql -U postgres -d multi_tenant -f scripts/init-data.sql
```

或者，您可以手动执行 SQL 语句添加初始数据。详细的 SQL 语句可以在 `scripts/init-data.sql` 文件中找到。

## 验证初始化

初始化完成后，您可以使用以下凭据登录系统：

### 系统管理员
- 用户名：admin
- 密码：admin123

### 租户管理员
- 用户名：tenant_admin
- 密码：admin123
- 租户代码：tenant1

## 常见问题

### 1. 数据库连接失败

检查 `.env` 文件中的数据库连接信息是否正确，包括用户名、密码、主机和端口。

### 2. 表已存在错误

如果遇到表已存在的错误，可以先删除数据库中的表，然后重新运行初始化命令。

### 3. 初始化脚本执行失败

如果初始化脚本执行失败，请检查错误信息，并根据错误信息修复问题。如果问题无法解决，可以尝试手动初始化。
